#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源泄漏修复测试脚本
"""
import asyncio
import platform
import time

print(f"操作系统: {platform.system()}")
print(f"Python 版本: {platform.python_version()}")

async def test_basic_async_operations():
    """测试基本异步操作"""
    print("\n=== 测试基本异步操作 ===")
    
    # 测试 asyncio.gather
    async def simple_task(n):
        await asyncio.sleep(0.01)
        return f"任务 {n} 完成"
    
    tasks = [simple_task(i) for i in range(5)]
    results = await asyncio.gather(*tasks)
    print(f"asyncio.gather 测试: {len(results)} 个任务完成")
    
    # 测试信号量
    semaphore = asyncio.Semaphore(2)
    
    async def limited_task(n):
        async with semaphore:
            await asyncio.sleep(0.01)
            return f"限制任务 {n} 完成"
    
    tasks = [limited_task(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    print(f"信号量测试: {len(results)} 个任务完成")

async def test_http_client():
    """测试 HTTP 客户端"""
    print("\n=== 测试 HTTP 客户端 ===")
    
    try:
        import aiohttp
        
        # 测试正确的 session 管理
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get('https://httpbin.org/get', timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        print("HTTP 客户端测试成功")
                    else:
                        print(f"HTTP 响应状态: {response.status}")
            except asyncio.TimeoutError:
                print("HTTP 请求超时（正常，可能是网络问题）")
            except Exception as e:
                print(f"HTTP 请求失败: {str(e)}")
                
    except ImportError:
        print("aiohttp 未安装，跳过 HTTP 客户端测试")

async def test_multiple_sessions():
    """测试多个会话的创建和销毁"""
    print("\n=== 测试多个会话创建和销毁 ===")
    
    try:
        import aiohttp
        
        # 创建多个会话并确保正确关闭
        sessions = []
        for i in range(3):
            session = aiohttp.ClientSession()
            sessions.append(session)
            print(f"创建会话 {i+1}")
        
        # 正确关闭所有会话
        for i, session in enumerate(sessions):
            await session.close()
            print(f"关闭会话 {i+1}")
        
        print("多会话测试完成")
        
    except ImportError:
        print("aiohttp 未安装，跳过多会话测试")

async def test_concurrent_operations():
    """测试并发操作"""
    print("\n=== 测试并发操作 ===")
    
    async def worker(worker_id):
        """工作任务"""
        await asyncio.sleep(0.1)
        return f"工作者 {worker_id} 完成"
    
    # 创建多个并发任务
    tasks = [worker(i) for i in range(5)]
    
    # 使用 gather 执行
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()
    
    successful = sum(1 for r in results if not isinstance(r, Exception))
    print(f"并发测试: {successful}/{len(tasks)} 个任务成功，耗时: {(end_time - start_time)*1000:.2f}ms")

async def test_resource_cleanup():
    """测试资源清理"""
    print("\n=== 测试资源清理 ===")
    
    # 创建一些资源并确保清理
    resources = []
    
    try:
        # 模拟创建资源
        for i in range(3):
            # 这里可以是任何需要清理的资源
            resource = {"id": i, "data": f"资源 {i}"}
            resources.append(resource)
            print(f"创建资源 {i}")
        
        # 模拟使用资源
        await asyncio.sleep(0.1)
        
    finally:
        # 确保资源被清理
        for i, resource in enumerate(resources):
            print(f"清理资源 {i}: {resource['id']}")
        resources.clear()
    
    print("资源清理测试完成")

async def main():
    """主测试函数"""
    print("开始资源泄漏修复验证测试...")
    print(f"当前平台: {platform.system()}")
    
    if platform.system() == 'Darwin':
        print("检测到 macOS 系统，将使用优化的资源管理策略")
    
    try:
        await test_basic_async_operations()
        await test_http_client()
        await test_multiple_sessions()
        await test_concurrent_operations()
        await test_resource_cleanup()
        
        print("\n🎉 所有测试完成！")
        print("如果程序正常退出且没有资源泄漏警告，说明修复有效。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        raise
    
    # 等待确保所有异步操作完成
    await asyncio.sleep(0.2)
    print("等待资源清理完成...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
        print("\n✅ 程序正常退出，没有检测到资源泄漏")
    except KeyboardInterrupt:
        print("\n⚠️  程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {str(e)}")
        exit(1)
