from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, func

from ..models.import_task import ImportTask
from ..core.exceptions import NotFoundError

class ImportTaskRepository:
    """导入任务仓库类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_task(self, total_files: int) -> ImportTask:
        """
        创建导入任务
        
        Args:
            total_files: 总文件数
            
        Returns:
            创建的任务实例
        """
        task = ImportTask(total_files=total_files)
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def get_task(self, task_id: str) -> Optional[ImportTask]:
        """
        获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务实例
        """
        result = await self.db.execute(
            select(ImportTask).where(ImportTask.id == task_id)
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise NotFoundError(f"导入任务不存在: {task_id}")
            
        return task
    
    async def update_task(
        self,
        task_id: str,
        status: Optional[str] = None,
        processed_files: Optional[int] = None,
        failed_files: Optional[int] = None,
        progress: Optional[float] = None,
        error_message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> ImportTask:
        """
        更新任务
        
        Args:
            task_id: 任务ID
            status: 状态
            processed_files: 已处理文件数
            failed_files: 失败文件数
            progress: 进度
            error_message: 错误信息
            result: 结果
            
        Returns:
            更新后的任务实例
        """
        task = await self.get_task(task_id)
        
        if status is not None:
            task.status = status
        if processed_files is not None:
            task.processed_files = processed_files
        if failed_files is not None:
            task.failed_files = failed_files
        if progress is not None:
            task.progress = progress
        if error_message is not None:
            task.error_message = error_message
        if result is not None:
            task.result = result
        
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def list_tasks(
        self,
        skip: int = 0,
        limit: int = 20
    ) -> List[ImportTask]:
        """
        获取任务列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务列表
        """
        result = await self.db.execute(
            select(ImportTask)
            .order_by(desc(ImportTask.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_task_count(self) -> int:
        """
        获取任务总数
        
        Returns:
            任务总数
        """
        count_query = select(func.count()).select_from(ImportTask)
        result = await self.db.execute(count_query)
        return result.scalar() 