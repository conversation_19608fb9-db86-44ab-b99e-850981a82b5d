from typing import List, Tu<PERSON>, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, delete
from ..models.chat import ChatSession, ChatMessage, MessageReference
from ..core.exceptions import NotFoundError, DatabaseError
import uuid

class ChatRepository:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_or_create_session(self, session_id: Optional[str] = None) -> ChatSession:
        """获取或创建聊天会话"""
        try:
            if session_id:
                stmt = select(ChatSession).where(ChatSession.id == session_id)
                result = await self.db.execute(stmt)
                session = result.scalar_one_or_none()
                if session:
                    return session
            
            # 如果没有提供 session_id，生成一个新的 UUID
            new_session_id = session_id or str(uuid.uuid4())
            session = ChatSession(id=new_session_id)
            self.db.add(session)
            await self.db.commit()
            await self.db.refresh(session)
            return session
        except Exception as e:
            await self.db.rollback()
            raise DatabaseError(f"获取或创建会话失败: {str(e)}")

    async def create_message(
        self,
        session_id: str,
        role: str,
        content: str,
        references: List[dict] = None
    ) -> ChatMessage:
        """创建新消息"""
        try:
            message = ChatMessage(
                id=str(uuid.uuid4()),  # 为消息生成 UUID
                session_id=session_id,
                role=role,
                content=content
            )
            self.db.add(message)
            
            if references:
                for ref in references:
                    message_ref = MessageReference(
                        message_id=message.id,
                        title=ref.get("title", ""),
                        url=ref.get("url", ""),
                        content=ref.get("content", ""),
                        relevance=ref.get("relevance", 0.0)
                    )
                    self.db.add(message_ref)
            
            await self.db.commit()
            await self.db.refresh(message)
            return message
        except Exception as e:
            await self.db.rollback()
            raise DatabaseError(f"创建消息失败: {str(e)}")

    async def update_message_content(
        self,
        message_id: str,
        content: str
    ) -> bool:
        """更新消息内容"""
        try:
            stmt = select(ChatMessage).where(ChatMessage.id == message_id)
            result = await self.db.execute(stmt)
            message = result.scalar_one_or_none()
            
            if not message:
                raise NotFoundError("消息不存在")
            
            message.content = content
            await self.db.commit()
            return True
            
        except NotFoundError:
            raise
        except Exception as e:
            await self.db.rollback()
            raise DatabaseError(f"更新消息内容失败: {str(e)}")

    async def get_session_messages(
        self,
        session_id: str,
        page_size: int = 20,
        page_number: int = 1
    ) -> Tuple[List[ChatMessage], int]:
        """获取会话消息历史"""
        try:
            # 获取总数
            count_stmt = select(ChatMessage).where(ChatMessage.session_id == session_id)
            result = await self.db.execute(count_stmt)
            total = len(result.scalars().all())
            
            # 获取分页数据
            stmt = select(ChatMessage).where(
                ChatMessage.session_id == session_id
            ).order_by(
                desc(ChatMessage.timestamp)
            ).offset(
                (page_number - 1) * page_size
            ).limit(page_size)
            
            result = await self.db.execute(stmt)
            messages = result.scalars().all()
            
            return messages, total
        except Exception as e:
            raise DatabaseError(f"获取会话消息失败: {str(e)}")

    async def get_message_by_id(self, message_id: str) -> Optional[ChatMessage]:
        """根据ID获取消息"""
        try:
            stmt = select(ChatMessage).where(ChatMessage.id == message_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            raise DatabaseError(f"获取消息失败: {str(e)}")

    async def delete_session(self, session_id: str) -> bool:
        """删除会话及其所有消息"""
        try:
            # 检查会话是否存在
            session_stmt = select(ChatSession).where(ChatSession.id == session_id)
            session_result = await self.db.execute(session_stmt)
            session = session_result.scalar_one_or_none()
            if not session:
                raise NotFoundError("会话不存在")
            
            # 获取所有相关消息
            messages_stmt = select(ChatMessage).where(ChatMessage.session_id == session_id)
            messages_result = await self.db.execute(messages_stmt)
            messages = messages_result.scalars().all()
            
            # 删除所有相关的消息引用
            for message in messages:
                ref_stmt = delete(MessageReference).where(MessageReference.message_id == message.id)
                await self.db.execute(ref_stmt)
            
            # 删除所有消息
            msg_stmt = delete(ChatMessage).where(ChatMessage.session_id == session_id)
            await self.db.execute(msg_stmt)
            
            # 删除会话
            self.db.delete(session)
            await self.db.commit()
            return True
        except NotFoundError:
            raise
        except Exception as e:
            await self.db.rollback()
            raise DatabaseError(f"删除会话失败: {str(e)}") 