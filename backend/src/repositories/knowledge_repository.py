import traceback
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, func
from sqlalchemy.orm import selectinload
import asyncio
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import time

from ..core.constants import KnowledgeDomain
from ..core.logging import get_module_logger
from ..core.content_enhancer import ContentFormater
from ..core.exceptions import NotFoundError, DatabaseError
from ..core.vector_store import VectorStore
from ..models.knowledge import Knowledge, KnowledgeChunk
from ..core.embeddings import get_embedding_model
from ..core.config import settings
from ..core.elasticsearch import ElasticSearch
from ..core.id_generator import IDGenerator

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class KnowledgeRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.vector_store = VectorStore(settings.VECTOR_DB_PATH)
        self.elastic_search = ElasticSearch(settings.ELASTICSEARCH["INDEX_NAME"])
        self.embedding_model = get_embedding_model()
        self._executor = ThreadPoolExecutor(max_workers=4)

    async def _encode_text(self, text: str) -> List[float]:
        """异步编码文本"""
        loop = asyncio.get_event_loop()
        vector = await loop.run_in_executor(
            self._executor,
            self.embedding_model.encode,
            text
        )
        # 确保返回的是标准Python列表而不是NumPy数组
        if hasattr(vector, 'tolist'):
            return vector.tolist()
        elif isinstance(vector, np.ndarray):
            return vector.tolist()
        elif isinstance(vector, list):
            return vector
        else:
            # 处理其他可能的类型
            logger.warning(f"未知的向量类型: {type(vector)}")
            return list(vector) if hasattr(vector, '__iter__') else []
        
    async def add_knowledge(
        self,
        title: str,
        content: str,
        source_type: str = "manual",
        url: Optional[str] = None,
        metadata: Optional[dict] = None,
        chunks: Optional[List[Dict[str, Any]]] = None,
        vector: Optional[List[float]] = None,
        domain: Optional[str] = KnowledgeDomain.DEFAULT,
        id: Optional[str] = None,
    ) -> str:
        """添加知识条目"""
        try:
            # 1. 如果没有提供向量，使用嵌入模型生成
            if (vector is None or (hasattr(vector, 'size') and vector.size == 0)) and content:
                content_for_vector = ContentFormater.get_enhanced_document_content(title, content, metadata)
                vector = await self._encode_text(content_for_vector)
                
            # 确保向量是有效的
            vector_id = None
            if vector is not None:
                # 如果是NumPy数组，转换为列表
                if hasattr(vector, 'tolist'):
                    vector = vector.tolist()
                
                # 检查向量是否为空列表
                if isinstance(vector, list) and len(vector) > 0:
                    # 2. 添加主向量到向量存储
                    vector_metadata = {
                        "title": title,
                        "url": url,
                        "type": "main"
                    }
                    vector_ids = await self.vector_store.add_vectors([vector], [vector_metadata], domain)
                    vector_id = vector_ids[0]
                else:
                    logger.warning(f"无法为知识条目生成有效向量: {title}, 向量为空")
            else:
                logger.warning(f"无法为知识条目生成有效向量: {title}, 向量为None")

            # 3. 创建知识条目
            knowledge_id = id if id else IDGenerator.generate_knowledge_id()
            knowledge = Knowledge(
                id=knowledge_id,
                title=title,
                content=content,
                url=url,
                meta_info=metadata or {},
                embedding_id=vector_id,
                source_type=source_type,
                domain=domain
            )
            self.db.add(knowledge)
            await self.db.flush()
            
            # 4. 处理知识片段
            chunk_objects = []
            if chunks:
                for chunk_index, chunk in enumerate(chunks):
                    chunk_content = chunk.get("content", "")
                    chunk_vector = chunk.get("vector", None)
                    
                    # 如果没有提供向量，使用嵌入模型生成
                    if (chunk_vector is None or (hasattr(chunk_vector, 'size') and chunk_vector.size == 0)) and chunk_content:
                        chunk_content_for_vector = ContentFormater.get_embedding_content(chunk)
                        logger.debug(f"增强知识块内容: {chunk_content_for_vector}")
                        chunk_vector = await self._encode_text(chunk_content_for_vector)
                    
                    # 确保向量是有效的
                    chunk_vector_id = None
                    if chunk_vector is not None:
                        # 如果是NumPy数组，转换为列表
                        if hasattr(chunk_vector, 'tolist'):
                            chunk_vector = chunk_vector.tolist()
                        
                        # 检查向量是否为空列表
                        if isinstance(chunk_vector, list) and len(chunk_vector) > 0:
                            # 添加片段向量到向量存储
                            chunk_metadata = {
                                "title": title,
                                "url": url,
                                "type": "chunk",
                                "content": chunk_content[:200]  # 存储前200个字符作为预览
                            }
                            chunk_vector_ids = await self.vector_store.add_vectors(
                                [chunk_vector],
                                [chunk_metadata],
                                domain=domain
                            )
                            chunk_vector_id = chunk_vector_ids[0]
                        else:
                            logger.warning(f"无法为知识块生成有效向量: {chunk_content[:50]}..., 向量为空")
                    else:
                        logger.warning(f"无法为知识块生成有效向量: {chunk_content[:50]}..., 向量为None")

                    # 创建知识片段记录
                    chunk_id = chunk.get("id") if chunk.get("id") else IDGenerator.generate_chunk_id(knowledge.id, chunk_index)
                    knowledge_chunk = KnowledgeChunk(
                        id=chunk_id,
                        knowledge_id=knowledge.id,
                        content=chunk_content,
                        meta_info=chunk.get("metadata", {}),
                        embedding_id=chunk_vector_id
                    )
                    self.db.add(knowledge_chunk)
                    await self.db.flush()
                    chunk_objects.append(knowledge_chunk)
                    
            # 5. 提交数据库事务
            await self.db.commit()
            
            # 6. 将知识条目索引到Elasticsearch
            try:
                await self.elastic_search.index_knowledge_and_chunks(knowledge, chunk_objects)
            except Exception as es_error:
                logger.warning(f"索引知识到Elasticsearch失败: ID={knowledge.id}, error={str(es_error)}")
                # 不抛出异常，因为数据库操作已经成功

            return knowledge.id

        except Exception as e:
            await self.db.rollback()
            logger.error(f"添加知识失败: {str(e)}")
            raise DatabaseError(f"添加知识失败: {str(e)}")

    async def add_knowledge_from_documents(self, documents: List[Dict[str, Any]]) -> List[str]:
        """从文档列表添加知识"""
        knowledge_ids = []
        
        try:
            async with self.db.begin() as transaction:
                for doc in documents:
                    title = doc.get("title", "")
                    content = doc.get("content", "")
                    url = doc.get("url", "")
                    source_type = doc.get("source_type", "crawl")
                    domain = doc.get("domain", KnowledgeDomain.DEFAULT)
                    metadata = doc.get("metadata", {})
                    chunks = doc.get("chunks", [])
                    
                    # 添加知识
                    knowledge_id = await self.add_knowledge(
                        title=title,
                        content=content,
                        source_type=source_type,
                        url=url,
                        domain=domain,
                        metadata=metadata,
                        chunks=chunks
                    )
                    
                    knowledge_ids.append(knowledge_id)
                
                await transaction.commit()
                return knowledge_ids
            
        except Exception as e:
            logger.error(f"批量添加知识失败: {str(e)}")
            raise DatabaseError(f"批量添加知识失败: {str(e)}")

    async def search_relevant_content(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """搜索相关内容"""
        start_time = time.time()
        try:
            # 1. 将查询文本转换为向量
            encode_start_time = time.time()
            query_vector = await self._encode_text(query)
            encode_end_time = time.time()
            
            logger.info(f"性能监控[KnowledgeRepository]: 查询编码耗时: {(encode_end_time - encode_start_time)*1000:.2f}ms")
            
            # 2. 使用向量搜索相似内容
            search_result = await self.search_by_vector(
                query_vector=query_vector,
                limit=limit,
                min_score=min_score,
                domains=domains
            )
            
            end_time = time.time()
            logger.info(f"性能监控[KnowledgeRepository]: 搜索相关内容完成, 结果数: {len(search_result)}, 总耗时: {(end_time - start_time)*1000:.2f}ms")
            
            return search_result
            
        except Exception as e:
            end_time = time.time()
            logger.error(f"搜索知识失败: {str(e)}")
            logger.info(f"性能监控[KnowledgeRepository]: 搜索相关内容失败, 耗时: {(end_time - start_time)*1000:.2f}ms")
            raise DatabaseError(f"搜索知识失败: {str(e)}")
    
    async def _get_knowledges_and_chunks(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取知识和片段
        
        Args:
            search_results: 搜索结果，每项包含id、type、score、domain
        Returns:
            results: 知识和片段
        Raises:
            DatabaseError: 数据库查询失败
        """
        results = []
        seen_knowledge_ids = set()

        for item in search_results:
            # 判断ID类型来确定查询方式
            # 如果id是纯数字，说明是向量检索的embedding_id
            # 如果id是字符串（如k_xxx），说明是BM25检索的knowledge.id或chunk.id
            item_id = item["id"]
            is_embedding_id = str(item_id).isdigit()
            
            if item.get("type") == "main":
                # 主知识条目
                if is_embedding_id:
                    # 向量检索：使用embedding_id查询
                    stmt = select(Knowledge).where(Knowledge.embedding_id == str(item_id))
                else:
                    # BM25检索：使用knowledge.id查询
                    stmt = select(Knowledge).where(Knowledge.id == str(item_id))
                    
                result = await self.db.execute(stmt)
                knowledge = result.scalars().first()
                logger.debug(f"主知识条目: {knowledge}, ID类型: {'embedding_id' if is_embedding_id else 'knowledge.id'}")

                if knowledge and knowledge.id not in seen_knowledge_ids:
                    results.append({
                        "id": knowledge.id,
                        "title": knowledge.title,
                        "content": knowledge.content,
                        "url": knowledge.url,
                        "relevance": item["score"],
                        "type": "document",
                        "domain": knowledge.domain,
                        "metadata": knowledge.meta_info if isinstance(knowledge.meta_info, dict) else {}
                    })
                    seen_knowledge_ids.add(knowledge.id)
            else:
                # 知识片段
                if is_embedding_id:
                    # 向量检索：使用embedding_id查询
                    stmt = select(KnowledgeChunk).where(KnowledgeChunk.embedding_id == str(item_id))
                else:
                    # BM25检索：使用chunk.id查询
                    stmt = select(KnowledgeChunk).where(KnowledgeChunk.id == str(item_id))
                    
                result = await self.db.execute(stmt)
                chunk = result.scalars().first()

                if chunk:
                    # 获取所属的知识条目
                    stmt = select(Knowledge).where(Knowledge.id == chunk.knowledge_id)
                    result = await self.db.execute(stmt)
                    knowledge = result.scalars().first()

                    results.append({
                        "id": chunk.id,
                        "knowledge_id": chunk.knowledge_id,
                        "title": knowledge.title if knowledge else "",
                        "content": chunk.content,
                        "url": knowledge.url if knowledge else "",
                        "relevance": item["score"],
                        "type": "chunk",
                        "domain": knowledge.domain,
                        "metadata": chunk.meta_info if isinstance(chunk.meta_info, dict) else {}
                    })
        return results

    async def search_by_vector(
        self,
        query_vector: List[float],
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """使用向量搜索相关内容"""
        start_time = time.time()
        try:
            # 1. 使用FAISS搜索相似向量
            vector_search_start = time.time()
            similar_vectors = await self.vector_store.search_similar(
                query_vector,
                limit=limit * 2,  # 获取更多结果以便后处理
                min_score=min_score,
                domains=domains
            )
            vector_search_end = time.time()
            
            logger.info(f"性能监控[KnowledgeRepository]: 向量搜索耗时: {(vector_search_end - vector_search_start)*1000:.2f}ms, 找到{len(similar_vectors)}个相似向量")

            # 2. 获取相关的知识条目和片段
            db_query_start = time.time()
            results = await self._get_knowledges_and_chunks(similar_vectors)
            results = results[:limit]
            db_query_end = time.time()
            
            logger.info(f"性能监控[KnowledgeRepository]: 数据库查询耗时: {(db_query_end - db_query_start)*1000:.2f}ms, 获取了{len(results)}条记录")

            # 3. 按相关度排序并限制数量
            sort_start = time.time()
            results.sort(key=lambda x: x["relevance"], reverse=True)

            limited_results = results[:limit]
            sort_end = time.time()
            
            logger.info(f"性能监控[KnowledgeRepository]: 排序和限制耗时: {(sort_end - sort_start)*1000:.2f}ms")
            
            end_time = time.time()
            logger.info(f"性能监控[KnowledgeRepository]: 向量搜索完成, 各阶段耗时:")
            logger.info(f"- 向量搜索: {(vector_search_end - vector_search_start)*1000:.2f}ms ({((vector_search_end - vector_search_start)/(end_time - start_time)*100):.1f}%)")
            logger.info(f"- 数据库查询: {(db_query_end - db_query_start)*1000:.2f}ms ({((db_query_end - db_query_start)/(end_time - start_time)*100):.1f}%)")
            logger.info(f"- 排序限制: {(sort_end - sort_start)*1000:.2f}ms ({((sort_end - sort_start)/(end_time - start_time)*100):.1f}%)")
            logger.info(f"- 总耗时: {(end_time - start_time)*1000:.2f}ms")
            
            return limited_results

        except Exception as e:
            end_time = time.time()
            logger.error(f"搜索知识失败: {str(e)}")
            logger.error(f"搜索知识失败: {traceback.format_exc()}")
            logger.info(f"性能监控[KnowledgeRepository]: 向量搜索失败, 耗时: {(end_time - start_time)*1000:.2f}ms")
            raise DatabaseError(f"搜索知识失败: {str(e)}")

    async def get_knowledge_by_id(self, knowledge_id: str) -> Optional[Dict[str, Any]]:
        """获取知识条目"""
        try:
            stmt = select(Knowledge).where(Knowledge.id == knowledge_id)
            result = await self.db.execute(stmt)
            knowledge = result.scalar_one_or_none()

            if not knowledge:
                return None

            # 获取知识片段
            stmt = select(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == knowledge_id)
            result = await self.db.execute(stmt)
            chunks = result.scalars().all()

            return {
                "id": knowledge.id,
                "title": knowledge.title,
                "content": knowledge.content,
                "url": knowledge.url,
                "metadata": knowledge.meta_info if isinstance(knowledge.meta_info, dict) else {},
                "source_type": knowledge.source_type,
                "domain": knowledge.domain,
                "chunks": [
                    {
                        "id": chunk.id,
                        "content": chunk.content,
                        "metadata": chunk.meta_info if isinstance(chunk.meta_info, dict) else {}
                    }
                    for chunk in chunks
                ],
                "created_at": knowledge.created_at.isoformat(),
                "updated_at": knowledge.updated_at.isoformat()
            }

        except Exception as e:
            logger.error(f"获取知识失败: {str(e)}")
            raise DatabaseError(f"获取知识失败: {str(e)}")

    async def update_knowledge(
        self,
        knowledge_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        url: Optional[str] = None,
        domain: Optional[str] = None,
        metadata: Optional[dict] = None,
        chunks: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """更新知识条目"""
        try:
            knowledge = await self.db.get(Knowledge, knowledge_id)

            if not knowledge:
                raise NotFoundError("知识条目不存在")

            # 记录是否需要更新向量
            need_update_vector = False
            
            # 1. 更新基本信息
            if title is not None:
                knowledge.title = title
                need_update_vector = True
                
            if content is not None:
                knowledge.content = content
                need_update_vector = True
                
            if url is not None:
                knowledge.url = url
                
            if domain is not None:
                knowledge.domain = domain
                
            if metadata is not None:
                knowledge.meta_info = metadata

            # 2. 如果内容更新，更新向量
            if need_update_vector:
                # 删除旧向量
                if knowledge.embedding_id:
                    await self.vector_store.delete_vectors([knowledge.embedding_id], knowledge.domain)
                
                # 创建新向量
                new_vector = await self._encode_text(knowledge.content)
                
                # 添加新向量
                vector_metadata = {
                    "title": knowledge.title,
                    "url": knowledge.url,
                    "type": "main"
                }
                vector_ids = await self.vector_store.add_vectors([new_vector], [vector_metadata], domain)
                knowledge.embedding_id = vector_ids[0]
            
            chunk_objects = []
            # 3. 更新知识片段
            if chunks is not None:
                # 删除旧的片段
                old_chunks = await self.db.execute(select(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == knowledge_id))
                for chunk in old_chunks.scalars().all():
                    if chunk.embedding_id:
                        chunk_domain = chunk.meta_info.get("domain", KnowledgeDomain.DEFAULT)
                        await self.vector_store.delete_vectors([chunk.embedding_id], chunk_domain)
                    await self.db.delete(chunk)

                # 添加新的片段
                for chunk_index, chunk in enumerate(chunks):
                    chunk_content = chunk.get("content", "")
                    
                    # 生成向量
                    chunk_vector = await self._encode_text(chunk_content)
                    
                    # 添加向量到存储
                    chunk_metadata = {
                        "title": knowledge.title,
                        "url": knowledge.url,
                        "type": "chunk",
                        "content": chunk_content[:200]
                    }
                    chunk_vector_ids = await self.vector_store.add_vectors(
                        [chunk_vector],
                        [chunk_metadata],
                        domain=domain
                    )
                    chunk_vector_id = chunk_vector_ids[0]

                    # 创建知识片段
                    chunk_id = IDGenerator.generate_chunk_id(knowledge.id, chunk_index)
                    knowledge_chunk = KnowledgeChunk(
                        id=chunk_id,
                        knowledge_id=knowledge.id,
                        content=chunk_content,
                        meta_info=chunk.get("metadata", {}),
                        embedding_id=chunk_vector_id
                    )
                    self.db.add(knowledge_chunk)
                    chunk_objects.append(knowledge_chunk)

            knowledge.updated_at = datetime.now()
            await self.db.commit()
            
            # 删除旧的knowledge和分片的ES索引
            try:
                await self.elastic_search.delete_knowledge(knowledge_id)
            except Exception as es_error:
                logger.warning(f"删除旧的ES索引失败: ID={knowledge_id}, error={str(es_error)}")
            
            # 把分片和主知识条目索引到ES
            try:
                await self.elastic_search.index_knowledge_and_chunks(knowledge, chunk_objects)
            except Exception as es_error:
                logger.warning(f"创建新的ES索引失败: ID={knowledge_id}, error={str(es_error)}")
            
            return True

        except NotFoundError:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新知识失败: {str(e)}")
            raise DatabaseError(f"更新知识失败: {str(e)}")

    async def delete_knowledge(self, knowledge_id: str) -> bool:
        """删除知识条目"""
        try:
            knowledge = await self.db.get(Knowledge, knowledge_id)

            if not knowledge:
                raise NotFoundError("知识条目不存在")

            # 1. 删除知识片段
            chunks = await self.db.execute(select(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == knowledge_id))
            for chunk in chunks.scalars().all():
                # 删除片段向量
                if chunk.embedding_id:
                    chunk_domain = chunk.meta_info.get("domain", KnowledgeDomain.DEFAULT)
                    await self.vector_store.delete_vectors([chunk.embedding_id], chunk_domain)
                await self.db.delete(chunk)

            # 2. 删除主向量
            if knowledge.embedding_id:
                await self.vector_store.delete_vectors([knowledge.embedding_id], knowledge.domain)

            # 3. 删除知识条目
            await self.db.delete(knowledge)
            await self.db.commit()
            
            # 4. 从Elasticsearch删除
            try:
                await self.elastic_search.delete_knowledge(knowledge_id)
            except Exception as es_error:
                logger.warning(f"从Elasticsearch删除失败: ID={knowledge_id}, error={str(es_error)}")
            
            return True

        except NotFoundError:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除知识失败: {str(e)}")
            raise DatabaseError(f"删除知识失败: {str(e)}")
    
    async def delete_all_knowledge(self, domains: Optional[List[str]] = None) -> int:
        """删除所有知识条目"""
        try:
            # 获取所有知识条目
            query = select(Knowledge)
            if domains:
                query = query.where(Knowledge.domain.in_(domains))
            result = await self.db.execute(query)
            knowledge_items = result.scalars().all()
            
            logger.info(f"需要删除知识条目: {len(knowledge_items)} 条, 领域: {domains}")
            count = 0
            
            # 逐个删除知识条目
            for knowledge in knowledge_items:
                try:
                    # 1. 删除知识片段
                    chunks = await self.db.execute(
                        select(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == knowledge.id)
                    )
                    for chunk in chunks.scalars().all():
                        # 删除片段向量
                        if chunk.embedding_id:
                            chunk_domain = chunk.meta_info.get("domain", KnowledgeDomain.DEFAULT)
                            await self.vector_store.delete_vectors([chunk.embedding_id], chunk_domain)
                        await self.db.delete(chunk)

                    # 2. 删除主向量
                    if knowledge.embedding_id:
                        await self.vector_store.delete_vectors([knowledge.embedding_id], knowledge.domain)

                    # 3. 删除知识条目
                    await self.db.delete(knowledge)
                    count += 1

                    # 4. 从Elasticsearch删除
                    try:
                        await self.elastic_search.delete_knowledge(knowledge.id)
                    except Exception as es_error:
                        logger.warning(f"从Elasticsearch删除失败: ID={knowledge.id}, error={str(es_error)}")

                except Exception as e:
                    logger.error(f"删除知识条目 {knowledge.id} 失败: {str(e)}")
                    # 继续处理下一个，不中断整个流程
            
            await self.db.commit()
            return count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"批量删除知识失败: {str(e)}")
            raise DatabaseError(f"批量删除知识失败: {str(e)}")
            
    async def get_all_knowledge(
        self, 
        page: int = 1, 
        page_size: int = 20,
        source_type: Optional[str] = None,
        domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取所有知识条目（带分页）"""
        try:
            # 构建基础查询
            query = select(Knowledge)
            
            # 按来源类型筛选
            if source_type:
                query = query.where(Knowledge.source_type == source_type)
                
            # 按领域筛选
            if domain:
                query = query.where(Knowledge.domain == domain)
                
            # 获取总数
            count_query = select(func.count()).select_from(Knowledge)
            if source_type:
                count_query = count_query.where(Knowledge.source_type == source_type)
            if domain:
                count_query = count_query.where(Knowledge.domain == domain)
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()
            
            # 分页并按创建时间排序
            query = query.order_by(desc(Knowledge.created_at))
            query = query.offset((page - 1) * page_size).limit(page_size)
            
            # 执行查询
            knowledge_items = await self.db.execute(query)
            
            # 格式化结果
            results = []
            for item in knowledge_items.scalars().all():
                # 获取片段数量
                chunk_count_query = select(func.count()).select_from(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == item.id)
                chunk_count_result = await self.db.execute(chunk_count_query)
                chunk_count = chunk_count_result.scalar()
                
                results.append({
                    "id": item.id,
                    "title": item.title,
                    "url": item.url,
                    "source_type": item.source_type,
                    "domain": item.domain,
                    "chunk_count": chunk_count,
                    "created_at": item.created_at.isoformat(),
                    "updated_at": item.updated_at.isoformat()
                })
                
            return {
                "total": total,
                "items": results,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            logger.error(f"获取知识列表失败: {str(e)}")
            raise DatabaseError(f"获取知识列表失败: {str(e)}")

    async def search_by_keywords(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """使用关键词搜索"""
        try:
            # 使用简单的关键词匹配
            stmt = select(Knowledge).where(
                Knowledge.content.ilike(f"%{query}%")
            ).limit(limit)

            if domains:
                stmt = stmt.where(Knowledge.domain.in_(domains))
            
            result = await self.db.execute(stmt)
            knowledge_items = result.scalars().all()
            
            results = []
            for knowledge in knowledge_items:
                # 计算一个简单的相关度分数（基于完全匹配）
                score = 1.0 if query.lower() in knowledge.content.lower() else 0.8
                
                # 只返回分数高于最小分数的结果
                if score >= min_score:
                    results.append({
                        "id": knowledge.id,
                        "title": knowledge.title,
                        "content": knowledge.content,
                        "url": knowledge.url,
                        "score": score,
                        "type": "document"
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {str(e)}")
            raise DatabaseError(f"关键词搜索失败: {str(e)}")

    async def search_by_bm25(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = 0.5,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """使用Elasticsearch BM25搜索"""
        try:
            # 性能监控
            start_time = time.time()
            search_results =  await self.elastic_search.search_by_bm25(
                query=query,
                limit=limit,
                min_score=min_score,
                domains=domains,
                **kwargs
            )
            end_time = time.time()
            logger.info(f"性能监控[KnowledgeRepository]: BM25搜索耗时: {(end_time - start_time)*1000:.2f}ms")
        except Exception as e:
            logger.error(f"BM25搜索失败: query='{query}', error={str(e)}")
            return []
        
        results = await self._get_knowledges_and_chunks(search_results)
        return results
        
    async def get_elasticsearch_stats(self) -> Dict[str, Any]:
        """获取Elasticsearch索引统计信息"""
        try:
            return await self.elastic_search.get_index_stats()
        except Exception as e:
            logger.error(f"获取Elasticsearch统计信息失败: {str(e)}")
            return {}