from fastapi import APIRouter

from .endpoints import users, login, chat, llm, knowledge, crawler, user_settings, debug

api_router = APIRouter()

api_router.include_router(login.router, prefix='/login', tags=['登录'])
api_router.include_router(users.router, prefix='/users', tags=['用户'])
api_router.include_router(user_settings.router, prefix='/user-settings', tags=['用户设置'])
api_router.include_router(chat.router, prefix='/chat', tags=['聊天'])
api_router.include_router(llm.router, prefix='/llm', tags=['大语言模型'])
api_router.include_router(knowledge.router, prefix='/knowledge', tags=['知识库'])
api_router.include_router(crawler.router, prefix='/crawlers', tags=['爬虫'])

# 添加路由注册
api_router.include_router(debug.router, prefix='/debug', tags=['debug'])