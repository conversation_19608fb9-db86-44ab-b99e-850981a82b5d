from datetime import timed<PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from ....core import deps
from ....core.config import settings
from ....core.security import create_access_token
from ....schemas.token import Token
from ....schemas.msg import Msg

router = APIRouter()

@router.post('/login/access-token', response_model=Token,
             summary='用户登录获取访问令牌',
             response_description='返回访问令牌和令牌类型')
async def login_access_token(
    db: AsyncSession = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    用户登录并获取访问令牌
    
    ## 功能描述
    
    - 验证用户名和密码
    - 生成JWT访问令牌
    - 支持OAuth2兼容的令牌认证
    
    ## 参数说明
    
    - **username**: 用户名/邮箱
    - **password**: 用户密码
    
    ## 返回说明
    
    - **access_token**: JWT访问令牌
    - **token_type**: 令牌类型，固定为'bearer'
    
    ## 错误情况
    
    - 400: 用户名或密码错误
    - 400: 用户未激活
    """
    user = await deps.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=400, detail='Incorrect username or password')
    elif not user.is_active:
        raise HTTPException(status_code=400, detail='Inactive user')
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        'access_token': create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        'token_type': 'bearer',
    }

@router.post('/login/test-token', response_model=Msg,
             summary='测试访问令牌',
             response_description='如果令牌有效，返回成功消息')
async def test_token(current_user = Depends(deps.get_current_user)) -> Any:
    """
    测试访问令牌是否有效
    
    ## 功能描述
    
    - 验证当前请求的访问令牌是否有效
    - 需要在请求头中包含有效的Authorization: Bearer {token}
    
    ## 返回说明
    
    - **msg**: 成功消息，固定为'ok'
    
    ## 错误情况
    
    - 401: 未提供有效的访问令牌
    - 401: 访问令牌已过期或无效
    """
    return {'msg': 'ok'} 