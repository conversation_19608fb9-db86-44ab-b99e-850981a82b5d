import uuid
from typing import Any
from datetime import datetime, timezone
from fastapi import APIRout<PERSON>, Depends, Query, HTTPException, Body
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import desc, or_, select, func
import json

from ....core import deps
from ....models.chat import ChatSession, ChatMessage, MessageReference
from ....schemas.chat import (
    SendMessageRequest,
    SendMessageResponse,
    ChatHistoryResponse,
    ChatSessionsResponse,
    CreateSessionResponse
)
from ....repositories.chat_repository import ChatRepository
from ....repositories.knowledge_repository import KnowledgeRepository
from ....services.chat_service import ChatService
from ....core.logging import get_module_logger
from ....core.exceptions import (
    LLMServiceError, 
    LLMServiceTimeoutError, 
    LLMServiceUnavailableError,
    LLMServiceRequestError,
    LLMServiceAuthError,
    LLMServiceQuotaExceededError,
    LLMServiceContentFilterError,
    LLMServiceInvalidInputError
)

# 添加日志记录器
logger = get_module_logger(__name__)

router = APIRouter()

@router.post('/messages', response_model=SendMessageResponse, 
             summary='发送聊天消息',
             response_description='包含AI回答和相关引用的响应')
async def send_message(
    *,
    db: AsyncSession = Depends(deps.get_db),
    request: SendMessageRequest = Body(
        ...,
        examples={
            '普通问答': {
                'summary': '发送普通问题',
                'description': '向AI发送一个关于鸿蒙开发的问题',
                'value': {
                    'message': '如何使用ArkTS开发鸿蒙应用？',
                    'session_id': None,
                    'context_messages': [],
                    'stream': False,
                    'domains': ['api_documentation', 'best_practices']
                }
            },
            '流式响应': {
                'summary': '使用流式响应',
                'description': '启用流式响应获取实时回答',
                'value': {
                    'message': '如何使用HarmonyOS的分布式能力？',
                    'session_id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                    'stream': True,
                    'domains': ['api_documentation', 'best_practices']
                }
            }
        }
    )
) -> Any:
    """
    发送新的聊天消息并获取AI回答
    
    ## 功能描述
    
    - 向AI发送问题并获取基于知识库的回答
    - 支持普通响应和流式响应
    - 自动关联到相关的知识库内容
    - 可以创建新会话或继续已有会话
    
    ## 参数说明
    
    - **message**: 用户发送的消息内容
    - **session_id**: 会话ID，如果为空则创建新会话
    - **context_messages**: 上下文消息列表，可选
    - **stream**: 是否使用流式响应，默认为false
    - **domains**: 知识库领域，可选
    
    ## 返回说明
    
    - **answer**: AI的回答内容
    - **references**: 回答引用的知识库内容
    - **session_id**: 会话ID
    - **message_id**: 消息ID
    """
    chat_repository = ChatRepository(db)
    knowledge_repository = KnowledgeRepository(db)
    chat_service = ChatService(chat_repository, knowledge_repository)
    
    if request.stream:
        # 流式响应
        generator = await chat_service.send_message(
            content=request.message,
            session_id=request.session_id,
            context_messages=request.context_messages,
            stream=True,
            domains=request.domains
        )
        
        async def event_generator():
            try:
                references = []
                async for chunk in generator:
                    if isinstance(chunk, dict) and 'references' in chunk:
                        # 这是包含引用的特殊chunk
                        references = chunk['references']
                        continue
                    if chunk:
                        # 使用 JSON 格式化 SSE 事件，确保换行符能够正确传递
                        yield f'data: {json.dumps(chunk)}\n'
                
                # 在文本完成后发送引用信息
                if references:
                    yield f'data: {json.dumps({"references": references})}\n\n'
                
                # 正常完成时发送[DONE]标记
                yield 'data: [DONE]\n\n'
            except LLMServiceTimeoutError as e:
                logger.error(f'LLM服务超时: {str(e)}')
                yield f'data: 模型响应超时，请稍后再试\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceUnavailableError as e:
                logger.error(f'LLM服务不可用: {str(e)}')
                yield f'data: 模型服务暂时不可用，请检查服务状态\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceRequestError as e:
                logger.error(f'LLM服务请求错误: {str(e)}')
                yield f'data: 请求处理失败: {str(e)}\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceAuthError as e:
                logger.error(f'LLM服务认证错误: {str(e)}')
                yield f'data: 模型服务认证失败，请检查API密钥\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceQuotaExceededError as e:
                logger.error(f'LLM服务配额超限: {str(e)}')
                yield f'data: 模型服务使用已达上限，请稍后再试\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceContentFilterError as e:
                logger.error(f'内容被过滤: {str(e)}')
                yield f'data: 您的请求包含不适当内容，无法生成回答\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceInvalidInputError as e:
                logger.error(f'无效的输入: {str(e)}')
                yield f'data: 输入格式无效，请修改后重试\n\n'
                yield 'data: [DONE]\n\n'
            except LLMServiceError as e:
                logger.error(f'LLM服务错误: {str(e)}')
                yield f'data: 模型服务出现错误: {str(e)}\n\n'
                yield 'data: [DONE]\n\n'
            except Exception as e:
                logger.error(f'生成事件流失败: {str(e)}')
                yield f'data: 生成回答时出现未知错误，请稍后重试\n\n'
                yield 'data: [DONE]\n\n'
        
        return StreamingResponse(
            event_generator(),
            media_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'text/event-stream',
                'X-Accel-Buffering': 'no'
            }
        )
    else:
        # 普通响应
        try:
            result = await chat_service.send_message(
                content=request.message,
                session_id=request.session_id,
                context_messages=request.context_messages,
                domains=request.domains
            )
            
            return {
                'code': 200,
                'message': 'success',
                'data': result,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceTimeoutError as e:
            logger.error(f'LLM服务超时: {str(e)}')
            return {
                'code': 504,
                'message': '模型响应超时，请稍后再试',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceUnavailableError as e:
            logger.error(f'LLM服务不可用: {str(e)}')
            return {
                'code': 503,
                'message': '模型服务暂时不可用，请检查服务状态',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceRequestError as e:
            logger.error(f'LLM服务请求错误: {str(e)}')
            return {
                'code': 400,
                'message': f'请求处理失败: {str(e)}',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceAuthError as e:
            logger.error(f'LLM服务认证错误: {str(e)}')
            return {
                'code': 401,
                'message': '模型服务认证失败，请检查API密钥',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceQuotaExceededError as e:
            logger.error(f'LLM服务配额超限: {str(e)}')
            return {
                'code': 429,
                'message': '模型服务使用已达上限，请稍后再试',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceContentFilterError as e:
            logger.error(f'内容被过滤: {str(e)}')
            return {
                'code': 403,
                'message': '您的请求包含不适当内容，无法生成回答',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceInvalidInputError as e:
            logger.error(f'无效的输入: {str(e)}')
            return {
                'code': 400,
                'message': '输入格式无效，请修改后重试',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except LLMServiceError as e:
            logger.error(f'LLM服务错误: {str(e)}')
            return {
                'code': 500,
                'message': f'模型服务出现错误: {str(e)}',
                'data': None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

@router.get('/history', response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: str,
    page_size: int = Query(20, ge=1, le=100),
    page_number: int = Query(1, ge=1),
    db: AsyncSession = Depends(deps.get_db)
) -> Any:
    """
    获取聊天历史记录
    """
    try:
        # 计算偏移量
        skip = (page_number - 1) * page_size
        
        # 获取消息总数
        count_query = select(func.count()).select_from(ChatMessage).where(
            ChatMessage.session_id == session_id
        )
        total = await db.scalar(count_query)
        
        # 获取分页消息
        query = select(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).order_by(
            desc(ChatMessage.timestamp)
        ).offset(skip).limit(page_size)
        
        result = await db.execute(query)
        messages = result.scalars().all()
        
        # 构建消息列表
        message_list = []
        for msg in messages:
            # 获取消息的引用
            ref_query = select(MessageReference).filter(
                MessageReference.message_id == msg.id
            )
            ref_result = await db.execute(ref_query)
            references = ref_result.scalars().all()
            
            message_data = {
                'id': msg.id,
                'role': msg.role,
                'content': msg.content,
                'timestamp': msg.timestamp.isoformat(),
                'references': [
                    {
                        'title': ref.title,
                        'url': ref.url,
                        'content': ref.content,
                        'relevance': ref.relevance
                    }
                    for ref in references
                ]
            }
            message_list.append(message_data)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'messages': message_list,
                'total': total,
                'page_size': page_size,
                'page_number': page_number
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f'获取聊天历史记录失败: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'获取聊天历史记录失败: {str(e)}'
        )

@router.get('/sessions', response_model=ChatSessionsResponse)
async def get_sessions(
    page_size: int = Query(20, ge=1, le=100),
    page_number: int = Query(1, ge=1),
    db: AsyncSession = Depends(deps.get_db)
) -> Any:
    """
    获取会话列表
    """
    try:
        skip = (page_number - 1) * page_size
        
        # Get total count
        count_query = select(func.count()).select_from(ChatSession)
        total = await db.scalar(count_query)
        
        # Get paginated sessions with message count
        message_count_subquery = (
            select(
                ChatMessage.session_id,
                func.count().label('message_count')
            )
            .group_by(ChatMessage.session_id)
            .subquery()
        )
        
        query = (
            select(
                ChatSession,
                message_count_subquery.c.message_count
            )
            .outerjoin(
                message_count_subquery,
                ChatSession.id == message_count_subquery.c.session_id
            )
            .order_by(desc(ChatSession.updated_at))
            .offset(skip)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        sessions_with_count = result.all()
        
        session_list = []
        for session_row in sessions_with_count:
            session = session_row[0]
            message_count = session_row[1] or 0
            
            # 使用异步查询获取最后一条消息
            last_message_query = (
                select(ChatMessage)
                .filter(ChatMessage.session_id == session.id)
                .order_by(desc(ChatMessage.timestamp))
                .limit(1)
            )
            
            last_message_result = await db.execute(last_message_query)
            last_message = last_message_result.scalar()
            
            session_data = {
                'id': session.id,
                'title': session.title or '新会话',
                'last_message': None,
                'last_message_time': None,
                'message_count': message_count,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
            }
            
            if last_message:
                session_data['last_message'] = last_message.content
                session_data['last_message_time'] = last_message.timestamp.isoformat()
            
            session_list.append(session_data)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'sessions': session_list,
                'total': total,
                'page_size': page_size,
                'page_number': page_number
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f'获取会话列表失败: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'获取会话列表失败: {str(e)}'
        )

@router.post('/sessions', response_model=CreateSessionResponse)
async def create_session(
    *,
    db: AsyncSession = Depends(deps.get_db),
    title: str = None
) -> Any:
    try:
        session = ChatSession(
            id=str(uuid.uuid4()),
            title=title
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'session': {
                    'id': session.id,
                    'title': session.title or '新会话',
                    'message_count': 0,
                    'created_at': session.created_at.isoformat(),
                    'updated_at': session.updated_at.isoformat(),
                }
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f'创建会话失败: {str(e)}'
        )

@router.put('/sessions/{session_id}')
async def update_session(
    *,
    db: AsyncSession = Depends(deps.get_db),
    session_id: str,
    title: str
) -> Any:
    try:
        # 查找会话
        query = select(ChatSession).filter(ChatSession.id == session_id)
        result = await db.execute(query)
        session = result.scalar()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail='会话不存在'
            )
        
        # 获取消息数量
        count_query = select(func.count()).where(ChatMessage.session_id == session_id)
        message_count = await db.scalar(count_query)
        
        session.title = title
        await db.commit()
        await db.refresh(session)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'session': {
                    'id': session.id,
                    'title': session.title,
                    'message_count': message_count,
                    'created_at': session.created_at.isoformat(),
                    'updated_at': session.updated_at.isoformat(),
                }
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f'更新会话失败: {str(e)}'
        )

@router.get('/sessions/search', response_model=ChatSessionsResponse)
async def search_sessions(
    keyword: str = Query(..., min_length=1),
    page_size: int = Query(20, ge=1, le=100),
    page_number: int = Query(1, ge=1),
    db: AsyncSession = Depends(deps.get_db)
) -> Any:
    try:
        skip = (page_number - 1) * page_size
        
        # 构建搜索条件 - 使用EXISTS子查询替代any()
        message_subquery = (
            select(1)
            .where(ChatMessage.session_id == ChatSession.id)
            .where(ChatMessage.content.ilike(f'%{keyword}%'))
            .exists()
            .label('has_matching_message')
        )
        
        search_condition = or_(
            ChatSession.title.ilike(f'%{keyword}%'),
            message_subquery
        )
        
        # 获取总数
        count_query = select(func.count()).select_from(ChatSession).where(search_condition)
        total = await db.scalar(count_query)
        
        # 获取分页数据，同时获取消息数量
        message_count_subquery = (
            select(
                ChatMessage.session_id,
                func.count().label('message_count')
            )
            .group_by(ChatMessage.session_id)
            .subquery()
        )
        
        query = (
            select(
                ChatSession,
                message_count_subquery.c.message_count
            )
            .outerjoin(
                message_count_subquery,
                ChatSession.id == message_count_subquery.c.session_id
            )
            .where(search_condition)
            .order_by(desc(ChatSession.updated_at))
            .offset(skip)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        sessions_with_count = result.all()
        
        session_list = []
        for session_row in sessions_with_count:
            session = session_row[0]
            message_count = session_row[1] or 0
            
            # 获取最后一条消息
            last_message_query = (
                select(ChatMessage)
                .filter(ChatMessage.session_id == session.id)
                .order_by(desc(ChatMessage.timestamp))
                .limit(1)
            )
            
            last_message_result = await db.execute(last_message_query)
            last_message = last_message_result.scalar()
            
            session_data = {
                'id': session.id,
                'title': session.title or '新会话',
                'last_message': None,
                'last_message_time': None,
                'message_count': message_count,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
            }
            
            if last_message:
                session_data['last_message'] = last_message.content
                session_data['last_message_time'] = last_message.timestamp.isoformat()
            
            session_list.append(session_data)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'sessions': session_list,
                'total': total,
                'page_size': page_size,
                'page_number': page_number
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f'搜索会话失败: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f'搜索会话失败: {str(e)}'
        )

@router.post('/feedback', summary='提交反馈')
async def submit_feedback(
    *,
    db: AsyncSession = Depends(deps.get_db),
    feedback_data: dict = Body(
        ...,
        example={
            'messageId': 'message-123',
            'feedback': 'positive',
            'comment': '这个回答非常有帮助！',
            'query': '如何使用ArkTS开发鸿蒙应用？',
            'response': 'ArkTS是鸿蒙生态开发的推荐语言...'
        }
    )
) -> Any:
    """
    提交用户对回答的反馈
    
    ## 功能描述
    
    - 记录用户对AI回答的反馈（点赞/点踩）
    - 可以附带文本评论
    - 用于改进回答质量
    
    ## 参数说明
    
    - **messageId**: 消息ID
    - **feedback**: 反馈类型 (positive/negative)
    - **comment**: 可选的文本评论
    - **query**: 用户提问内容
    - **response**: AI回答内容
    """
    try:
        # 这里可以添加将反馈保存到数据库的逻辑
        # 暂时只记录日志，实际使用时需要扩展
        logger.info(f'收到用户反馈: {feedback_data}')
        
        # 返回成功响应
        return {
            'code': 200,
            'message': '反馈已记录',
            'data': None,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f'处理反馈失败: {str(e)}')
        return {
            'code': 500,
            'message': f'处理反馈失败: {str(e)}',
            'data': None,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@router.post('/stop', 
             summary='停止正在生成的回答',
             response_description='停止AI回答的请求结果')
async def stop_answer(
    *,
    db: AsyncSession = Depends(deps.get_db),
    data: dict = Body(
        ...,
        examples={
            'normal': {
                'summary': '正常停止请求',
                'description': '停止指定会话中特定请求的回答生成',
                'value': {
                    'session_id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                    'request_id': '1234567890'
                }
            }
        }
    )
) -> Any:
    """
    停止正在生成的AI回答
    
    ## 功能描述
    
    - 中止指定会话中的特定回答生成过程
    - 基于session_id和request_id识别需要停止的请求
    
    ## 参数说明
    
    - **session_id**: 会话ID
    - **request_id**: 请求ID，与前端消息ID对应
    
    ## 返回说明
    
    - **success**: 是否成功停止
    """
    try:
        # 目前只返回成功响应，后续实现实际停止逻辑
        session_id = data.get('session_id')
        request_id = data.get('request_id')
        
        logger.info(f'收到停止回答请求: session_id={session_id}, request_id={request_id}')
        
        # 后续这里会实现实际的停止逻辑
        
        return {
            'code': 200,
            'message': '成功处理停止请求',
            'data': {
                'success': True
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f'处理停止请求失败: {str(e)}')
        return {
            'code': 500,
            'message': f'处理停止请求失败: {str(e)}',
            'data': {
                'success': False
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        } 