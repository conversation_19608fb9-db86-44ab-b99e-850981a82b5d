from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Any
from datetime import datetime, UTC

from ....core import deps
from ....services.llm_service_factory import LLMServiceFactory
from ....core.exceptions import LLMServiceError
from ....core.logging import get_module_logger

# 添加日志记录器
logger = get_module_logger(__name__)

router = APIRouter()

@router.get('/status')
async def check_llm_status(
    *,
    db: Session = Depends(deps.get_db),
    service_type: str = None  # 默认使用配置中的服务类型
) -> Any:
    """
    检查LLM服务状态
    """
    try:
        # 使用工厂类检查服务状态
        status = await LLMServiceFactory.check_service_status(service_type)
        
        return {
            'code': 200,
            'message': 'success',
            'data': status,
            'timestamp': datetime.now(UTC).isoformat()
        }
    except LLMServiceError as e:
        logger.error(f'检查LLM服务状态失败: {str(e)}')
        return {
            'code': 500,
            'message': f'检查服务状态失败: {str(e)}',
            'data': {
                'available': False,
                'message': str(e)
            },
            'timestamp': datetime.now(UTC).isoformat()
        }
    except Exception as e:
        logger.error(f'检查LLM服务状态时出现未知错误: {str(e)}')
        return {
            'code': 500,
            'message': '检查服务状态时出现未知错误',
            'data': {
                'available': False,
                'message': str(e)
            },
            'timestamp': datetime.now(UTC).isoformat()
        }

@router.get('/models')
async def get_llm_models(
    *,
    db: Session = Depends(deps.get_db),
    service_type: str = None  # 默认使用配置中的服务类型
) -> Any:
    """
    获取LLM服务支持的模型列表
    """
    try:
        # 检查服务状态，获取模型列表
        status = await LLMServiceFactory.check_service_status(service_type)
        
        if not status.get('available', False):
            return {
                'code': 503,
                'message': 'LLM服务不可用',
                'data': {
                    'models': [],
                    'message': status.get('message', '服务不可用')
                },
                'timestamp': datetime.now(UTC).isoformat()
            }
            
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'models': status.get('models', []),
                'current_model': status.get('current_model', ''),
                'model_available': status.get('model_available', False)
            },
            'timestamp': datetime.now(UTC).isoformat()
        }
    except LLMServiceError as e:
        logger.error(f'获取LLM模型列表失败: {str(e)}')
        return {
            'code': 500,
            'message': f'获取模型列表失败: {str(e)}',
            'data': {
                'models': []
            },
            'timestamp': datetime.now(UTC).isoformat()
        }
    except Exception as e:
        logger.error(f'获取LLM模型列表时出现未知错误: {str(e)}')
        return {
            'code': 500,
            'message': '获取模型列表时出现未知错误',
            'data': {
                'models': []
            },
            'timestamp': datetime.now(UTC).isoformat()
        } 