from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ....core.logging import get_module_logger

from ....core import deps
from ....repositories.knowledge_repository import KnowledgeRepository
from ....services.crawler_service import CrawlerService
from ....core.exceptions import ProcessingError

router = APIRouter()
logger = get_module_logger(__name__)

@router.get('/', response_model=List[Dict[str, Any]])
async def list_crawlers(
    *,
    db: Session = Depends(deps.get_db)
) -> List[Dict[str, Any]]:
    """
    获取所有可用的爬虫列表
    """
    try:
        repository = KnowledgeRepository(db)
        service = CrawlerService(repository)
        return service.get_available_crawlers()
        
    except Exception as e:
        logger.error(f'获取爬虫列表失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='获取爬虫列表失败'
        )

@router.post('/{crawler_name}/run', response_model=Dict[str, Any])
async def run_crawler(
    *,
    db: Session = Depends(deps.get_db),
    crawler_name: str,
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    运行指定的爬虫
    """
    try:
        repository = KnowledgeRepository(db)
        service = CrawlerService(repository)
        
        result = await service.run_crawler(
            crawler_name=crawler_name,
            config=config
        )
        
        return result
        
    except ProcessingError as e:
        logger.error(f'运行爬虫失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f'运行爬虫时发生未知错误: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='运行爬虫失败'
        )

@router.post('/run-all', response_model=Dict[str, Any])
async def run_all_crawlers(
    *,
    db: Session = Depends(deps.get_db),
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    运行所有爬虫
    """
    try:
        repository = KnowledgeRepository(db)
        service = CrawlerService(repository)
        
        result = await service.run_all_crawlers(config=config)
        return result
        
    except ProcessingError as e:
        logger.error(f'运行爬虫失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f'运行爬虫时发生未知错误: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='运行爬虫失败'
        ) 