from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, UTC

from src.core import deps
from src.models.user import User
from src.models.user_settings import UserSettings
from src.schemas.user_settings import (
    UserSettingsUpdate,
    UserSettingsResponse
)

router = APIRouter()

@router.get('/settings', response_model=UserSettingsResponse)
def get_user_settings(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取用户设置
    """
    if not current_user.settings:
        settings = UserSettings(user_id=current_user.id)
        db.add(settings)
        db.commit()
        db.refresh(settings)
        current_user.settings = settings
    
    return {
        'code': 200,
        'message': 'success',
        'data': {
            'settings': {
                'theme': current_user.settings.theme,
                'language': current_user.settings.language,
                'notification_enabled': current_user.settings.notification_enabled
            }
        },
        'timestamp': datetime.now(UTC).isoformat()
    }

@router.put('/settings', response_model=UserSettingsResponse)
def update_user_settings(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    settings_in: UserSettingsUpdate
) -> Any:
    """
    更新用户设置
    """
    if not current_user.settings:
        settings = UserSettings(user_id=current_user.id)
        db.add(settings)
        db.commit()
        db.refresh(settings)
        current_user.settings = settings
    
    settings = current_user.settings
    for field, value in settings_in.dict(exclude_unset=True).items():
        setattr(settings, field, value)
    
    db.add(settings)
    db.commit()
    db.refresh(settings)
    
    return {
        'code': 200,
        'message': 'success',
        'data': {
            'settings': {
                'theme': settings.theme,
                'language': settings.language,
                'notification_enabled': settings.notification_enabled
            }
        },
        'timestamp': datetime.now(UTC).isoformat()
    } 