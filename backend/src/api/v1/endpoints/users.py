from typing import Any, List
from fastapi import APIRouter, Body, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from pydantic.networks import EmailStr
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ....core import deps
from ....core.security import get_password_hash, verify_password
from ....models.user import User
from ....schemas.user import User as UserSchema
from ....schemas.user import UserCreate, UserUpdate

router = APIRouter()

@router.get('/', response_model=List[UserSchema])
async def read_users(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Retrieve users.
    """
    query = select(User).offset(skip).limit(limit)
    result = await db.execute(query)
    users = result.scalars().all()
    return users

@router.post('/', response_model=UserSchema)
async def create_user(
    *,
    db: AsyncSession = Depends(deps.get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Create new user.
    """
    # 检查邮箱是否已存在
    query = select(User).filter(User.email == user_in.email)
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    if user:
        raise HTTPException(
            status_code=400,
            detail='The user with this email already exists in the system.',
        )
    
    # 检查用户名是否已存在
    query = select(User).filter(User.username == user_in.username)
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    if user:
        raise HTTPException(
            status_code=400,
            detail='The user with this username already exists in the system.',
        )
    
    user_in_data = jsonable_encoder(user_in)
    user_in_data.pop('password')
    db_obj = User(**user_in_data)
    db_obj.hashed_password = get_password_hash(user_in.password)
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj

@router.get('/me', response_model=UserSchema)
async def read_user_me(
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    Get current user.
    """
    return current_user

@router.put('/me', response_model=UserSchema)
async def update_user_me(
    *,
    db: AsyncSession = Depends(deps.get_db),
    password: str = Body(None),
    email: EmailStr = Body(None),
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    Update own user.
    """
    current_user_data = jsonable_encoder(current_user)
    user_in = UserUpdate(**current_user_data)
    if password is not None:
        user_in.password = password
    if email is not None:
        user_in.email = email
    
    current_user.email = user_in.email
    if user_in.password:
        current_user.hashed_password = get_password_hash(user_in.password)
    
    await db.commit()
    await db.refresh(current_user)
    return current_user

@router.get('/{user_id}', response_model=UserSchema)
async def read_user_by_id(
    user_id: int,
    current_user: User = Depends(deps.get_current_active_superuser),
    db: AsyncSession = Depends(deps.get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    query = select(User).filter(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar()
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail='The user with this id does not exist in the system',
        )
    return user

@router.put('/{user_id}', response_model=UserSchema)
async def update_user(
    *,
    db: AsyncSession = Depends(deps.get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Update a user.
    """
    query = select(User).filter(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar()
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail='The user with this id does not exist in the system',
        )
    
    if user_in.password:
        user.hashed_password = get_password_hash(user_in.password)
    if user_in.email:
        user.email = user_in.email
    if user_in.is_active is not None:
        user.is_active = user_in.is_active
    if user_in.is_superuser is not None:
        user.is_superuser = user_in.is_superuser
    
    await db.commit()
    await db.refresh(user)
    return user 