from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form, status, Body
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.ext.asyncio import AsyncSession
from ....core.logging import get_module_logger
import os
import shutil
from datetime import datetime, UTC

from ....core import deps
from ....repositories.knowledge_repository import KnowledgeRepository
from ....repositories.import_task_repository import ImportTaskRepository
from ....services.knowledge_service import KnowledgeService
from ....schemas.knowledge import (
    KnowledgeCreate,
    KnowledgeUpdate,
    KnowledgeResponse,
    KnowledgeSearchRequest,
    KnowledgeListResponse,
    KnowledgeDetailResponse,
    ImportTaskResponse,
    ImportTaskListResponse
)
from ....core.exceptions import NotFoundError, ProcessingError, DatabaseError
from ....core.constants import GROUP_DOMAINS, GroupId
from ....core.config import settings

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

router = APIRouter()

# 确保上传目录存在
UPLOAD_DIR = os.path.join('data', 'uploads')
os.makedirs(UPLOAD_DIR, exist_ok=True)

@router.post('/', response_model=Dict[str, str], status_code=status.HTTP_201_CREATED)
async def create_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    knowledge: KnowledgeCreate
) -> Dict[str, str]:
    """
    创建知识条目
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        knowledge_id = await service.process_document(
            title=knowledge.title,
            content=knowledge.content,
            url=knowledge.url,
            source_type=knowledge.source_type,
            domain=knowledge.domain,
            metadata=knowledge.metadata
        )
        
        return {'id': knowledge_id}
        
    except ProcessingError as e:
        logger.error(f'创建知识条目失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f'创建知识条目时发生未知错误: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='创建知识条目失败'
        )

@router.get('/', response_model=KnowledgeListResponse)
async def list_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    source_type: Optional[str] = None,
    domain: Optional[str] = None
) -> KnowledgeListResponse:
    """
    获取知识条目列表
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        result = await service.get_knowledge_list(
            page=page,
            page_size=page_size,
            source_type=source_type,
            domain=domain
        )
        
        return result
        
    except Exception as e:
        logger.error(f'获取知识条目列表失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='获取知识条目列表失败'
        )

@router.get('/import-tasks', response_model=ImportTaskListResponse)
async def list_import_tasks(
    *,
    db: AsyncSession = Depends(deps.get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100)
) -> ImportTaskListResponse:
    """
    获取导入任务列表
    """
    try:
        repository = KnowledgeRepository(db)
        import_repository = ImportTaskRepository(db)
        service = KnowledgeService(repository, import_repository)
        
        skip = (page - 1) * page_size
        tasks, total = await service.list_import_tasks(skip, page_size)
        
        return {
            'code': 200,
            'message': 'success',
            'data': {
                'tasks': tasks,
                'total': total,
                'page': page,
                'page_size': page_size
            },
            'timestamp': datetime.now(UTC).isoformat()
        }
        
    except Exception as e:
        logger.error(f'获取导入任务列表失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'获取导入任务列表失败: {str(e)}'
        )

@router.get('/{knowledge_id}', response_model=KnowledgeDetailResponse)
async def get_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    knowledge_id: str
) -> KnowledgeDetailResponse:
    """
    获取知识条目详情
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        knowledge = await service.get_knowledge_by_id(knowledge_id)
        return knowledge
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='知识条目不存在'
        )
    except Exception as e:
        logger.error(f'获取知识条目详情失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='获取知识条目详情失败'
        )

@router.put('/{knowledge_id}', response_model=Dict[str, bool])
async def update_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    knowledge_id: str,
    knowledge: KnowledgeUpdate
) -> Dict[str, bool]:
    """
    更新知识条目
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        success = await service.update_knowledge(
            knowledge_id=knowledge_id,
            title=knowledge.title,
            content=knowledge.content,
            url=knowledge.url,
            domain=knowledge.domain,
            metadata=knowledge.metadata,
            reprocess_chunks=knowledge.reprocess_chunks
        )
        
        return {'success': success}
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='知识条目不存在'
        )
    except ProcessingError as e:
        logger.error(f'更新知识条目失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f'更新知识条目时发生未知错误: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='更新知识条目失败'
        )

@router.delete('/all', response_model=Dict[str, Any])
async def delete_all_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db)
) -> Dict[str, Any]:
    """
    删除所有知识条目
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        count = await service.delete_all_knowledge()
        return {'success': True, 'count': count}
        
    except Exception as e:
        logger.error(f'删除所有知识条目失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='删除所有知识条目失败'
        )

@router.delete('/{knowledge_id}', response_model=Dict[str, bool])
async def delete_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    knowledge_id: str
) -> Dict[str, bool]:
    """
    删除知识条目
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        success = await service.delete_knowledge(knowledge_id)
        return {'success': success}
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='知识条目不存在'
        )
    except Exception as e:
        logger.error(f'删除知识条目失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='删除知识条目失败'
        )

@router.post('/search', response_model=List[KnowledgeResponse],
             summary='搜索知识库内容',
             response_description='返回与查询相关的知识条目列表')
async def search_knowledge(
    *,
    db: AsyncSession = Depends(deps.get_db),
    search_request: KnowledgeSearchRequest = Body(
        ...,
        examples={
            '基本搜索': {
                'summary': '基本知识搜索',
                'description': '搜索与\'pixelMap内存问题\'相关的知识',
                'value': {
                    'query': 'pixelMap内存问题',
                    'limit': settings.DEFAULT_SEARCH_LIMIT,
                    'min_score': settings.MIN_RELEVANCE_SCORE,
                    'use_multi_query': False
                }
            },
            '多查询搜索': {
                'summary': '使用多查询扩展',
                'description': '使用查询变体扩展搜索结果范围',
                'value': {
                    'query': 'ArkTS组件生命周期',
                    'limit': 10,
                    'min_score': settings.MIN_RELEVANCE_SCORE,
                    'use_multi_query': True
                }
            }
        }
    )
) -> List[KnowledgeResponse]:
    """
    搜索知识库内容
    
    ## 功能描述
    
    - 基于语义向量搜索知识库内容
    - 支持多查询变体扩展，提高召回率
    - 支持设置结果数量和相似度阈值
    - 返回按相关度排序的知识条目或知识块
    
    ## 参数说明
    
    - **query**: 搜索查询文本，必填
    - **limit**: 返回结果数量，默认{settings.DEFAULT_SEARCH_LIMIT}，范围1-20
    - **min_score**: 最小相似度分数，默认{settings.MIN_RELEVANCE_SCORE}，范围0-1
    - **use_multi_query**: 是否启用多查询扩展，默认根据系统配置决定
    
    ## 返回说明
    
    返回一个知识条目列表，每个条目包含：
    
    - **id**: 知识条目或知识块ID
    - **title**: 知识标题
    - **content**: 知识内容
    - **url**: 知识来源URL（可能为null）
    - **relevance**: 相关度分数（0-1之间）
    - **type**: 结果类型，document（整个文档）或chunk（文档片段）
    - **knowledge_id**: 如果是chunk，所属的知识条目ID
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        results = await service.search_knowledge(
            query=search_request.query,
            domains=search_request.domains,
            limit=search_request.limit,
            min_score=search_request.min_score,
            use_multi_query=search_request.use_multi_query
        )
        logger.info(f'搜索知识库成功, query={search_request.query}, domains={search_request.domains}, limit={search_request.limit}, '
                    f'min_score={search_request.min_score}, use_multi_query={search_request.use_multi_query}, 找到{len(results)}条结果')
        return results
        
    except Exception as e:
        logger.error(f'搜索知识库失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='搜索知识库失败'
        )

@router.post('/import', response_model=Dict[str, Any])
async def import_documents(
    *,
    db: AsyncSession = Depends(deps.get_db),
    documents: List[KnowledgeCreate]
) -> Dict[str, Any]:
    """
    批量导入文档到知识库
    """
    try:
        repository = KnowledgeRepository(db)
        service = KnowledgeService(repository)
        
        # 将请求模型转换为文档列表
        docs = []
        for doc in documents:
            docs.append({
                'title': doc.title,
                'content': doc.content,
                'url': doc.url,
                'source_type': doc.source_type,
                'metadata': doc.metadata
            })
        
        # 导入文档
        knowledge_ids = await service.import_documents(docs)
        
        return {
            'success': True,
            'count': len(knowledge_ids),
            'ids': knowledge_ids
        }
        
    except ProcessingError as e:
        logger.error(f'导入文档失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f'导入文档时发生未知错误: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='导入文档失败'
        )

@router.post('/upload', response_model=Dict[str, str])
async def upload_files(
    *,
    files: List[UploadFile] = File(...),
    chunk_size: Optional[int] = Form(None, description='文档分块大小，默认使用系统配置'),
    chunk_overlap: Optional[int] = Form(None, description='分块重叠大小，默认使用系统配置'),
    domain: Optional[str] = Form(None, description='知识领域，默认使用系统配置的默认领域'),
    db: AsyncSession = Depends(deps.get_db)
) -> Dict[str, str]:
    """
    上传文件到知识库
    
    可以通过chunk_size和chunk_overlap参数自定义文档分块大小和重叠大小，
    通过domain参数指定知识领域
    """
    try:
        # 1. 保存文件
        file_paths = []
        for file in files:
            # 生成唯一文件名
            file_name = f"{datetime.now(UTC).strftime('%Y%m%d%H%M%S')}_{file.filename}"
            file_path = os.path.join(UPLOAD_DIR, file_name)
            
            # 保存文件
            with open(file_path, 'wb') as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            file_paths.append(file_path)
        
        # 2. 创建导入任务
        repository = KnowledgeRepository(db)
        import_repository = ImportTaskRepository(db)
        service = KnowledgeService(repository, import_repository)
        
        task_id = await service.import_documents(
            file_paths=file_paths,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            domain=domain
        )
        
        return {'task_id': task_id}
        
    except Exception as e:
        # 清理上传的文件
        for file_path in file_paths:
            try:
                os.remove(file_path)
            except:
                pass
                
        logger.error(f'上传文件失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'上传文件失败: {str(e)}'
        )

@router.get('/import-tasks/{task_id}', response_model=ImportTaskResponse)
async def get_import_task(
    *,
    db: AsyncSession = Depends(deps.get_db),
    task_id: str
) -> ImportTaskResponse:
    """
    获取导入任务详情
    """
    try:
        repository = KnowledgeRepository(db)
        import_repository = ImportTaskRepository(db)
        service = KnowledgeService(repository, import_repository)
        
        task = await service.get_import_task(task_id)
        
        return {
            'code': 200,
            'message': 'success',
            'data': task,
            'timestamp': datetime.now(UTC).isoformat()
        }
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='导入任务不存在'
        )
    except Exception as e:
        logger.error(f'获取导入任务失败: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'获取导入任务失败: {str(e)}'
        ) 
    
@router.get('/domains/{group_id}', response_model=List[str])
async def get_group_domain(
    *,
    group_id: int
) -> List[str]:
    """
    Get knowledge domains allowed by group_id.
    """
    # 处理特殊情况：group_id为0时，返回COMMON组的权限
    if group_id == 0:
        group_id = GroupId.COMMON.value
    
    # 查找匹配的GroupId枚举
    group_enum = None
    for group in GroupId:
        if group.value == group_id:
            group_enum = group
            break
    
    # 如果找不到匹配的组，返回空列表
    if group_enum is None:
        logger.warning(f'未找到group_id {group_id}对应的组，返回空列表')
        return []
    
    # 获取组对应的域列表，并转换为字符串
    group_domain = GROUP_DOMAINS.get(group_enum, [])
    domain_strings = [domain.value for domain in group_domain]
    
    logger.info(f'group_id: {group_id}, group_enum: {group_enum}, group_domain: {domain_strings}')
    
    return domain_strings