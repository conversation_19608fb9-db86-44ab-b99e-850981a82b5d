# 在 backend/src/api/v1/endpoints/debug.py 中添加

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from ....core import deps
from src.cli.test_search import test_search
from src.schemas.knowledge import KnowledgeSearchRequest

router = APIRouter()

@router.post('/test-search', response_model=Dict[str, Any])
async def debug_test_search(
    *,
    db: AsyncSession = Depends(deps.get_db),
    search_request: KnowledgeSearchRequest
) -> Dict[str, Any]:
    """
    使用CLI工具测试搜索
    """
    try:
        results = await test_search(
            query=search_request.query,
            search_type='vector',
            limit=search_request.limit,
            min_score=search_request.min_score
        )
        
        # 将结果转换为字符串
        result_str = ''
        for i, result in enumerate(results, 1):
            result_str += f'\n--- 结果 {i} ---\n'
            result_str += f'标题: {result.get("title", "N/A")}\n'
            result_str += f'相关度: {result.get("relevance", 0):.4f}\n'
            result_str += f'内容: {result.get("content", "N/A")[:200]}...\n'
            result_str += '-------------------\n'
        
        return {'results': result_str or '未找到结果'}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'测试搜索失败: {str(e)}'
        )