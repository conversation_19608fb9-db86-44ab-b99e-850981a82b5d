# -*- coding: utf-8 -*-
from datetime import datetime
import uuid
from typing import Dict, Any, Optional
from sqlalchemy import Column, Integer, String, DateTime, Float, JSON, Text, ForeignKey, Index, func
from sqlalchemy.dialects.sqlite import <PERSON><PERSON><PERSON> as SQLiteJSON
from sqlalchemy.orm import relationship

from ..core.database import Base
from ..core.constants import KnowledgeDomain
from ..core.id_generator import IDGenerator

class Knowledge(Base):
    """
    知识条目模型
    """
    __tablename__ = 'knowledge'
    
    id = Column(String(50), primary_key=True, index=True, default=IDGenerator.generate_knowledge_id)
    title = Column(String(255), nullable=False, index=True)
    content = Column(Text, nullable=False)
    url = Column(String(512), nullable=True)
    source_type = Column(String(50), nullable=False, default='manual', index=True)  # manual: 手动添加, crawl: 爬虫爬取
    domain = Column(String(50), nullable=False, default=KnowledgeDomain.DEFAULT.value, index=True)  # 知识领域
    meta_info = Column(JSON, nullable=False, default=dict)
    embedding_id = Column(String(100), nullable=True)  # 向量存储中的ID
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    # 关系：一个知识条目有多个知识片段
    chunks = relationship('KnowledgeChunk', back_populates='knowledge', cascade='all, delete-orphan')
    
    # 索引
    __table_args__ = (
        Index('ix_knowledge_title_content', 'title', 'content'),
    )

class KnowledgeChunk(Base):
    """
    知识片段模型，用于存储文档分块后的知识内容
    
    每个知识片段包含：
    - 唯一标识符
    - 关联的知识条目ID
    - 分块内容
    - 元数据信息
    - 向量存储ID
    - 创建和更新时间戳
    """
    __tablename__ = 'knowledge_chunks'
    
    id = Column(String(60), primary_key=True, index=True)  # chunk ID将由程序逻辑生成
    knowledge_id = Column(String(50), ForeignKey('knowledge.id', ondelete='CASCADE'), nullable=False, index=True)
    content = Column(Text, nullable=False)
    meta_info = Column(JSON, nullable=False, default=dict)
    embedding_id = Column(String(100), nullable=True)  # 向量存储中的ID
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    # 关系：每个知识片段属于一个知识条目
    knowledge = relationship('Knowledge', back_populates='chunks')

class KnowledgeSource(Base):
    """
    知识来源模型，记录知识的来源信息
    """
    __tablename__ = 'knowledge_sources'
    
    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    source_type = Column(String(50), nullable=False)  # website, document, api, etc.
    config = Column(JSON, nullable=False, default=dict)  # 爬虫配置或其他配置信息
    last_crawled_at = Column(DateTime, nullable=True)  # 最后一次爬取时间
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False) 