# -*- coding: utf-8 -*-
from sqlalchemy import <PERSON><PERSON>n, Integer, String, Boolean, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from src.core.database import Base

class UserSettings(Base):
    __tablename__ = 'user_settings'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), unique=True)
    theme = Column(String(50), default='light')
    language = Column(String(10), default='zh')
    notification_enabled = Column(Boolean, default=True)

    user = relationship('User', back_populates='settings') 