# -*- coding: utf-8 -*-
from sqlalchemy import Column, String, DateTime, Text, Foreign<PERSON>ey, Integer, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, UTC

from ..core.database import Base

class ChatSession(Base):
    """
    聊天会话模型
    """
    __tablename__ = 'chat_sessions'
    
    id = Column(String, primary_key=True, index=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    title = Column(String)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    meta_data = Column(JSON, nullable=True)
    
    messages = relationship('ChatMessage', back_populates='session', cascade='all, delete-orphan')
    
class ChatMessage(Base):
    """
    聊天消息模型
    """
    __tablename__ = 'chat_messages'
    
    id = Column(String, primary_key=True, index=True)
    session_id = Column(String, ForeignKey('chat_sessions.id'))
    role = Column(String)  # user, assistant, system
    content = Column(Text)
    timestamp = Column(DateTime, default=lambda: datetime.now(UTC))
    meta_data = Column(JSON, nullable=True)
    
    session = relationship('ChatSession', back_populates='messages')
    references = relationship('MessageReference', back_populates='message', cascade='all, delete-orphan')

class MessageReference(Base):
    """
    消息引用模型（用于保存引用的知识条目信息）
    """
    __tablename__ = 'message_references'
    
    id = Column(String, primary_key=True, index=True)
    message_id = Column(String, ForeignKey('chat_messages.id'))
    title = Column(String)
    content = Column(Text)
    url = Column(String, nullable=True)
    relevance = Column(String, nullable=True)  # 相关度分数
    meta_data = Column(JSON, nullable=True)
    
    message = relationship('ChatMessage', back_populates='references') 