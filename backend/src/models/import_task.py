# -*- coding: utf-8 -*-
from datetime import datetime
import uuid
from sqlalchemy import Column, String, DateTime, Integer, JSON, Text, Float
from sqlalchemy.orm import relationship

from ..core.database import Base

class ImportTask(Base):
    """
    导入任务模型
    """
    __tablename__ = 'import_tasks'
    
    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    status = Column(String(20), nullable=False, default='pending')  # pending, processing, completed, failed
    total_files = Column(Integer, nullable=False, default=0)
    processed_files = Column(Integer, nullable=False, default=0)
    failed_files = Column(Integer, nullable=False, default=0)
    progress = Column(Float, nullable=False, default=0.0)  # 0-100
    error_message = Column(Text, nullable=True)
    result = Column(JSON, nullable=True)  # 导入结果，包含成功和失败的文件信息
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    def to_dict(self) -> dict:
        """
        转换为字典
        """
        return {
            'id': self.id,
            'status': self.status,
            'total_files': self.total_files,
            'processed_files': self.processed_files,
            'failed_files': self.failed_files,
            'progress': self.progress,
            'error_message': self.error_message,
            'result': self.result,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        } 