# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Enum
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class KnowledgeSource(Base):
    __tablename__ = 'knowledge_sources'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    url = Column(String(1024), nullable=False)
    source_type = Column(String(50), nullable=False)  # web_page, api_doc, git_repo
    content_hash = Column(String(32))  # MD5哈希值
    last_check_time = Column(DateTime, default=datetime.now)
    last_update_time = Column(DateTime, default=datetime.now)
    update_frequency = Column(Integer, default=3600)  # 更新频率（秒）
    status = Column(String(20), default='active')  # active, inactive, error
    error_message = Column(Text)
    
    def __repr__(self):
        return f"<KnowledgeSource(id={self.id}, name='{self.name}', type='{self.source_type}')>" 