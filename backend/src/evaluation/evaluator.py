import json
import asyncio
from typing import List, Dict, Any, Set

from ..services.rag.retrievers import HybridRetriever

from .metrics import (
    calculate_recall_at_k,
    calculate_ndcg_at_k,
    calculate_mrr,
    calculate_doc_recall_at_k
)

from ..core.logging import get_cli_logger
logger = get_cli_logger("evaluator")

class RAGEvaluator:
    def __init__(self, knowledge_repository):
        self.knowledge_repository = knowledge_repository
        self.retriever = HybridRetriever(self.knowledge_repository)

    async def evaluate(self, test_queries_file: str, k_values: List[int] = [5, 10, 20]):
        """执行评测"""
        # 1. 加载测试查询
        with open(test_queries_file, "r", encoding="utf-8") as f:
            queries = json.load(f)
        
        # 2. 初始化结果
        results = {
            "per_query": [],
            "overall": {f"recall@{k}": 0.0 for k in k_values}
        }
        
        # 添加其他指标
        for k in k_values:
            results["overall"][f"ndcg@{k}"] = 0.0
            results["overall"][f"doc_recall@{k}"] = 0.0
        results["overall"]["mrr"] = 0.0
        
        # 3. 处理每个查询
        for query in queries:
            query_id = query["query_id"]
            query_text = query["query_content"]
            
            # 获取标注的相关块
            relevant_chunks = query["related_chunks"]
            relevant_chunk_ids = {chunk["chunk_id"] for chunk in relevant_chunks}
            relevant_doc_ids = {chunk["knowledge_id"] for chunk in relevant_chunks}
            
            # 创建相关度映射
            relevance_map = {chunk["chunk_id"]: chunk["relevance"] for chunk in relevant_chunks}
            
            # 执行检索
            retrieved_results = await self.retriever.retrieve(
                query=query_text,
                limit=max(k_values),
                min_score=0.0  # 获取所有结果用于评测
            )
            
            # 提取检索到的块ID和文档ID
            retrieved_chunk_ids = []
            retrieved_doc_ids = []
            
            for result in retrieved_results:
                if result["type"] == "chunk":
                    retrieved_chunk_ids.append(result["id"])
                    if "knowledge_id" in result:
                        retrieved_doc_ids.append(result["knowledge_id"])
                elif result["type"] == "document":
                    # 对于文档类型的结果，使用文档ID作为chunk_id
                    retrieved_chunk_ids.append(result["id"])
                    retrieved_doc_ids.append(result["id"])
            logger.info(f"retrieved_chunk_ids: {retrieved_chunk_ids}")
            logger.info(f"relevant_chunk_ids: {relevant_chunk_ids}")
            # 计算查询级指标
            query_metrics = {}
            
            # 对每个k值计算指标
            for k in k_values:
                # 召回率
                recall = calculate_recall_at_k(retrieved_chunk_ids, relevant_chunk_ids, k)
                query_metrics[f"recall@{k}"] = recall
                results["overall"][f"recall@{k}"] += recall
                
                # NDCG
                ndcg = calculate_ndcg_at_k(retrieved_chunk_ids, relevance_map, k)
                query_metrics[f"ndcg@{k}"] = ndcg
                results["overall"][f"ndcg@{k}"] += ndcg
                
                # 文档召回率
                doc_recall = calculate_doc_recall_at_k(retrieved_doc_ids, relevant_doc_ids, k)
                query_metrics[f"doc_recall@{k}"] = doc_recall
                results["overall"][f"doc_recall@{k}"] += doc_recall
            
            # MRR
            mrr = calculate_mrr(retrieved_chunk_ids, relevant_chunk_ids)
            query_metrics["mrr"] = mrr
            results["overall"]["mrr"] += mrr
            
            # 保存查询结果
            results["per_query"].append({
                "query_id": query_id,
                "query": query_text,
                "metrics": query_metrics
            })
        
        # 4. 计算平均值
        query_count = len(queries)
        for metric in results["overall"]:
            results["overall"][metric] /= query_count
        
        return results
