import json
import os
import random
from typing import List, Dict, Any
import re
from loguru import logger
from ..repositories.knowledge_repository import KnowledgeRepository
from ..core.database import get_db
from ..services.rag.retrievers import HybridRetriever

def load_data(data_dir: str) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """加载知识库数据"""
    docs_file = os.path.join(data_dir, "knowledge_docs.json")
    chunks_file = os.path.join(data_dir, "chunks_data.json")
    
    if not os.path.exists(docs_file):
        raise FileNotFoundError(f"找不到文档文件: {docs_file}")
    if not os.path.exists(chunks_file):
        raise FileNotFoundError(f"找不到分块文件: {chunks_file}")
    
    with open(docs_file, "r", encoding="utf-8") as f:
        docs = json.load(f)
    
    with open(chunks_file, "r", encoding="utf-8") as f:
        chunks = json.load(f)
    
    return docs, chunks

def extract_key_sentences(text: str, max_sentences: int = 3) -> List[str]:
    """从文本中提取关键句子作为查询"""
    # 使用正则表达式分割句子
    sentences = re.split(r'[。！？!?]', text)
    # 过滤空句子和过短的句子
    sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
    # 随机选择句子
    return random.sample(sentences, min(max_sentences, len(sentences)))

async def get_related_chunks(query: str, knowledge_repository: KnowledgeRepository, limit: int = 10) -> List[Dict[str, Any]]:
    """使用向量搜索获取相关分块"""
    try:
    # 创建混合检索器
        hybrid_retriever = HybridRetriever(
            knowledge_repository=knowledge_repository
        )
        
        # 执行混合检索
        logger.info("\n=== 混合检索结果 ===")
        results = await hybrid_retriever.retrieve(
            query=query, 
            limit=limit, 
            min_score=0.3
        )
        
        # 格式化结果
        related_chunks = []
        for result in results:
            if result["type"] == "chunk":
                related_chunks.append({
                    "chunk_id": result["id"],
                    "knowledge_id": result["knowledge_id"],
                    "content": result["content"],
                    "relevance": 0  # 初始相关度为0，等待人工标注
                })
        
        return related_chunks
    except Exception as e:
        logger.error(f"搜索相关分块失败: {str(e)}")
        return []

async def generate_queries(knowledge_repository: KnowledgeRepository, manual_queries: List[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
    """生成测试查询"""
    queries = []
    
    if manual_queries:
        # 使用手动输入的查询
        for i, query_text in enumerate(manual_queries):
            # 获取相关分块
            related_chunks = await get_related_chunks(query_text, knowledge_repository, limit)
            
            # 创建查询
            query = {
                "query_id": f"q{i+1}",
                "query_content": query_text,
                "related_chunks": related_chunks
            }
            
            queries.append(query)
    
    return queries 