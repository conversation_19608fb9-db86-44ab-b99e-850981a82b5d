import numpy as np
from typing import List, Dict, Set, Union

def calculate_recall_at_k(retrieved_ids: List[str], relevant_ids: Set[str], k: int) -> float:
    """计算Recall@k"""
    if not relevant_ids:
        return 1.0
    
    retrieved_k = set(retrieved_ids[:k])
    relevant_retrieved = retrieved_k.intersection(relevant_ids)
    return len(relevant_retrieved) / len(relevant_ids)

def calculate_ndcg_at_k(retrieved_ids: List[str], relevance_map: Dict[str, Union[int, float]], k: int) -> float:
    """计算NDCG@k"""
    if not relevance_map:
        return 1.0
    
    # 计算DCG@k
    dcg = 0
    for i, chunk_id in enumerate(retrieved_ids[:k]):
        if chunk_id in relevance_map:
            rel = relevance_map[chunk_id]
            dcg += (2 ** rel - 1) / np.log2(i + 2)
    
    # 计算IDCG@k
    sorted_rels = sorted(relevance_map.values(), reverse=True)
    idcg = 0
    for i, rel in enumerate(sorted_rels[:k]):
        idcg += (2 ** rel - 1) / np.log2(i + 2)
    
    return dcg / idcg if idcg > 0 else 0

def calculate_mrr(retrieved_ids: List[str], relevant_ids: Set[str]) -> float:
    """计算MRR"""
    if not relevant_ids:
        return 1.0
    
    for i, chunk_id in enumerate(retrieved_ids):
        if chunk_id in relevant_ids:
            return 1.0 / (i + 1)
    
    return 0.0

def calculate_doc_recall_at_k(retrieved_doc_ids: List[str], relevant_doc_ids: Set[str], k: int) -> float:
    """计算文档召回率"""
    if not relevant_doc_ids:
        return 1.0
    
    retrieved_k = set(retrieved_doc_ids[:k])
    relevant_retrieved = retrieved_k.intersection(relevant_doc_ids)
    return len(relevant_retrieved) / len(relevant_doc_ids)
