# 评测流程

## 1. 导出知识库数据

首先需要导出知识库数据，生成必要的文件：

```bash
cd backend
python -m src.evaluation.run_eval --export
```

这将生成以下文件：
- `data/evaluation/knowledge_docs.json`: 知识库文档数据
- `data/evaluation/chunks_data.json`: 知识分块数据

## 2. 生成测试查询

有两种方式生成测试查询：

1. 自动生成（从文档中提取关键句子）：
```bash
python -m src.evaluation.run_eval --generate
```

2. 手动输入查询：
```bash
python -m src.evaluation.run_eval --generate --manual
```
然后按提示输入查询，每行一个，输入空行结束。

## 3. 标注相关度

生成测试查询后，需要人工标注相关度：

1. 打开 `data/evaluation/test_queries.json` 文件
2. 对于每个查询：
   - 查看查询内容和自动生成的相关分块
   - 检查每个相关分块的内容，修改相关度：
     * `relevance: 2` 表示高度相关
     * `relevance: 1` 表示相关
     * `relevance: 0` 表示不相关
   - 可以添加或删除相关分块

## 4. 运行评测

完成标注后，运行评测：

```bash
python -m src.evaluation.run_eval --evaluate
```

评测结果将保存在 `data/evaluation/results` 目录下：
- `evaluation_results_[timestamp].json`: 详细评测结果
- `evaluation_summary_[timestamp].json`: 评测摘要

# 评测指标说明

- **Recall@K**: 在前K个检索结果中，相关文档的比例
- **NDCG@K**: 归一化折损累积增益，考虑相关度的排序质量
- **MRR**: 平均倒数排名，第一个相关文档排名的倒数平均值

# 注意事项

1. 运行顺序：导出数据 -> 生成查询 -> 标注相关度 -> 运行评测
2. 确保数据库连接正常
3. 标注相关度时请仔细检查内容
4. 评测结果会自动保存，建议定期备份

# 常见问题

1. **找不到模块错误**
   - 确保在正确的目录下运行脚本
   - 使用 `python -m` 方式运行

2. **数据库连接错误**
   - 检查环境变量设置
   - 确认数据库服务是否运行

3. **文件保存失败**
   - 检查目录权限
   - 确保磁盘空间充足