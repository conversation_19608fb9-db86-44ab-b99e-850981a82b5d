"""
RAG评测运行脚本
"""

import asyncio
import argparse
import json
import os
from datetime import datetime
from loguru import logger
import os
# 设置环境变量，如果需要从其他环境变量导出数据，就把这行注释掉
os.environ["NKRA_ENV"] = "eval"

# 确保配置在导入其他模块前加载
from ..core.config_loader import load_configurations
load_configurations()

from ..core.database import get_db
from ..core.config import settings
from ..repositories.knowledge_repository import KnowledgeRepository
from ..services.knowledge_service import KnowledgeService
from .data_exporter import DataExporter
from .data_importer import DataImporter
from .evaluator import RAGEvaluator

# 从 generate_test_queries 模块导入需要的函数
from .generate_test_queries import generate_queries



async def get_database():
    """获取数据库连接（异步）
    返回 (db_session, db_generator) ，调用方在使用完毕后负责关闭两者
    """
    db_gen = get_db()
    db_session = await anext(db_gen)  # 先获取会话，不要立即关闭生成器
    return db_session, db_gen

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RAG评测工具")
    parser.add_argument("--export", action="store_true", help="导出知识库数据")
    parser.add_argument("--export-dir", type=str, default=None, help="导出数据的目录路径（默认使用环境对应的evaluation目录）")
    
    parser.add_argument("--import", dest="import_data", action="store_true", help="从JSON文件导入知识库数据")
    parser.add_argument("--import-dir", type=str, default=None, help="导入数据的目录路径（默认使用环境对应的evaluation目录）")
    parser.add_argument("--clear-existing", action="store_true", help="导入前清空现有数据")

    parser.add_argument("--generate", action="store_true", help="生成测试查询")
    parser.add_argument("--manual", action="store_true", help="手动输入查询")
    parser.add_argument("--question_file", type=str, default=None, help="问题文件路径")

    parser.add_argument("--queries", type=str, help="测试查询文件路径")
    
    parser.add_argument("--evaluate", action="store_true", help="运行评测")
    
    args = parser.parse_args()
    
    if not (args.export or args.import_data or args.evaluate or args.generate):
        parser.print_help()
        return
    
    # 获取数据库连接 (异步)
    db, db_gen = await get_database()
    try:
        # evaluation环境通过NKRA_ENV=eval自动使用独立的存储路径和索引
        knowledge_repository = KnowledgeRepository(db)
        knowledge_service = KnowledgeService(knowledge_repository)
        
        if args.export:
            # 导出数据
            logger.info("正在导出知识库数据...")
            if args.export_dir is None:
                args.export_dir = os.path.join(settings.DATA_DIR, "evaluation")
                logger.info(f"未指定导出目录，使用环境默认目录: {args.export_dir}")
            exporter = DataExporter(knowledge_repository, output_dir=args.export_dir)
            files = await exporter.export_data()
            logger.info(f"数据已导出到: {files}")
        
        if args.import_data:
            # 确定导入目录 - 如果未指定则使用环境对应的evaluation目录
            import_dir = args.import_dir
            if import_dir is None:
                import_dir = os.path.join(settings.DATA_DIR, "evaluation")
                logger.info(f"未指定导入目录，使用环境默认目录: {import_dir}")
            
            # 导入数据
            logger.info(f"正在从 {import_dir} 导入知识库数据...")
            if args.clear_existing:
                logger.warning("将清空现有数据！")
            
            importer = DataImporter(knowledge_repository, knowledge_service)
            result = await importer.import_data(
                data_dir=import_dir,
                clear_existing=args.clear_existing
            )
            
            logger.info(f"导入完成: 成功 {result['success_count']} 个文档，失败 {result['failed_count']} 个")
            logger.info(f"总计处理 {result['total_docs']} 个文档，{result['total_chunks']} 个分块")
        
        if args.generate:
            # 生成查询
            if args.manual:
                print("\n请输入测试查询（输入空行结束）：")
                manual_queries = []
                while True:
                    query = input("> ").strip()
                    if not query:
                        break
                    manual_queries.append(query)
                
                if not manual_queries:
                    logger.error("没有输入任何查询")
                    return
                
                logger.info(f"输入了 {len(manual_queries)} 个查询")
                queries = await generate_queries(knowledge_repository, manual_queries)
            elif args.question_file:
                with open(args.question_file, "r", encoding="utf-8") as f:
                    manual_queries = f.readlines()
                manual_queries = [query.strip() for query in manual_queries if query.strip()]
                logger.info(f"从 {args.question_file} 中读取了 {len(manual_queries)} 个查询")
                queries = await generate_queries(knowledge_repository, manual_queries, limit=10)
            else:
                print("请使用 --manual 或 --question_file 参数生成测试查询")
                return
            
            # 保存查询
            queries_file = os.path.join(settings.DATA_DIR, "evaluation", "test_queries.json")
            os.makedirs(os.path.dirname(queries_file), exist_ok=True)
            try:
                with open(queries_file, "w", encoding="utf-8") as f:
                    json.dump(queries, f, ensure_ascii=False, indent=2)
                logger.info(f"测试查询已生成并保存到: {queries_file}")
                
                # 验证文件是否成功写入
                if os.path.exists(queries_file):
                    with open(queries_file, "r", encoding="utf-8") as f:
                        saved_queries = json.load(f)
                        logger.info(f"验证: 文件包含 {len(saved_queries)} 个查询")
                else:
                    logger.error("文件保存失败: 文件不存在")
            except Exception as e:
                logger.error(f"保存文件时出错: {str(e)}")
                raise
            
            print("\n请按照以下步骤进行人工标注：")
            print("1. 打开 test_queries.json 文件")
            print("2. 对于每个查询：")
            print("   - 查看查询内容和自动生成的相关分块")
            print("   - 检查每个相关分块的内容，修改相关度：")
            print("     * relevance: 2 表示高度相关")
            print("     * relevance: 1 表示相关")
            print("     * relevance: 0 表示不相关")
            print("   - 可以添加或删除相关分块")
        
        if args.evaluate:
            # 运行评测
            logger.info("开始RAG系统评测...")
            evaluator = RAGEvaluator(knowledge_repository)
            
            # 使用指定的查询文件或默认文件
            queries_file = args.queries or os.path.join(settings.DATA_DIR, "evaluation", "test_queries.json")
            
            if not os.path.exists(queries_file):
                logger.error(f"错误: 找不到测试查询文件: {queries_file}")
                logger.error("请先运行 --export 导出数据并创建测试查询")
                return
            
            results = await evaluator.evaluate(queries_file)
            
            # 打印总体结果
            print("\n评测结果摘要:")
            for metric, value in results["overall"].items():
                print(f"  {metric}: {value:.4f}")
            
            # 保存评测结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_dir = os.path.join(settings.DATA_DIR, "evaluation", "results")
            os.makedirs(results_dir, exist_ok=True)
            
            # 保存详细结果
            detailed_file = os.path.join(results_dir, f"evaluation_results_{timestamp}.json")
            with open(detailed_file, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            # 保存摘要结果
            summary_file = os.path.join(results_dir, f"evaluation_summary_{timestamp}.json")
            with open(summary_file, "w", encoding="utf-8") as f:
                json.dump({
                    "timestamp": timestamp,
                    "overall_metrics": results["overall"],
                    "query_count": len(results["per_query"])
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"\n详细评测结果已保存到: {detailed_file}")
            logger.info(f"评测摘要已保存到: {summary_file}")
    finally:
        # 先关闭数据库会话，然后再关闭生成器以触发其清理逻辑
        try:
            await db.close()
        finally:
            try:
                await db_gen.aclose()
            except Exception:
                pass

if __name__ == "__main__":
    asyncio.run(main())
