import json
import os
import asyncio
from typing import Dict, Any, List
from loguru import logger
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.knowledge_service import KnowledgeService

class DataImporter:
    def __init__(self, knowledge_repository: KnowledgeRepository, knowledge_service: KnowledgeService = None):
        self.knowledge_repository = knowledge_repository
        self.knowledge_service = knowledge_service
        
    async def import_data(self, data_dir: str = "data/evaluation", clear_existing: bool = False):
        """从JSON文件导入知识库数据
        
        Args:
            data_dir: 数据目录路径
            clear_existing: 是否清空现有数据
        """
        docs_file = os.path.join(data_dir, "knowledge_docs.json")
        chunks_file = os.path.join(data_dir, "chunks_data.json")
        
        # 检查文件是否存在
        if not os.path.exists(docs_file):
            raise FileNotFoundError(f"找不到文档文件: {docs_file}")
        if not os.path.exists(chunks_file):
            raise FileNotFoundError(f"找不到分块文件: {chunks_file}")
        
        # 读取数据
        with open(docs_file, "r", encoding="utf-8") as f:
            docs_data = json.load(f)
        
        with open(chunks_file, "r", encoding="utf-8") as f:
            chunks_data = json.load(f)
        
        logger.info(f"准备导入 {len(docs_data)} 个文档和 {len(chunks_data)} 个分块")
        
        # 如果需要清空现有数据
        if clear_existing:
            logger.warning("正在清空现有知识库数据...")
            await self._clear_all_data()
        
        # 按knowledge_id组织分块数据
        chunks_by_doc = {}
        for chunk in chunks_data:
            knowledge_id = chunk["knowledge_id"]
            if knowledge_id not in chunks_by_doc:
                chunks_by_doc[knowledge_id] = []
            chunks_by_doc[knowledge_id].append(chunk)
        
        # 批量导入文档
        success_count = 0
        failed_count = 0
        
        for doc in docs_data:
            try:
                knowledge_id = doc["knowledge_id"]
                doc_chunks = chunks_by_doc.get(knowledge_id, [])
                
                # 准备分块数据，保留所有原始字段并确保向量重新生成
                formatted_chunks = []
                for chunk in sorted(doc_chunks, key=lambda x: x.get("index", 0)):
                    # 保留完整的metadata，确保所有字段都被保存
                    chunk_metadata = chunk.get("metadata", {})
                    
                    # 如果metadata是字符串，尝试解析为字典
                    if isinstance(chunk_metadata, str):
                        try:
                            chunk_metadata = json.loads(chunk_metadata)
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析chunk metadata: {chunk_metadata}")
                            chunk_metadata = {}
                    
                    formatted_chunk = {
                        "content": chunk["content"],
                        "metadata": chunk_metadata,
                        # 明确不提供vector，强制重新生成
                        "vector": None,
                        "id": chunk.get("chunk_id")
                    }
                    
                    formatted_chunks.append(formatted_chunk)
                
                # 添加到知识库，使用原始字段信息
                knowledge_id = await self.knowledge_repository.add_knowledge(
                    title=doc["title"],
                    content=doc["content"],
                    url=doc.get("url"),
                    source_type=doc.get("source_type", "imported"),
                    domain=doc.get("domain", "DEFAULT"),
                    metadata=doc.get("metadata", {}),
                    chunks=formatted_chunks,
                    id=doc.get("knowledge_id")
                )
                
                logger.info(f"成功导入文档: {doc['title']} (ID: {knowledge_id}), 包含 {len(formatted_chunks)} 个分块")
                logger.debug(f"向量将存储到 evaluation 专用路径")
                success_count += 1
                
            except Exception as e:
                logger.error(f"导入文档失败 {doc['title']}: {str(e)}")
                failed_count += 1
                continue
        
        logger.info(f"导入完成: 成功 {success_count} 个文档，失败 {failed_count} 个")
        logger.info(f"所有文档和分块的向量嵌入已重新生成")
        logger.info(f"Elasticsearch 索引已重新建立")
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_docs": len(docs_data),
            "total_chunks": len(chunks_data)
        }
    
    async def _clear_all_data(self):
        """清空所有知识库数据（谨慎使用）"""
        try:
            if self.knowledge_service:
                logger.warning("正在清空所有知识库数据...")
                count = await self.knowledge_service.delete_all_knowledge()
                logger.info(f"已删除 {count} 条知识数据")
            else:
                logger.warning("无法清空数据：knowledge_service 未提供")
        except Exception as e:
            logger.error(f"清空数据失败: {str(e)}")
            raise
    
    async def import_from_backup(self, backup_path: str):
        """从备份文件导入数据
        
        Args:
            backup_path: 备份文件路径或目录路径
        """
        if os.path.isdir(backup_path):
            # 如果是目录，查找标准文件名
            return await self.import_data(backup_path)
        else:
            # 如果是单个文件，假设是压缩包或其他格式
            raise NotImplementedError("暂不支持压缩包格式的导入") 