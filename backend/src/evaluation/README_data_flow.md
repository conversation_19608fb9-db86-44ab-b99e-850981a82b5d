# 知识库数据导出导入流程

## 概述

这个工具提供了完整的知识库数据导出和导入功能，支持将数据库中的所有知识文档和分块数据导出为JSON文件，并能重新导入到另一个数据库中。

## 工作流程

### 1. 导出数据

从当前数据库导出所有知识库数据：

```bash
cd backend
python -m src.evaluation.run_eval --export --export-dir /dir/to/save/json/files
```


### 2. 数据文件结构

**knowledge_docs.json 格式：**
```json
[
  {
    "knowledge_id": "知识条目ID",
    "title": "文档标题",
    "content": "完整文档内容",
    "url": "文档URL",
    "source_type": "来源类型",
    "domain": "知识领域",
    "metadata": {
      "summary": "文档摘要",
      "其他元数据": "..."
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
  }
]
```

**chunks_data.json 格式：**
```json
[
  {
    "chunk_id": "分块ID",
    "knowledge_id": "所属知识条目ID",
    "content": "分块内容",
    "metadata": {
      "chunk_index": 0,
      "total_chunks": 5,
      "chunk_type": "text",
      "title": "文档标题",
      "其他元数据": "..."
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
  }
]
```

### 3. 导入数据（建议设置NKRA_ENV=eval）

将导出的数据导入到数据库中：

```bash
# 基本导入（追加到现有数据）
python -m src.evaluation.run_eval --import

# 指定数据目录
python -m src.evaluation.run_eval --import --import-dir /path/to/data

# 清空现有数据后导入（谨慎使用！）
python -m src.evaluation.run_eval --import --clear-existing

# 全量覆盖导入指定数据目录<用这条就行>
python -m src.evaluation.run_eval --import --import-dir /path/to/data --clear-existing

```

### 4. 用于标注的数据生成（建议设置NKRA_ENV=eval）
```bash
# 3. 生成测试查询 会生成一个用于标注的json文件
python -m src.evaluation.run_eval --generate --question_file question/file/name.txt
```
接下来需要对该文件人工标注，得到一个queries.json

### 5. 运行评测（建议设置NKRA_ENV=eval）
运行测评的时候环境需要有数据
这里的queries.json
```bash
python -m src.evaluation.run_eval --evaluate --queries /input/queries.json
```

## 文件说明

- `data_exporter.py` - 数据导出器
- `data_importer.py` - 数据导入器  
- `run_eval.py` - 统一命令行入口
- `README_data_flow.md` - 本说明文档 