import json
import os
import asyncio
from typing import Dict, Any
from src.repositories.knowledge_repository import KnowledgeRepository

class DataExporter:
    def __init__(self, knowledge_repository: KnowledgeRepository, output_dir: str = "data/evaluation"):
        self.knowledge_repository = knowledge_repository
        self.output_dir = output_dir
        
    async def export_data(self):
        """导出知识库数据到文件"""
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 1. 获取所有知识条目
        all_knowledge = await self.knowledge_repository.get_all_knowledge(page=1, page_size=10000)
        
        # 2. 导出文档数据
        docs_data = []
        for item in all_knowledge["items"]:
            # 获取完整的知识条目内容
            knowledge = await self.knowledge_repository.get_knowledge_by_id(item["id"])
            if not knowledge:
                print(f"警告: 无法获取知识条目 {item['id']} 的完整内容，跳过")
                continue
                
            # print(knowledge)
            # 获取元数据，处理字段名不一致的情况
            doc_metadata = knowledge.get('metadata', {}) or knowledge.get('meta_info', {})
                
            docs_data.append({
                "knowledge_id": knowledge["id"],
                "title": knowledge["title"],
                "content": knowledge["content"],
                "url": knowledge.get("url"),
                "source_type": knowledge.get("source_type", "manual"),
                "domain": knowledge.get("domain", "DEFAULT"),
                "metadata": doc_metadata,
                "created_at": knowledge.get("created_at"),
                "updated_at": knowledge.get("updated_at")
            })
        
        if not docs_data:
            raise ValueError("没有找到有效的知识条目，请检查数据库内容")
        
        docs_file = os.path.join(self.output_dir, "knowledge_docs.json")
        with open(docs_file, "w", encoding="utf-8") as f:
            json.dump(docs_data, f, ensure_ascii=False, indent=2)
        
        # 3. 导出分块数据
        chunks_data = []
        for knowledge in docs_data:
            # 获取完整的知识条目内容（包含分块）
            full_knowledge = await self.knowledge_repository.get_knowledge_by_id(knowledge["knowledge_id"])
            if not full_knowledge or "chunks" not in full_knowledge:
                print(f"警告: 无法获取知识条目 {knowledge['knowledge_id']} 的分块数据，跳过")
                continue
                
            for chunk in full_knowledge["chunks"]:
                # 获取元数据
                chunk_metadata = chunk.get('metadata', {})
                if isinstance(chunk_metadata, str):
                    try:
                        chunk_metadata = json.loads(chunk_metadata)
                    except json.JSONDecodeError:
                        chunk_metadata = {}
                elif not isinstance(chunk_metadata, dict):
                    chunk_metadata = {}
                
                # 保存chunk的所有原始字段
                chunk_data = {
                    "chunk_id": chunk["id"],
                    "knowledge_id": knowledge["knowledge_id"],
                    "content": chunk["content"],
                    "metadata": chunk_metadata,
                    "created_at": chunk.get("created_at"),
                    "updated_at": chunk.get("updated_at")
                }
                
                chunks_data.append(chunk_data)
        
        if not chunks_data:
            raise ValueError("没有找到有效的分块数据，请检查数据库内容")
        
        chunks_file = os.path.join(self.output_dir, "chunks_data.json")
        with open(chunks_file, "w", encoding="utf-8") as f:
            json.dump(chunks_data, f, ensure_ascii=False, indent=2)
        
        return {
            "docs_file": docs_file,
            "chunks_file": chunks_file
        }
