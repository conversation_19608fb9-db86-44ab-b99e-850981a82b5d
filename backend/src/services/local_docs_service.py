# -*- coding: utf-8 -*-
from typing import List, Dict, Any, Optional
import os
from pathlib import Path
from ..core.logging import get_module_logger
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..core.document_parser import ParserFactory
from ..repositories.knowledge_repository import KnowledgeRepository
from ..repositories.import_task_repository import ImportTaskRepository
from ..core.exceptions import ProcessingError
from ..services.knowledge_service import KnowledgeService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class LocalDocsService:
    """
    本地文档处理服务
    """
    def __init__(
        self,
        knowledge_repository: KnowledgeRepository,
        import_task_repository: Optional[ImportTaskRepository] = None,
        docs_dir: str = 'data/local_docs',
        domain: str = None
    ):
        """
        初始化本地文档处理服务
        
        Args:
            knowledge_repository: 知识库仓库实例
            import_task_repository: 导入任务仓库实例
            docs_dir: 本地文档目录
        """
        self.knowledge_repository = knowledge_repository
        self.import_task_repository = import_task_repository
        self.docs_dir = docs_dir
        self._executor = ThreadPoolExecutor(max_workers=4)
        self.knowledge_service = KnowledgeService(knowledge_repository, import_task_repository)
        self.domain = domain

    async def _walk_directory(self, directory: str) -> List[str]:
        """
        异步遍历目录
        """
        loop = asyncio.get_running_loop()
        files = []
        def _walk():
            for root, _, filenames in os.walk(directory):
                for filename in filenames:
                    files.append(os.path.join(root, filename))
            return files
        return await loop.run_in_executor(self._executor, _walk)
    async def _get_file_size(self, file_path: str) -> int:
        """
        异步获取文件大小
        """
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(self._executor, os.path.getsize, file_path)
    async def scan_docs(self) -> List[Dict[str, Any]]:
        """
        扫描本地文档目录
        
        Returns:
            文档列表，每个文档包含路径和基本信息
        """
        docs = []
        try:
            files = await self._walk_directory(self.docs_dir)
            for file_path in files:
                rel_path = os.path.relpath(file_path, self.docs_dir)
                _, ext = os.path.splitext(file_path)
                parser = ParserFactory.get_parser(file_path)
                file_size = await self._get_file_size(file_path)
                if parser:
                    docs.append({
                        'file_path': file_path,
                        'relative_path': rel_path,
                        'file_type': ext.lstrip('.'),
                        'file_size': file_size,
                        'supported': True
                    })
                else:
                    logger.warning(f'Unsupported file type: {file_path}')
                    docs.append({
                        'file_path': file_path,
                        'relative_path': rel_path,
                        'file_type': ext.lstrip('.'),
                        'file_size': file_size,
                        'supported': False
                    })
            return docs
        except Exception as e:
            logger.error(f'Error scanning docs directory: {str(e)}')
            raise ProcessingError(f'扫描文档目录失败: {str(e)}')
    async def import_doc(self, file_path: str) -> str:
        """
        导入单个文档
        
        Args:
            file_path: 文档路径
            
        Returns:
            知识条目ID
        """
        try:
            doc = await ParserFactory.async_parse(file_path)
            metadata = {
                **doc['metadata'],
                'file_path': file_path,
                'relative_path': os.path.relpath(file_path, self.docs_dir)
            }
            knowledge_id = await self.knowledge_service.process_document(
                title=doc['title'],
                content=doc['content'],
                source_type='local_import',
                url=doc.get('url', None),
                metadata=metadata,
                domain=self.domain
            )
            return knowledge_id
        except Exception as e:
            logger.error(f'Error importing document {file_path}: {str(e)}')
            raise ProcessingError(f'导入文档失败: {str(e)}')
    async def import_docs(self, file_paths: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        批量导入文档
        
        Args:
            file_paths: 要导入的文档路径列表，如果为None则导入所有支持的文档
            
        Returns:
            导入结果统计
        """
        task_id = None
        try:
            if not file_paths:
                docs = await self.scan_docs()
                file_paths = [doc['file_path'] for doc in docs if doc['supported']]
            if self.import_task_repository:
                task = await self.import_task_repository.create_task(
                    total_files=len(file_paths)
                )
                task_id = task.id
            results = {
                'success': [],
                'failed': [],
                'total': len(file_paths),
                'task_id': task_id
            }
            for file_path in file_paths:
                try:
                    knowledge_id = await self.import_doc(file_path)
                    results['success'].append({
                        'file_path': file_path,
                        'knowledge_id': knowledge_id
                    })
                    if self.import_task_repository and task_id:
                        progress = (len(results['success']) + len(results['failed'])) / len(file_paths) * 100
                        await self.import_task_repository.update_task(
                            task_id=task_id,
                            processed_files=len(results['success']),
                            failed_files=len(results['failed']),
                            progress=progress
                        )
                except Exception as e:
                    results['failed'].append({
                        'file_path': file_path,
                        'error': str(e)
                    })
                    logger.error(f'Failed to import {file_path}: {str(e)}')
                    if self.import_task_repository and task_id:
                        progress = (len(results['success']) + len(results['failed'])) / len(file_paths) * 100
                        await self.import_task_repository.update_task(
                            task_id=task_id,
                            processed_files=len(results['success']),
                            failed_files=len(results['failed']),
                            progress=progress
                        )
            if self.import_task_repository and task_id:
                status = 'completed' if not results['failed'] else 'completed_with_errors'
                await self.import_task_repository.update_task(
                    task_id=task_id,
                    status=status,
                    result=results
                )
            return results
        except Exception as e:
            logger.error(f'Error in batch import: {str(e)}')
            if self.import_task_repository and task_id:
                await self.import_task_repository.update_task(
                    task_id=task_id,
                    status='failed',
                    error_message=str(e)
                )
            raise ProcessingError(f'批量导入文档失败: {str(e)}') 