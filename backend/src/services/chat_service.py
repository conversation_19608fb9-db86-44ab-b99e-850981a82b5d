# -*- coding: utf-8 -*-
from typing import List, Optional, Dict, Any, AsyncGenerator, Union, AsyncIterator

from ..core.logging import get_module_logger
from ..core.exceptions import DatabaseError, LLMServiceError
from ..repositories.chat_repository import ChatRepository
from ..repositories.knowledge_repository import KnowledgeRepository
from ..services.llm_service_factory import LLMServiceFactory
from ..core.config import settings
from .rag import (
    RAGService,
    VectorRetriever,
    KeywordRetriever,
    MultiQueryRetriever,
    LLMGenerator,
    RelevanceEvaluator
)

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class ChatService:
    def __init__(
        self,
        chat_repository: ChatRepository,
        knowledge_repository: KnowledgeRepository,
        llm_service = None,
        use_multi_query: bool = None  # 是否启用多查询检索
    ):
        """
        初始化聊天服务
        
        Args:
            chat_repository: 聊天记录仓库
            knowledge_repository: 知识库仓库
            llm_service: LLM 服务实例（可选）
            use_multi_query: 是否启用多查询优化，如果为None则使用配置值
        """
        self.chat_repository = chat_repository
        self.knowledge_repository = knowledge_repository
        
        # 从配置中获取多查询设置
        if use_multi_query is None:
            use_multi_query = settings.USE_MULTI_QUERY
        self.use_multi_query = use_multi_query
        
        # 创建 LLM 服务
        self.llm_service = llm_service or LLMServiceFactory.create_service()
        
        # 创建基础检索器
        self.vector_retriever = VectorRetriever(knowledge_repository)
        self.keyword_retriever = KeywordRetriever(knowledge_repository)
        
        # 创建专用于变体生成的LLM服务实例
        self.variant_llm_service = LLMServiceFactory.create_variant_service()
        
        # 创建多查询检索器（使用向量检索器作为基础）
        multi_query_count = settings.MULTI_QUERY_COUNT
        multi_query_cache_size = settings.MULTI_QUERY_CACHE_SIZE
        multi_query_cache_ttl = settings.MULTI_QUERY_CACHE_TTL
        
        self.multi_query_retriever = MultiQueryRetriever(
            base_retriever=self.vector_retriever,
            llm_service=self.llm_service,
            variant_llm_service=self.variant_llm_service,  # 添加专用变体服务
            num_queries=multi_query_count,
            cache_size=multi_query_cache_size,
            use_cache=True,   # 启用缓存
            cache_ttl=multi_query_cache_ttl
        )
        
        # 创建生成器和评估器
        generator = LLMGenerator(self.llm_service)
        evaluator = RelevanceEvaluator(self.llm_service)
        
        # 选择检索器
        retriever = self.multi_query_retriever if self.use_multi_query else self.vector_retriever
        logger.info(f'使用检索器: {retriever.__class__.__name__}')
        if self.use_multi_query:
            logger.info(f'多查询数量: {multi_query_count}')
        
        # 创建 RAG 服务
        self.rag_service = RAGService(
            retriever=retriever,
            generator=generator,
            evaluator=evaluator
        )
        
    def set_retriever_type(self, retriever_type: str) -> None:
        """
        设置检索器类型
        
        Args:
            retriever_type: 检索器类型，可选值：'vector', 'keyword', 'multi_query'
        """
        if retriever_type == 'vector':
            self.rag_service.retriever = self.vector_retriever
        elif retriever_type == 'keyword':
            self.rag_service.retriever = self.keyword_retriever
        elif retriever_type == 'multi_query':
            self.rag_service.retriever = self.multi_query_retriever
        else:
            logger.warning(f'未知的检索器类型: {retriever_type}，继续使用当前检索器')
        
        logger.info(f'切换检索器为: {self.rag_service.retriever.__class__.__name__}')
    
    async def send_message(
        self,
        content: str,
        session_id: Optional[str] = None,
        context_messages: List[Dict[str, Any]] = None,
        stream: bool = False,
        domains: List[str] = None
    ) -> Union[Dict[str, Any], AsyncIterator[str]]:
        """
        处理用户发送的消息并生成回答
        
        Args:
            content: 用户消息内容
            session_id: 会话ID
            context_messages: 上下文消息列表
            stream: 是否使用流式响应
            domains: 知识库领域
        Returns:
            如果 stream=False，返回包含回答的字典
            如果 stream=True，返回回答生成器
        """
        try:
            # 1. 获取或创建会话
            session = await self.chat_repository.get_or_create_session(session_id)
            session_id = session.id
            
            # 2. 保存用户消息
            user_message = await self.chat_repository.create_message(
                session_id=session_id,
                role='user',
                content=content
            )
            
            # 3. 使用 RAG 服务处理查询
            if stream:
                # 创建消息占位符
                assistant_message = await self.chat_repository.create_message(
                    session_id=session_id,
                    role='assistant',
                    content='',  # 初始为空
                    references=[]  # 初始为空
                )
                
                async def stream_generator() -> AsyncIterator[str]:
                    full_response = ''
                    references = []
                    try:
                        response_stream = await self.rag_service.process_query(
                            query=content,
                            stream=True,
                            context_messages=context_messages,
                            domains=domains
                        )
                        if isinstance(response_stream, (AsyncGenerator, AsyncIterator)):
                            try:
                                async for chunk in response_stream:
                                    if isinstance(chunk, dict) and 'references' in chunk:
                                        # 这是包含引用的特殊chunk
                                        references = chunk['references']
                                        continue
                                    if isinstance(chunk, str):
                                        full_response += chunk
                                        yield chunk
                            except StopAsyncIteration:
                                # 正常的迭代结束，不是错误
                                logger.debug('流式响应正常结束')
                                pass
                        else:
                            # 如果返回的不是生成器，直接返回完整响应
                            full_response = str(response_stream)
                            yield full_response
                        
                        # 在文本响应完成后，发送引用信息
                        if not references:
                            # 如果流式响应中没有引用信息，尝试获取引用
                            try:
                                # 使用非流式方式获取引用
                                result = await self.rag_service.get_references_for_query(query=content, domains=domains)
                                if result and 'references' in result:
                                    references = result['references']
                            except Exception as e:
                                logger.error(f'获取引用信息失败: {str(e)}')
                        
                        # 发送引用信息
                        if references:
                            yield {'references': references}
                    except Exception as e:
                        # 增强异常日志记录
                        error_type = type(e).__name__
                        error_detail = str(e) or '无详细错误信息'
                        error_repr = repr(e)
                        error_msg = f'生成回答时出现错误: {error_type}: {error_detail}'
                        logger.error(f'{error_msg}, 异常对象: {error_repr}')
                        yield error_msg
                    finally:
                        # 更新完整的回答
                        if full_response:
                            try:
                                await self.chat_repository.update_message_content(
                                    message_id=assistant_message.id,
                                    content=full_response.strip()
                                )
                            except Exception as e:
                                logger.error(f'更新消息内容失败: {str(e)}')
                
                return stream_generator()
            else:
                result = await self.rag_service.process_query(
                    query=content,
                    stream=False,
                    context_messages=context_messages,
                    domains=domains
                )
                
                # 保存助手消息
                assistant_message = await self.chat_repository.create_message(
                    session_id=session_id,
                    role='assistant',
                    content=result['response'],
                    references=result.get('references', [])
                )
                
                return {
                    'answer': result['response'],
                    'references': result.get('references', []),
                    'evaluation': result.get('evaluation'),
                    'session_id': session_id,
                    'message_id': assistant_message.id
                }
                
        except Exception as e:
            logger.error(f'处理消息失败: {str(e)}')
            raise LLMServiceError(f'处理消息失败: {str(e)}')

    async def get_chat_history(
        self,
        session_id: str,
        page_size: int = 20,
        page_number: int = 1
    ) -> Dict[str, Any]:
        """
        获取聊天历史记录
        """
        try:
            messages, total = await self.chat_repository.get_session_messages(
                session_id=session_id,
                page_size=page_size,
                page_number=page_number
            )
            
            return {
                'messages': [
                    {
                        'id': msg.id,
                        'role': msg.role,
                        'content': msg.content,
                        'timestamp': msg.timestamp.isoformat(),
                        'references': [
                            {
                                'title': ref.title,
                                'url': ref.url,
                                'content': ref.content,
                                'relevance': ref.relevance
                            }
                            for ref in msg.references
                        ]
                    }
                    for msg in messages
                ],
                'total': total,
                'page_size': page_size,
                'page_number': page_number
            }
        except Exception as e:
            raise DatabaseError(f'获取历史记录失败: {str(e)}')

    async def delete_chat_session(self, session_id: str) -> bool:
        """
        删除聊天会话
        """
        return await self.chat_repository.delete_session(session_id) 