# -*- coding: utf-8 -*-
import importlib
import inspect
import pkgutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Type

from ..core.constants import KnowledgeDomain
from ..core.logging import get_module_logger
from ..crawlers.fetchers import FetcherFactory
from ..crawlers.knowledge_base import KnowledgeBase
from ..repositories.knowledge_repository import KnowledgeRepository

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

# 基础爬虫类定义
class BaseCrawler:
    """爬虫基类"""
    name = 'base_crawler'
    description = '基础爬虫'
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        
    def crawl(self) -> List[Dict[str, Any]]:
        """执行爬取任务，返回文档列表"""
        raise NotImplementedError('子类必须实现crawl方法')

class CrawlerService:
    """爬虫服务"""
    
    def __init__(self, repository: KnowledgeRepository):
        self.repository = repository
        self.knowledge_base = KnowledgeBase(repository)
        self._crawlers = {}
        self._load_crawlers()
        
    def get_available_crawlers(self) -> List[Dict[str, Any]]:
        """获取可用的爬虫列表"""
        crawlers = []
        
        # 获取所有注册的获取器
        fetcher_types = FetcherFactory.get_supported_types()
        for fetcher_type in fetcher_types:
            crawlers.append({
                'name': fetcher_type,
                'description': f'{fetcher_type} crawler',
                'config_schema': {
                    'type': 'object',
                    'properties': {
                        'urls': {
                            'type': 'array',
                            'items': {'type': 'string', 'format': 'uri'},
                            'description': '要爬取的URL列表'
                        }
                    }
                }
            })
            
        # 获取所有注册的爬虫
        for name, crawler_class in self._crawlers.items():
            crawlers.append({
                'name': name,
                'description': getattr(crawler_class, 'description', ''),
                'config_schema': getattr(crawler_class, 'config_schema', {})
            })
            
        return crawlers
        
    async def run_crawler(
        self,
        crawler_name: str,
        domain: Optional[str] = KnowledgeDomain.DEFAULT,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行指定的爬虫
        
        Args:
            crawler_name: 爬虫名称
            domain: 知识领域，默认为default
            config: 爬虫配置
            
        Returns:
            Dict: 爬取结果
        """
        try:
            # 尝试使用 FetcherFactory
            if crawler_name in FetcherFactory.get_supported_types():
                if not config or 'urls' not in config:
                    raise ValueError('URLs must be provided in config')
                    
                urls = config['urls']
                if not isinstance(urls, list):
                    urls = [urls]
                    
                results = await self.knowledge_base.batch_fetch(
                    urls=urls,
                    source_type=crawler_name,
                    domain=domain
                )
                
                return {
                    'crawler': crawler_name,
                    'total': len(urls),
                    'success': sum(1 for r in results if r['status'] == 'success'),
                    'failed': sum(1 for r in results if r['status'] == 'error'),
                    'results': results
                }
                
            # 尝试使用注册的爬虫
            if crawler_name in self._crawlers:
                crawler_class = self._crawlers[crawler_name]
                crawler = crawler_class(config)
                documents = crawler.crawl()
                
                # 处理文档并导入知识库
                knowledge_ids = await self.knowledge_service.import_documents(documents)
                
                return {
                    'crawler': crawler_name,
                    'total': len(documents),
                    'success': len(knowledge_ids),
                    'failed': 0,
                    'results': knowledge_ids
                }
                
            raise ValueError(f'Unsupported crawler: {crawler_name}')
            
        except Exception as e:
            logger.error(f'Error running crawler {crawler_name}: {str(e)}')
            return {
                'crawler': crawler_name,
                'error': str(e),
                'status': 'error'
            }
            
    async def run_all_crawlers(
        self,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行所有爬虫
        
        Args:
            config: 爬虫配置
            
        Returns:
            Dict: 爬取结果
        """
        if not config:
            raise ValueError('Config must be provided')
            
        results = {}
        all_crawlers = list(FetcherFactory.get_supported_types()) + list(self._crawlers.keys())
        
        for crawler_name in all_crawlers:
            if crawler_name in config:
                try:
                    result = await self.run_crawler(
                        crawler_name=crawler_name,
                        config={'urls': config[crawler_name]} if isinstance(config[crawler_name], (list, str)) else config[crawler_name]
                    )
                    results[crawler_name] = result
                except Exception as e:
                    logger.error(f'Error running crawler {crawler_name}: {str(e)}')
                    results[crawler_name] = {
                        'status': 'error',
                        'error': str(e)
                    }
                    
        return {
            'total_crawlers': len(all_crawlers),
            'success_crawlers': sum(1 for r in results.values() if 'status' not in r),
            'failed_crawlers': sum(1 for r in results.values() if 'status' in r and r['status'] == 'error'),
            'results': results
        }
        
    def _load_crawlers(self) -> None:
        """加载所有爬虫类"""
        try:
            # 从crawlers目录加载爬虫
            backend_path = Path(__file__).parent.parent / 'crawlers'
            if not backend_path.exists():
                logger.warning(f'爬虫目录不存在: {backend_path}')
                return
                
            # 加载sites目录下的爬虫
            sites_path = backend_path / 'sites'
            if sites_path.exists():
                self._load_from_path(sites_path)
                
            # 加载自定义爬虫目录
            custom_path = backend_path / 'custom'
            if custom_path.exists():
                self._load_from_path(custom_path)
                
        except Exception as e:
            logger.error(f'加载爬虫时出错: {str(e)}')
            
    def _load_from_path(self, path: Path) -> None:
        """从指定路径加载爬虫类"""
        try:
            # 获取相对于backend.src的路径
            relative_path = path.relative_to(Path(__file__).parent.parent)
            
            # 查找所有爬虫文件（以_crawler.py结尾）
            for item in path.glob('*_crawler.py'):
                if item.name == '__init__.py':
                    continue
                    
                try:
                    # 构建模块路径（使用绝对导入）
                    module_path = f'src.crawlers.{relative_path.name}.{item.stem}'
                    module = importlib.import_module(module_path)
                    
                    # 查找模块中的爬虫类
                    for cls_name, cls in inspect.getmembers(module, inspect.isclass):
                        if (issubclass(cls, BaseCrawler) and 
                            cls.__module__ == module.__name__ and 
                            cls is not BaseCrawler):
                            
                            self._register_crawler(cls)
                            
                except Exception as e:
                    logger.error(f'加载爬虫模块 {item.name} 时出错: {str(e)}')
                    
        except Exception as e:
            logger.error(f'从路径 {path} 加载爬虫时出错: {str(e)}')
                
    def _register_crawler(self, crawler_class: Type[BaseCrawler]) -> None:
        """注册爬虫类"""
        if not hasattr(crawler_class, 'name') or not crawler_class.name:
            logger.warning(f'爬虫类 {crawler_class.__name__} 没有name属性，跳过注册')
            return
            
        self._crawlers[crawler_class.name] = crawler_class
        logger.info(f'已注册爬虫: {crawler_class.name}') 