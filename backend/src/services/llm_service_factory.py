# -*- coding: utf-8 -*-
from typing import Optional
from ..core.logging import get_module_logger

from ..llm.base import BaseLLMService
from ..llm.ollama import OllamaService
from ..llm.siliconflow import SiliconFlowService
from ..core.config import settings
from ..core.exceptions import LLMServiceError

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class LLMServiceFactory:
    """LLM服务工厂类，用于创建不同类型的LLM服务实例"""
    
    @staticmethod
    def create_service(service_type: Optional[str] = None, model: Optional[str] = None) -> BaseLLMService:
        """
        创建LLM服务实例
        
        Args:
            service_type: 服务类型，可选值为'ollama'或'siliconflow'，
                         如果为None则使用配置中的默认值
            model: 模型名称，如果为None则使用配置中的默认值
                         
        Returns:
            LLM服务实例
            
        Raises:
            LLMServiceError: 如果服务类型不支持或配置不正确
        """
        # 如果未指定服务类型，使用配置中的默认值
        if service_type is None:
            service_type = settings.LLM_SERVICE
            
        service_type = service_type.lower()
        
        if service_type == 'ollama':
            # 如果未指定模型，使用配置中的默认值
            ollama_model = model or settings.OLLAMA_MODEL
            logger.info(f'创建Ollama服务实例，模型: {ollama_model}')
            return OllamaService(
                model=ollama_model,
                base_url=settings.OLLAMA_BASE_URL,
                request_timeout=settings.LLM_REQUEST_TIMEOUT
            )
        elif service_type == 'siliconflow':
            if not settings.SILICONFLOW_API_KEY:
                raise LLMServiceError('未配置SiliconFlow API密钥')
                
            # 如果未指定模型，使用配置中的默认值
            siliconflow_model = model or settings.SILICONFLOW_MODEL
            logger.info(f'创建SiliconFlow服务实例，模型: {siliconflow_model}')
            return SiliconFlowService(
                api_key=settings.SILICONFLOW_API_KEY,
                model=siliconflow_model,
                base_url=settings.SILICONFLOW_BASE_URL,
                request_timeout=settings.LLM_REQUEST_TIMEOUT
            )
        else:
            raise LLMServiceError(f'不支持的LLM服务类型: {service_type}')
    
    @staticmethod
    def create_variant_service() -> BaseLLMService:
        """
        创建用于生成查询变体的专用LLM服务实例
        
        如果配置了专用服务和模型，则使用专用配置；否则使用主LLM服务。
        
        Returns:
            LLM服务实例
        """
        # 如果配置了专用的变体生成服务和模型
        if settings.VARIANT_LLM_SERVICE or settings.VARIANT_LLM_MODEL:
            service_type = settings.VARIANT_LLM_SERVICE or settings.LLM_SERVICE
            model = settings.VARIANT_LLM_MODEL
            logger.info(f'创建查询变体专用LLM服务，类型: {service_type}, 模型: {model or "默认模型"}')
            return LLMServiceFactory.create_service(service_type, model)
        else:
            # 使用默认LLM服务
            logger.info('使用默认LLM服务生成查询变体')
            return LLMServiceFactory.create_service()
            
    @staticmethod
    async def check_service_status(service_type: Optional[str] = None) -> dict:
        """
        检查LLM服务状态
        
        Args:
            service_type: 服务类型，可选值为'ollama'或'siliconflow'，
                         如果为None则使用配置中的默认值
                         
        Returns:
            服务状态信息
            
        Raises:
            LLMServiceError: 如果服务类型不支持或配置不正确
        """
        service = LLMServiceFactory.create_service(service_type)
        return await service.check_service_status() 