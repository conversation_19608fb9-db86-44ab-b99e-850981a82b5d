# -*- coding: utf-8 -*-
from typing import List, Dict, Any, Optional
import uuid
from ...core.logging import get_module_logger
import asyncio
import time
import hashlib

from .base import BaseRetriever
from ...core.embeddings import get_embedding_model
from ...repositories.knowledge_repository import KnowledgeRepository
from ...llm.base import BaseLLMService
from ...core.config import settings
from ...core.database import async_session_factory

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class VectorRetriever(BaseRetriever):
    '''
    向量检索器
    '''
    def __init__(
        self,
        knowledge_repository: KnowledgeRepository,
        embedding_model = None
    ):
        '''
        初始化向量检索器
        Args:
            knowledge_repository: 知识库仓库实例
            embedding_model: 向量嵌入模型，如果为None则使用默认模型
        '''
        self.knowledge_repository = knowledge_repository
        self.embedding_model = embedding_model or get_embedding_model()
    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        执行向量检索
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            min_score: 最小相似度分数
            **kwargs: 其他参数
            
        Returns:
            检索结果列表
        """
        # 添加性能监控点
        start_time = time.time()
        try:
            # 执行向量检索
            logger.info(f'性能监控[VectorRetriever]: 开始向量检索, 查询: {query[:30]}{"..." if len(query) > 30 else ""}')
            
            results = await self.knowledge_repository.search_relevant_content(
                query=query,
                limit=limit,
                min_score=min_score,
                domains=domains
            )
            
            end_time = time.time()
            logger.info(f'性能监控[VectorRetriever]: 向量检索完成, 找到{len(results)}条结果, 耗时: {(end_time - start_time)*1000:.2f}ms')
            
            # 为结果添加代码内容
            enhanced_results = self._restore_code_in_results(results)
            
            return enhanced_results
            
        except Exception as e:
            end_time = time.time()
            logger.error(f'向量检索失败: {str(e)}')
            logger.info(f'性能监控[VectorRetriever]: 向量检索失败, 耗时: {(end_time - start_time)*1000:.2f}ms')
            return []

class KeywordRetriever(BaseRetriever):
    """关键词检索器"""
    
    def __init__(self, knowledge_repository: KnowledgeRepository):
        """
        初始化关键词检索器
        
        Args:
            knowledge_repository: 知识库仓库实例
        """
        self.knowledge_repository = knowledge_repository
        
        # 预定义意图关键词
        self.intent_keywords = {
            'code': ['代码', '实现', '示例', 'example', 'code', 'implement'],
            'error': ['错误', '异常', '失败', '报错', 'error', 'exception', 'fail'],
            'api': ['api', '接口', '方法', '函数', '调用']
        }
        
        # 意图对应的搜索参数
        self.search_params = {
            'code': {'min_score': 0.3},
            'error': {'min_score': 0.4},
            'api': {'min_score': 0.3},
            'general': {'min_score': 0.2}
        }
    
    def _analyze_intent(self, query: str) -> str:
        """分析查询意图"""
        query_lower = query.lower()
        for intent, keywords in self.intent_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return intent
        return "general"
    
    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        执行关键词检索
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            **kwargs: 其他参数
            
        Returns:
            检索结果列表
        """
        try:
            # 1. 分析查询意图
            intent = self._analyze_intent(query)
            params = self.search_params[intent]
            
            # 2. 执行检索
            results = await self.knowledge_repository.search_by_keywords(
                query=query,
                limit=limit,
                domains=domains,
                **params
            )
            
            # 3. 处理结果
            processed_results = []
            for result in results:
                processed_result = {
                    'title': result.get('title', ''),
                    'content': result.get('content', ''),
                    'url': result.get('url', ''),
                    'type': result.get('type', 'document'),
                    'relevance': result.get('score', 0.0)
                }
                
                # 根据意图处理内容
                if intent == 'code' and 'code_content' in result:
                    processed_result['content'] = result['code_content']
                elif intent == 'error' and 'solution' in result:
                    processed_result['content'] = f'错误描述：{result.get('error_description', '')}\n解决方案：{result['solution']}'
                elif intent == 'api' and 'api_description' in result:
                    processed_result['content'] = f'API说明：{result['api_description']}\n参数说明：{result.get('parameters', '')}'
                
                processed_results.append(processed_result)
            logger.debug(f'关键词检索结果含{len(processed_results)}条')
            
            # 为结果添加代码内容
            enhanced_results = self._restore_code_in_results(processed_results)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f'关键词检索失败: {str(e)}')
            return []


class BM25Retriever(BaseRetriever):
    """基于BM25的关键词检索器"""
    
    def __init__(self, knowledge_repository: KnowledgeRepository):
        """
        初始化BM25检索器
        
        Args:
            knowledge_repository: 知识库仓库实例
        """
        self.knowledge_repository = knowledge_repository
        
        # BM25搜索参数配置
        self.search_params = {
            'api': {
                'boost_title': 3.5,
                'boost_content': 1.2,
                'boost_domain': 2.0,
                'min_score': 0.8
            },
            'harmony': {
                'boost_title': 3.0,
                'boost_content': 1.8,
                'boost_domain': 2.5,
                'min_score': 0.8
            },
            'component': {
                'boost_title': 3.2,
                'boost_content': 1.5,
                'boost_domain': 2.0,
                'min_score': 0.8
            },
            'general': {
                'boost_title': 2.0,
                'boost_content': 1.0,
                'boost_domain': 1.0,
                'min_score': 0.5
            }
        }

    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        执行BM25检索
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            domains: 搜索领域过滤
            **kwargs: 其他参数，包括min_score, boost_title, boost_content, api_category等
            
        Returns:
            检索结果列表
        """
        start_time = time.time()
        try:
            # 1.明确查询意图，TODO待优化
            intent = "general"
            params = self.search_params[intent].copy()
            
            # 2. 合并传入的参数
            params.update(kwargs)
            
            logger.info(f'性能监控[BM25Retriever]: 开始BM25检索, 查询: {query[:30]}{"..." if len(query) > 30 else ""}, 意图: {intent}')
            
            # 4. 执行BM25搜索
            results = await self.knowledge_repository.search_by_bm25(
                query=query,
                limit=limit,
                domains=domains,
                **params
            )
            
            # 6. 记录性能信息
            elapsed_time = (time.time() - start_time) * 1000
            logger.info(f'性能监控[BM25Retriever]: BM25检索完成, 找到{len(results)}条结果, 耗时: {elapsed_time:.2f}ms')
            
            # 为结果添加代码内容
            enhanced_results = self._restore_code_in_results(results)
            
            return enhanced_results
            
        except Exception as e:
            elapsed_time = (time.time() - start_time) * 1000
            logger.error(f'性能监控[BM25Retriever]: BM25检索失败, 查询: {query}, 耗时: {elapsed_time:.2f}ms, 错误: {str(e)}')
            return []

class MultiQueryRetriever(BaseRetriever):
    """多查询检索器"""
    
    # 类级缓存，保存查询到变体的映射
    _variant_cache = {}
    
    def __init__(
        self,
        base_retriever: BaseRetriever,
        llm_service: BaseLLMService,
        variant_llm_service: Optional[BaseLLMService] = None,
        num_queries: int = 3,
        cache_size: int = 100,  # 缓存大小
        use_cache: bool = True,  # 是否使用缓存
        cache_ttl: int = None    # 缓存过期时间（秒）
    ):
        """
        初始化多查询检索器
        
        Args:
            base_retriever: 基础检索器
            llm_service: LLM服务，用于生成查询变体（如果未指定variant_llm_service）
            variant_llm_service: 专用于生成查询变体的LLM服务，可以使用更小的模型以提高速度
            num_queries: 生成的查询变体数量
            cache_size: 变体缓存大小
            use_cache: 是否使用缓存
            cache_ttl: 缓存过期时间（秒）
        """
        self.base_retriever = base_retriever
        self.llm_service = llm_service
        self.variant_llm_service = variant_llm_service or llm_service  # 如果未指定专用模型，使用主模型
        self.num_queries = num_queries
        self.cache_size = cache_size
        self.use_cache = use_cache
        # 从配置中获取缓存过期时间，如果未指定
        self.cache_ttl = cache_ttl or settings.MULTI_QUERY_CACHE_TTL or 86400
    
    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """执行多查询检索"""
        start_time = time.time()
        logger.info(f'性能监控[MultiQueryRetriever]: 开始多查询检索, 查询: {query[:30]}{"..." if len(query) > 30 else ""}')
        
        # 1. 生成查询变体
        variant_start_time = time.time()
        query_variants = await self._generate_query_variants(query)
        all_variants = [query] + query_variants  # 包含原始查询
        
        variant_time = time.time()
        logger.info(f'性能监控[MultiQueryRetriever]: 生成了{len(query_variants)}个查询变体, 耗时: {(variant_time - variant_start_time)*1000:.2f}ms')
        logger.debug(f'生成了{len(query_variants)}个查询变体: {query_variants}, 耗时: {(variant_time - start_time)*1000:.2f}ms')
        
        # 2. 为每个变体创建独立的检索任务
        all_results = []
        
        # 定义一个独立的检索函数，为每个变体使用新的数据库会话
        async def execute_variant_search(variant: str) -> List[Dict[str, Any]]:
            """为每个变体创建新的数据库会话并执行检索"""
            variant_search_start = time.time()
            try:
                # 创建新的数据库会话，使用简化的方式
                async with async_session_factory() as session:
                    # 使用新会话创建新的知识库仓库
                    knowledge_repo = KnowledgeRepository(session)
                    # 使用新的仓库创建新的检索器
                    if settings.USE_HYBRID_RETRIEVER:
                        retriever = HybridRetriever(knowledge_repo)
                    else:
                        retriever = VectorRetriever(knowledge_repo)

                    # 执行检索
                    results = await retriever.retrieve(
                        query=variant,
                        limit=limit,
                        min_score=min_score,
                        domains=domains,
                        **kwargs
                    )
                    variant_search_end = time.time()
                    logger.debug(f'性能监控[MultiQueryRetriever]: 变体{variant[:20]}...检索完成, 找到{len(results)}条结果, 耗时: {(variant_search_end - variant_search_start)*1000:.2f}ms')
                    return results

            except Exception as e:
                variant_search_end = time.time()
                logger.error(f'变体{variant}检索失败: {str(e)}')
                logger.debug(f'性能监控[MultiQueryRetriever]: 变体{variant[:20]}...检索失败, 耗时: {(variant_search_end - variant_search_start)*1000:.2f}ms')
                return []
        
        # 创建任务列表，使用序列化执行避免资源泄漏
        search_start_time = time.time()

        # 在 macOS 上使用序列化执行，避免信号量泄漏问题
        import platform
        if platform.system() == 'Darwin':  # macOS
            # macOS 上使用序列化执行，避免并发问题
            variant_results_list = []
            for variant in all_variants:
                try:
                    result = await execute_variant_search(variant)
                    variant_results_list.append(result)
                except Exception as e:
                    logger.error(f'变体{variant}检索失败: {str(e)}')
                    variant_results_list.append([])
        else:
            # 其他系统使用有限并发
            max_concurrent = min(3, len(all_variants))
            semaphore = asyncio.Semaphore(max_concurrent)

            async def limited_variant_search(variant: str) -> List[Dict[str, Any]]:
                """带并发限制的变体搜索"""
                async with semaphore:
                    return await execute_variant_search(variant)

            tasks = [limited_variant_search(variant) for variant in all_variants]
            variant_results_list = await asyncio.gather(*tasks, return_exceptions=True)

        search_end_time = time.time()
        
        logger.info(f'性能监控[MultiQueryRetriever]: 并行执行{len(tasks)}个变体检索任务, 总耗时: {(search_end_time - search_start_time)*1000:.2f}ms')

        # 收集所有结果，处理可能的异常
        successful_results = 0
        failed_results = 0
        for i, variant_result in enumerate(variant_results_list):
            variant = all_variants[i] if i < len(all_variants) else "unknown"

            # 检查是否是异常结果
            if isinstance(variant_result, Exception):
                failed_results += 1
                logger.error(f'变体查询{variant}失败: {str(variant_result)}')
                continue

            # 正常结果
            if isinstance(variant_result, list):
                all_results.extend(variant_result)
                successful_results += 1
                logger.debug(f'变体查询{variant}返回{len(variant_result)}条结果')
            else:
                failed_results += 1
                logger.warning(f'变体查询{variant}返回了意外的结果类型: {type(variant_result)}')

        logger.info(f'性能监控[MultiQueryRetriever]: 成功执行{successful_results}个查询，失败{failed_results}个查询')
        
        # 3. 合并结果并去重
        merge_start_time = time.time()
        merged_results = self._merge_and_deduplicate(all_results, limit)
        merge_end_time = time.time()
        
        logger.info(f'性能监控[MultiQueryRetriever]: 合并和去重耗时: {(merge_end_time - merge_start_time)*1000:.2f}ms')
        
        # 记录总体耗时统计
        end_time = time.time()
        logger.info(f'性能监控[MultiQueryRetriever]: 多查询检索完成, 总耗时: {(end_time - start_time)*1000:.2f}ms, 结果数: {len(merged_results)}')
        logger.info(f'性能监控[MultiQueryRetriever]: 各阶段耗时分析:')
        logger.info(f'- 生成变体: {(variant_time - variant_start_time)*1000:.2f}ms ({((variant_time - variant_start_time)/(end_time - start_time)*100):.1f}%)')
        logger.info(f'- 变体搜索: {(search_end_time - search_start_time)*1000:.2f}ms ({((search_end_time - search_start_time)/(end_time - start_time)*100):.1f}%)')
        logger.info(f'- 合并去重: {(merge_end_time - merge_start_time)*1000:.2f}ms ({((merge_end_time - merge_start_time)/(end_time - start_time)*100):.1f}%)')
        
        merged_results = self._restore_code_in_results(merged_results)
        return merged_results
    
    async def _generate_query_variants(self, query: str) -> List[str]:
        """生成查询变体"""
        start_time = time.time()
        
        # 如果启用缓存，先检查缓存
        if self.use_cache:
            cache_key = self._get_cache_key(query)
            cached_variants = self._get_from_cache(cache_key)
            if cached_variants:
                cache_hit_time = time.time()
                logger.info(f'性能监控[MultiQueryRetriever]: 查询变体缓存命中, 耗时: {(cache_hit_time - start_time)*1000:.2f}ms')
                return cached_variants
        
        try:
            # 构建变体生成提示词
            prompt = f'''为了提高检索效果，请为以下查询生成{self.num_queries}个不同的表述方式，保持原意但使用不同的词语和句式。
查询：{query}

注意：
1. 只需返回变体查询本身，不要包含额外的说明或标号
2. 每个变体占一行
3. 变体的总数必须是{self.num_queries}个

变体：'''

            # 使用LLM生成变体
            llm_start_time = time.time()
            response = await self.variant_llm_service.generate(prompt)
            llm_end_time = time.time()
            
            logger.debug(f'性能监控[MultiQueryRetriever]: LLM生成变体响应耗时: {(llm_end_time - llm_start_time)*1000:.2f}ms')
            
            # 处理响应
            variants = [line.strip() for line in response.strip().split('\n') if line.strip()]
            variants = variants[:self.num_queries]  # 确保不超过设定数量
            
            # 如果启用缓存，保存到缓存
            if self.use_cache:
                cache_key = self._get_cache_key(query)
                self._add_to_cache(cache_key, variants)
            
            end_time = time.time()
            logger.info(f'性能监控[MultiQueryRetriever]: 生成查询变体完成, 耗时: {(end_time - start_time)*1000:.2f}ms, LLM调用耗时: {(llm_end_time - llm_start_time)*1000:.2f}ms')
            
            return variants
        except Exception as e:
            end_time = time.time()
            logger.error(f'生成查询变体失败: {str(e)}')
            logger.info(f'性能监控[MultiQueryRetriever]: 生成查询变体失败, 耗时: {(end_time - start_time)*1000:.2f}ms')
            return []
    
    def _get_cache_key(self, query: str) -> str:
        """生成缓存键"""
        # 使用MD5生成查询字符串的哈希作为缓存键
        return hashlib.md5(query.encode('utf-8')).hexdigest()
    
    def _get_from_cache(self, key: str) -> Optional[List[str]]:
        """从缓存获取数据"""
        if key not in self._variant_cache:
            return None
            
        # 获取缓存项和时间戳
        variants, timestamp = self._variant_cache[key]
        
        # 检查是否过期
        if time.time() - timestamp > self.cache_ttl:
            # 删除过期缓存
            del self._variant_cache[key]
            return None
            
        return variants
    
    def _add_to_cache(self, key: str, variants: List[str]) -> None:
        """添加数据到缓存"""
        # 如果缓存已满，删除最旧的项
        if len(self._variant_cache) >= self.cache_size:
            oldest_key = min(
                self._variant_cache.keys(), 
                key=lambda k: self._variant_cache[k][1]
            )
            del self._variant_cache[oldest_key]
        
        # 添加新缓存项，包含变体列表和时间戳
        self._variant_cache[key] = (variants, time.time())
    
    def _merge_and_deduplicate(
        self, 
        results: List[Dict[str, Any]], 
        limit: int
    ) -> List[Dict[str, Any]]:
        """合并并去重结果"""
        # 使用ID或内容hash作为唯一标识
        unique_results = {}
        
        for result in results:
            result_id = result.get("id")
            
            # 如果没有ID，使用内容的哈希作为ID
            if not result_id:
                content = result.get("content", "")
                result_id = hash(content)
            
            # 如果结果已存在，保留分数更高的那个
            if result_id in unique_results:
                existing_score = unique_results[result_id].get("relevance", 0)
                current_score = result.get("relevance", 0)
                
                if current_score > existing_score:
                    unique_results[result_id] = result
            else:
                unique_results[result_id] = result
        
        # 按分数排序
        sorted_results = sorted(
            unique_results.values(),
            key=lambda x: x.get("relevance", 0),
            reverse=True
        )
        
        return sorted_results[:limit] 

class HybridRetriever(BaseRetriever):
    """混合检索器 - 结合BM25和向量检索"""
    
    def __init__(
        self,
        knowledge_repository: KnowledgeRepository
    ):
        """
        初始化混合检索器
        
        Args:
            knowledge_repository: 知识库仓库实例
            fusion_method: 融合方法 ("weighted_sum" 或 "rrf")
        """
        self.knowledge_repository = knowledge_repository
        self.vector_weight = settings.HYBRID_VECTOR_WEIGHT
        self.bm25_weight = settings.HYBRID_BM25_WEIGHT
        self.fusion_method = settings.HYBRID_FUSION_METHOD
        self.normalize_scores = True
        self.embedding_model = get_embedding_model()
        
        # 归一化权重（确保总和为1）
        total_weight = self.vector_weight + self.bm25_weight
        if total_weight > 0:
            self.vector_weight = self.vector_weight / total_weight
            self.bm25_weight = self.bm25_weight / total_weight
        else:
            # 如果两个权重都为0，则设置为相等权重
            self.vector_weight = 0.5
            self.bm25_weight = 0.5

        # 创建子检索器
        self.vector_retriever = VectorRetriever(knowledge_repository, self.embedding_model)
        self.bm25_retriever = BM25Retriever(knowledge_repository)
        
        logger.info(f'初始化混合检索器: vector_weight={self.vector_weight:.2f}, bm25_weight={self.bm25_weight:.2f}, fusion_method={self.fusion_method}')
    
    def set_weights(self, vector_weight: float, bm25_weight: float):
        """
        动态调整检索权重
        
        Args:
            vector_weight: 向量检索权重
            bm25_weight: BM25检索权重
        """
        self.vector_weight = max(0.0, min(1.0, vector_weight))
        self.bm25_weight = max(0.0, min(1.0, bm25_weight))
        
        # 归一化权重
        total_weight = self.vector_weight + self.bm25_weight
        if total_weight > 0:
            self.vector_weight = self.vector_weight / total_weight
            self.bm25_weight = self.bm25_weight / total_weight
        
        logger.info(f'调整混合检索器权重: vector_weight={self.vector_weight:.2f}, bm25_weight={self.bm25_weight:.2f}')
    
    def set_fusion_method(self, method: str):
        """
        设置融合方法
        
        Args:
            method: 融合方法 ("weighted_sum" 或 "rrf")
        """
        if method in ["weighted_sum", "rrf"]:
            self.fusion_method = method
            logger.info(f'设置融合方法为: {method}')
        else:
            logger.warning(f'不支持的融合方法: {method}，保持原有设置: {self.fusion_method}')
    
    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None,
        vector_params: Optional[Dict[str, Any]] = None,
        bm25_params: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        执行混合检索
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            min_score: 最小相似度分数
            domains: 搜索领域过滤
            vector_params: 向量检索特定参数
            bm25_params: BM25检索特定参数
            **kwargs: 其他参数
            
        Returns:
            混合检索结果列表
        """
        start_time = time.time()
        try:
            logger.info(f'性能监控[HybridRetriever]: 开始混合检索, 查询: {query[:30]}{"..." if len(query) > 30 else ""}')
            logger.info(f'混合检索参数: vector_weight={self.vector_weight:.2f}, bm25_weight={self.bm25_weight:.2f}, fusion_method={self.fusion_method}')
            
            # 准备检索参数
            vector_search_params = {
                "limit": limit * 2,  # 获取更多结果用于融合
                "min_score": min_score * 0.5,  # 降低最小分数阈值
                "domains": domains,
                **kwargs
            }
            if vector_params:
                vector_search_params.update(vector_params)
            
            bm25_search_params = {
                "limit": limit * 2,
                "min_score": min_score * 0.5,
                "domains": domains,
                **kwargs
            }
            if bm25_params:
                bm25_search_params.update(bm25_params)
            
            # 并行执行两种检索
            search_start_time = time.time()
            vector_task = self.vector_retriever.retrieve(query, **vector_search_params)
            bm25_task = self.bm25_retriever.retrieve(query, **bm25_search_params)
            
            vector_results, bm25_results = await asyncio.gather(vector_task, bm25_task)
            search_end_time = time.time()
            
            logger.info(f'性能监控[HybridRetriever]: 并行检索完成, 向量结果:{len(vector_results)}条, BM25结果:{len(bm25_results)}条, 耗时:{(search_end_time - search_start_time)*1000:.2f}ms')
            
            # 融合结果
            fusion_start_time = time.time()
            fused_results = self._fuse_results(
                vector_results=vector_results,
                bm25_results=bm25_results,
                limit=limit,
                min_score=min_score
            )
            fusion_end_time = time.time()
            
            logger.info(f"性能监控[HybridRetriever]: 结果融合完成, 最终结果:{len(fused_results)}条, 耗时:{(fusion_end_time - fusion_start_time)*1000:.2f}ms")
            
            # 记录总体性能
            end_time = time.time()
            logger.info(f"性能监控[HybridRetriever]: 混合检索完成, 总耗时:{(end_time - start_time)*1000:.2f}ms")
            
            # 为结果恢复代码内容
            enhanced_results = self._restore_code_in_results(fused_results)
            enhanced_results = enhanced_results[:limit]
            return enhanced_results
            
        except Exception as e:
            end_time = time.time()
            logger.error(f"混合检索失败: {str(e)}")
            logger.info(f"性能监控[HybridRetriever]: 混合检索失败, 耗时:{(end_time - start_time)*1000:.2f}ms")
            return []
    
    def _fuse_results(
        self,
        vector_results: List[Dict[str, Any]],
        bm25_results: List[Dict[str, Any]],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """
        融合向量检索和BM25检索的结果
        
        Args:
            vector_results: 向量检索结果
            bm25_results: BM25检索结果
            limit: 最终结果数量限制
            min_score: 最小分数阈值
            
        Returns:
            融合后的结果列表
        """
        if self.fusion_method == "rrf":
            return self._rrf_fusion(vector_results, bm25_results, limit, min_score)
        else:  # weighted_sum
            return self._weighted_sum_fusion(vector_results, bm25_results, limit, min_score)
    
    def _weighted_sum_fusion(
        self,
        vector_results: List[Dict[str, Any]],
        bm25_results: List[Dict[str, Any]],
        limit: int,
        min_score: float
    ) -> List[Dict[str, Any]]:
        """
        使用加权求和方法融合结果
        """
        # 创建结果ID到结果的映射
        all_results = {}
        
        # 处理向量检索结果
        if self.vector_weight > 0:
            vector_scores = [r.get("relevance", 0) for r in vector_results]
            if vector_scores and self.normalize_scores:
                max_score = max(vector_scores) if vector_scores else 1
                min_score_val = min(vector_scores) if vector_scores else 0
                score_range = max_score - min_score_val
                if score_range > 0:
                    # 归一化到0-1范围
                    for result in vector_results:
                        original_score = result.get("relevance", 0)
                        normalized_score = (original_score - min_score_val) / score_range
                        result["normalized_vector_score"] = normalized_score
                else:
                    for result in vector_results:
                        result["normalized_vector_score"] = 1.0
            else:
                for result in vector_results:
                    result["normalized_vector_score"] = result.get("relevance", 0)
            
            for result in vector_results:
                result_id = self._get_result_id(result)
                if result_id not in all_results:
                    all_results[result_id] = result.copy()
                    all_results[result_id]["vector_score"] = result["normalized_vector_score"]
                    all_results[result_id]["bm25_score"] = 0
                else:
                    all_results[result_id]["vector_score"] = result["normalized_vector_score"]
        
        # 处理BM25检索结果
        if self.bm25_weight > 0:
            bm25_scores = [r.get("relevance", 0) for r in bm25_results]
            if bm25_scores and self.normalize_scores:
                max_score = max(bm25_scores) if bm25_scores else 1
                min_score_val = min(bm25_scores) if bm25_scores else 0
                score_range = max_score - min_score_val
                if score_range > 0:
                    # 归一化到0-1范围
                    for result in bm25_results:
                        original_score = result.get("relevance", 0)
                        normalized_score = (original_score - min_score_val) / score_range
                        result["normalized_bm25_score"] = normalized_score
                else:
                    for result in bm25_results:
                        result["normalized_bm25_score"] = 1.0
            else:
                for result in bm25_results:
                    result["normalized_bm25_score"] = result.get("relevance", 0)
            
            for result in bm25_results:
                result_id = self._get_result_id(result)
                if result_id not in all_results:
                    all_results[result_id] = result.copy()
                    all_results[result_id]["vector_score"] = 0
                    all_results[result_id]["bm25_score"] = result["normalized_bm25_score"]
                else:
                    all_results[result_id]["bm25_score"] = result["normalized_bm25_score"]
        
        # 计算混合分数
        for result in all_results.values():
            vector_score = result.get("vector_score", 0)
            bm25_score = result.get("bm25_score", 0)
            
            # 加权求和
            hybrid_score = (
                self.vector_weight * vector_score + 
                self.bm25_weight * bm25_score
            )
            
            result["relevance"] = hybrid_score
            result["fusion_info"] = {
                "method": "weighted_sum",
                "vector_score": vector_score,
                "bm25_score": bm25_score,
                "vector_weight": self.vector_weight,
                "bm25_weight": self.bm25_weight
            }
        
        # 过滤和排序
        filtered_results = [
            result for result in all_results.values() 
            if result["relevance"] >= min_score
        ]
        
        sorted_results = sorted(
            filtered_results,
            key=lambda x: x["relevance"],
            reverse=True
        )
        
        return sorted_results[:limit]
    
    def _rrf_fusion(
        self,
        vector_results: List[Dict[str, Any]],
        bm25_results: List[Dict[str, Any]],
        limit: int,
        min_score: float,
        k: int = 60
    ) -> List[Dict[str, Any]]:
        """
        使用Reciprocal Rank Fusion (RRF)方法融合结果
        
        Args:
            k: RRF参数，通常设置为60
        """
        # 创建排名映射
        vector_rank = {self._get_result_id(r): i + 1 for i, r in enumerate(vector_results)}
        bm25_rank = {self._get_result_id(r): i + 1 for i, r in enumerate(bm25_results)}
        
        # 收集所有唯一的结果
        all_results = {}
        
        # 添加向量检索结果
        for result in vector_results:
            result_id = self._get_result_id(result)
            all_results[result_id] = result.copy()
        
        # 添加BM25检索结果（如果还没有的话）
        for result in bm25_results:
            result_id = self._get_result_id(result)
            if result_id not in all_results:
                all_results[result_id] = result.copy()
        
        # 计算RRF分数
        for result_id, result in all_results.items():
            rrf_score = 0
            
            # 向量检索贡献
            if result_id in vector_rank and self.vector_weight > 0:
                rrf_score += self.vector_weight / (k + vector_rank[result_id])
            
            # BM25检索贡献
            if result_id in bm25_rank and self.bm25_weight > 0:
                rrf_score += self.bm25_weight / (k + bm25_rank[result_id])
            
            result["relevance"] = rrf_score
            result["fusion_info"] = {
                "method": "rrf",
                "vector_rank": vector_rank.get(result_id, None),
                "bm25_rank": bm25_rank.get(result_id, None),
                "vector_weight": self.vector_weight,
                "bm25_weight": self.bm25_weight,
                "k": k
            }
        
        # 过滤和排序
        filtered_results = [
            result for result in all_results.values() 
            if result["relevance"] >= min_score
        ]
        
        sorted_results = sorted(
            filtered_results,
            key=lambda x: x["relevance"],
            reverse=True
        )
        
        return sorted_results[:limit]
    
    def _get_result_id(self, result: Dict[str, Any]) -> str:
        """获取结果的唯一标识符"""
        result_id = result.get("id")
        
        if result_id is not None and str(result_id).strip():
            # 确保返回非空字符串
            return str(result_id).strip()
        
        # 异常情况：生成临时ID（这种情况理论上不应该发生）
        logger.warning(f"结果缺少有效ID，生成临时ID: {result}")
        return f"temp_{uuid.uuid4().hex[:8]}"
    
    async def get_retrieval_stats(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        获取混合检索的详细统计信息（用于调试和优化）
        
        Args:
            query: 查询文本
            limit: 结果数量限制
            
        Returns:
            包含各种统计信息的字典
        """
        start_time = time.time()
        
        # 分别执行两种检索
        vector_results = await self.vector_retriever.retrieve(query, limit=limit * 2)
        bm25_results = await self.bm25_retriever.retrieve(query, limit=limit * 2)
        
        # 执行融合
        fused_results = self._fuse_results(vector_results, bm25_results, limit, 0.0)
        
        end_time = time.time()
        
        # 计算重叠度
        vector_ids = {self._get_result_id(r) for r in vector_results}
        bm25_ids = {self._get_result_id(r) for r in bm25_results}
        overlap_ids = vector_ids & bm25_ids
        
        # 统计信息
        stats = {
            "query": query,
            "execution_time_ms": (end_time - start_time) * 1000,
            "vector_results_count": len(vector_results),
            "bm25_results_count": len(bm25_results),
            "fused_results_count": len(fused_results),
            "overlap_count": len(overlap_ids),
            "overlap_ratio": len(overlap_ids) / max(len(vector_ids | bm25_ids), 1),
            "fusion_method": self.fusion_method,
            "weights": {
                "vector": self.vector_weight,
                "bm25": self.bm25_weight
            },
            "vector_score_range": {
                "min": min([r.get("relevance", 0) for r in vector_results], default=0),
                "max": max([r.get("relevance", 0) for r in vector_results], default=0)
            },
            "bm25_score_range": {
                "min": min([r.get("relevance", 0) for r in bm25_results], default=0),
                "max": max([r.get("relevance", 0) for r in bm25_results], default=0)
            },
            "fused_score_range": {
                "min": min([r.get("relevance", 0) for r in fused_results], default=0),
                "max": max([r.get("relevance", 0) for r in fused_results], default=0)
            }
        }
        
        return stats 