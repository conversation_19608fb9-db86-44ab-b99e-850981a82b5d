# -*- coding: utf-8 -*-
from typing import List, Dict, Any, Union, AsyncGenerator, Optional
from ...core.logging import get_module_logger

from .base import BaseGenerator
from ...llm import BaseLLMService
from ...llm.prompts import PromptTemplates

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class LLMGenerator(BaseGenerator):
    """基于 LLM 的生成器"""
    
    def __init__(self, llm_service: BaseLLMService):
        """
        初始化 LLM 生成器
        
        Args:
            llm_service: LLM 服务实例
        """
        self.llm_service = llm_service
    
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        context_messages: Optional[List[Dict[str, str]]] = None,
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """
        生成回答
        
        Args:
            query: 用户查询
            context: 知识上下文
            stream: 是否使用流式输出
            context_messages: 对话历史上下文
            **kwargs: 其他参数
            
        Returns:
            生成的回答或回答生成器
        """
        try:
            # 1. 处理上下文消息
            messages = []
            if context_messages:
                messages.extend(context_messages)
            messages.append({'role': 'user', 'content': query})
            
            # 2. 生成回答
            response = await self.llm_service.generate_with_context(
                messages=messages,
                knowledge=context,
                stream=stream
            )
            
            return response
            
        except StopAsyncIteration:
            # 这是正常的流结束标志，不需要特殊处理
            logger.debug('生成器正常结束')
            raise
        except Exception as e:
            logger.error(f'生成回答失败: {str(e)}')
            if stream:
                # 创建一个带错误消息的异步生成器
                async def error_generator():
                    yield f'抱歉，生成回答时出现错误: {str(e)}'
                
                return error_generator()
            return '抱歉，生成回答时出现错误，请稍后重试。' 