# -*- coding: utf-8 -*-
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union, AsyncGenerator
from ...core.logging import get_module_logger
from ...core.config import settings

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class BaseRetriever(ABC):
    """检索器基类，用于从知识库中检索相关内容"""
    
    @abstractmethod
    async def retrieve(
        self,
        query: str,
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        domains: List[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """检索与查询相关的知识片段
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            domains: 知识库领域
            **kwargs: 其他可选参数
            
        Returns:
            检索结果列表，每个结果包含知识片段及其元数据
        """
        pass
    
    def _restore_code_in_results(
        self, 
        results: List[Dict[str, Any]], 
    ) -> List[Dict[str, Any]]:
        """为检索结果添加代码内容
        
        Args:
            results: 原始检索结果列表
            
        Returns:
            增强后的检索结果列表
        """
        code_enhanced_count = 0
        
        for result in results:
            # 检查是否有代码信息
            metadata = result.get('metadata', {})
            codes = metadata.get('code', {})
            
            if codes and isinstance(codes, dict) and len(codes) > 0:
                # 检查是否已经被增强过了
                if result.get('has_code'):
                    logger.debug('检索结果已经包含代码增强，跳过')
                    continue
                
                content = result['content']
                
                # 检查内容中是否有占位符
                import re
                placeholders = re.findall(r'__CODE_(\d+)__', content)
                
                if placeholders:
                    # 按占位符替换代码
                    enhanced_content = content
                    restored_count = 0
                    missing_codes = []
                    
                    for match in re.finditer(r'__CODE_(\d+)__', content):
                        code_index = match.group(1)  # 直接使用字符串索引
                        if code_index in codes:
                            code_content = codes[code_index]
                            # 直接使用代码内容，因为它已经包含了```标记
                            enhanced_content = enhanced_content.replace(match.group(0), code_content)
                            restored_count += 1
                        else:
                            missing_codes.append(code_index)
                            logger.warning(f'检索结果中发现占位符 __CODE_{code_index}__ 但metadata中缺少对应的代码内容')
                    
                    result['content'] = enhanced_content
                    result['has_code'] = True
                    result['code_count'] = restored_count
                    result['missing_codes'] = missing_codes  # 记录缺失的代码索引
                    code_enhanced_count += 1
                    
                    if missing_codes:
                        logger.warning(f'恢复代码时发现缺失: 成功恢复{restored_count}个，缺失{len(missing_codes)}个: {missing_codes}')
                    else:
                        logger.debug(f'通过占位符为检索结果恢复了{restored_count}个代码块')
                else:
                    # 传统方式：在内容末尾添加代码
                    code_content_blocks = []
                    for index, code in codes.items():
                        # 如果代码已经包含```标记，直接使用；否则添加
                        if code.startswith('```') and code.endswith('```'):
                            code_content_blocks.append(code)
                        else:
                            code_content_blocks.append('```\n' + code + '\n```')
                    
                    code_content = '\n\n'.join(code_content_blocks)
                    
                    result['content'] = f"{result['content']}\n\n**相关代码：**\n{code_content}"
                    result['has_code'] = True
                    result['code_count'] = len(codes)
                    code_enhanced_count += 1
                    
                    logger.debug(f'为检索结果添加了{len(codes)}个代码块')
        
        if code_enhanced_count > 0:
            logger.info(f'代码恢复完成：为{code_enhanced_count}/{len(results)}个检索结果恢复了代码内容')
        
        return results

class BaseGenerator(ABC):
    """生成器基类，用于基于检索结果生成回答"""
    
    @abstractmethod
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        domains: List[str] = None,
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """基于上下文生成回答
        
        Args:
            query: 用户查询
            context: 检索到的相关上下文
            domains: 知识库领域
            stream: 是否使用流式输出
            **kwargs: 其他可选参数
            
        Returns:
            生成的回答文本或回答生成器
        """
        pass

class BaseEvaluator(ABC):
    """评估器基类"""
    
    @abstractmethod
    async def evaluate(
        self,
        query: str,
        response: str,
        references: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        评估生成的回答质量
        
        Args:
            query: 用户查询
            response: 生成的回答
            references: 参考的知识片段
            **kwargs: 其他参数
            
        Returns:
            评估结果
        """
        pass 