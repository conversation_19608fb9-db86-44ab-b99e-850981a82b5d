# -*- coding: utf-8 -*-
import traceback
from typing import List, Dict, Any, Optional, Union, AsyncGenerator, AsyncIterator
from ...core.logging import get_module_logger
import time

from .base import BaseRetriever, BaseGenerator, BaseEvaluator

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class RAGService:
    """RAG 服务主类"""
    
    def __init__(
        self,
        retriever: BaseRetriever,
        generator: BaseGenerator,
        evaluator: Optional[BaseEvaluator] = None
    ):
        """
        初始化 RAG 服务
        
        Args:
            retriever: 检索器实例
            generator: 生成器实例
            evaluator: 评估器实例（可选）
        """
        self.retriever = retriever
        self.generator = generator
        self.evaluator = evaluator
    
    async def process_query(
        self,
        query: str,
        stream: bool = False,
        context_messages: Optional[List[Dict[str, str]]] = None,
        domains: List[str] = None,
        **kwargs
    ) -> Union[Dict[str, Any], AsyncIterator[str]]:
        """
        处理用户查询
        
        Args:
            query: 用户查询
            stream: 是否使用流式输出
            context_messages: 对话历史上下文
            domains: 知识库领域
            **kwargs: 其他参数
            
        Returns:
            如果 stream=False，返回包含回答和评估结果的字典
            如果 stream=True，返回回答生成器
        """
        # 记录性能指标
        start_time = time.time()
        retrieve_start_time = 0
        retrieve_end_time = 0
        generation_start_time = 0
        first_token_time = 0
        
        try:
            logger.info(f'开始处理查询: \'{query[:50]}{"..." if len(query) > 50 else ""}\', stream={stream}')
            
            # 1. 检索相关内容
            retrieve_start_time = time.time()
            logger.debug(f'开始检索, 耗时: {(retrieve_start_time - start_time)*1000:.2f}ms')
            
            # 添加性能监控点 - 检索前准备阶段
            logger.info(f'性能监控: 检索前准备阶段耗时: {(retrieve_start_time - start_time)*1000:.2f}ms')
            
            references = await self.retriever.retrieve(query=query, domains=domains)
            retrieve_end_time = time.time()
            
            # 添加详细耗时统计
            logger.info(f'性能监控: 检索阶段耗时: {(retrieve_end_time - retrieve_start_time)*1000:.2f}ms')
            logger.info(f'检索完成, 找到{len(references)}条参考资料, 检索耗时: {(retrieve_end_time - retrieve_start_time)*1000:.2f}ms')
            
            # 2. 生成回答
            if stream:
                try:
                    # 流式生成
                    generation_start_time = time.time()
                    logger.debug(f'开始生成回答(流式), 检索到生成的准备时间: {(generation_start_time - retrieve_end_time)*1000:.2f}ms')
                    
                    # 添加详细耗时统计
                    logger.info(f'性能监控: 检索到生成准备阶段耗时: {(generation_start_time - retrieve_end_time)*1000:.2f}ms')
                    
                    # 记录LLM调用开始时间
                    llm_call_start_time = time.time()
                    response_stream = await self.generator.generate(
                        query=query,
                        context=references,
                        stream=True,
                        context_messages=context_messages,
                        **kwargs
                    )
                    # 记录LLM响应时间
                    llm_response_time = time.time()
                    
                    # 添加详细耗时统计
                    logger.info(f'性能监控: LLM生成器初始化耗时: {(llm_response_time - llm_call_start_time)*1000:.2f}ms')
                    
                    # 创建一个新的生成器，在最后添加引用信息并记录首个token时间
                    async def enhanced_stream():
                        nonlocal first_token_time
                        first_chunk = True
                        start_chunk_time = time.time()
                        chunk_count = 0
                        total_chunk_size = 0
                        
                        try:
                            if isinstance(response_stream, AsyncIterator):
                                async for chunk in response_stream:
                                    current_time = time.time()
                                    chunk_count += 1
                                    
                                    if isinstance(chunk, str):
                                        total_chunk_size += len(chunk)
                                    
                                    if first_chunk:
                                        first_token_time = current_time
                                        logger.info(f'首个token生成完成, 总耗时: {(first_token_time - start_time)*1000:.2f}ms')
                                        logger.info(f'首个token生成时间明细: 检索前准备={retrieve_start_time - start_time:.3f}s, 检索={retrieve_end_time - retrieve_start_time:.3f}s, 生成准备={generation_start_time - retrieve_end_time:.3f}s, 首个token生成={first_token_time - generation_start_time:.3f}s')
                                        logger.info(f'性能监控: 首个token到达耗时: {(first_token_time - llm_response_time)*1000:.2f}ms')
                                        first_chunk = False
                                    
                                    # 每10个token记录一次性能
                                    if chunk_count % 10 == 0:
                                        logger.debug(f'性能监控: 已生成{chunk_count}个文本块, 总大小{total_chunk_size}字节, 当前流速: {total_chunk_size/(current_time-start_chunk_time):.2f}字节/秒')
                                        
                                    yield chunk
                                    
                                # 在文本流结束后，发送引用信息
                                end_stream_time = time.time()
                                logger.debug('流式生成完成, 添加引用信息')
                                logger.info(f'性能监控: 流式生成总耗时: {(end_stream_time - start_chunk_time)*1000:.2f}ms, 平均流速: {total_chunk_size/(end_stream_time-start_chunk_time):.2f}字节/秒')
                                logger.info(f'性能监控: 生成了总计{chunk_count}个文本块, 总字节数: {total_chunk_size}')
                                yield {'references': references}
                            else:
                                first_token_time = time.time()
                                logger.info(f'非迭代流式响应生成完成, 总耗时: {(first_token_time - start_time)*1000:.2f}ms')
                                yield response_stream
                                yield {'references': references}
                        except StopAsyncIteration:
                            # 正常流结束
                            end_time = time.time()
                            logger.debug('流式生成器正常结束')
                            logger.info(f'性能监控: 流生成器StopAsyncIteration总耗时: {(end_time - start_time)*1000:.2f}ms')
                            yield {'references': references}
                        except Exception as e:
                            # 处理流式生成过程中的其他错误
                            end_time = time.time()
                            logger.error(f'流式生成过程中出错: {str(e)}')
                            logger.info(f'性能监控: 流生成器异常总耗时: {(end_time - start_time)*1000:.2f}ms')
                            yield {'error': str(e), 'references': references}
                        finally:
                            end_time = time.time()
                            # 添加每个阶段的详细耗时分析
                            logger.info(f'性能监控: 查询处理完成各阶段耗时分析:')
                            logger.info(f'- 检索前准备: {(retrieve_start_time - start_time)*1000:.2f}ms ({((retrieve_start_time - start_time)/(end_time - start_time)*100):.1f}%)')
                            logger.info(f'- 检索阶段: {(retrieve_end_time - retrieve_start_time)*1000:.2f}ms ({((retrieve_end_time - retrieve_start_time)/(end_time - start_time)*100):.1f}%)')
                            logger.info(f'- 生成准备: {(generation_start_time - retrieve_end_time)*1000:.2f}ms ({((generation_start_time - retrieve_end_time)/(end_time - start_time)*100):.1f}%)')
                            logger.info(f'- LLM响应: {(llm_response_time - llm_call_start_time)*1000:.2f}ms ({((llm_response_time - llm_call_start_time)/(end_time - start_time)*100):.1f}%)')
                            if first_token_time > 0:
                                logger.info(f'- 首个token: {(first_token_time - llm_response_time)*1000:.2f}ms ({((first_token_time - llm_response_time)/(end_time - start_time)*100):.1f}%)')
                                logger.info(f'- 剩余生成: {(end_time - first_token_time)*1000:.2f}ms ({((end_time - first_token_time)/(end_time - start_time)*100):.1f}%)')
                            logger.info(f'查询处理完成, 总耗时: {(end_time - start_time)*1000:.2f}ms')
                    
                    return enhanced_stream()
                except StopAsyncIteration:
                    # 正常的迭代结束，不是错误
                    logger.debug('流式生成正常结束')
                    # 返回一个空的异步生成器，但确保附加引用信息
                    async def done_generator():
                        yield ''
                        yield {'references': references}
                    return done_generator()
                except Exception as e:
                    logger.error(f'初始化流式生成时出错: {str(e)}')
                    # 返回带错误信息的生成器
                    async def error_generator():
                        yield f'抱歉，生成回答时出现错误: {str(e)}'
                        yield {'references': references}
                    return error_generator()
            else:
                # 同步生成
                generation_start_time = time.time()
                logger.info(f'性能监控: 开始同步生成, 检索到生成准备耗时: {(generation_start_time - retrieve_end_time)*1000:.2f}ms')
                
                response = await self.generator.generate(
                    query=query,
                    context=references,
                    context_messages=context_messages,
                    **kwargs
                )
                
                generation_end_time = time.time()
                logger.info(f'性能监控: 同步生成完成, 耗时: {(generation_end_time - generation_start_time)*1000:.2f}ms')
                
                # 3. 评估结果（如果启用）
                evaluation = None
                evaluation_start_time = time.time()
                if self.evaluator and isinstance(response, str):
                    evaluation = await self.evaluator.evaluate(
                        query=query,
                        response=response,
                        references=references
                    )
                evaluation_end_time = time.time()
                
                if self.evaluator:
                    logger.info(f'性能监控: 评估阶段耗时: {(evaluation_end_time - evaluation_start_time)*1000:.2f}ms')
                
                # 总体耗时统计
                end_time = time.time()
                logger.info(f'性能监控: 同步查询处理完成各阶段耗时分析:')
                logger.info(f'- 检索前准备: {(retrieve_start_time - start_time)*1000:.2f}ms ({((retrieve_start_time - start_time)/(end_time - start_time)*100):.1f}%)')
                logger.info(f'- 检索阶段: {(retrieve_end_time - retrieve_start_time)*1000:.2f}ms ({((retrieve_end_time - retrieve_start_time)/(end_time - start_time)*100):.1f}%)')
                logger.info(f'- 生成准备: {(generation_start_time - retrieve_end_time)*1000:.2f}ms ({((generation_start_time - retrieve_end_time)/(end_time - start_time)*100):.1f}%)')
                logger.info(f'- 生成阶段: {(generation_end_time - generation_start_time)*1000:.2f}ms ({((generation_end_time - generation_start_time)/(end_time - start_time)*100):.1f}%)')
                if self.evaluator:
                    logger.info(f'- 评估阶段: {(evaluation_end_time - evaluation_start_time)*1000:.2f}ms ({((evaluation_end_time - evaluation_start_time)/(end_time - start_time)*100):.1f}%)')
                logger.info(f'查询处理完成, 总耗时: {(end_time - start_time)*1000:.2f}ms')
                
                return {
                    'response': response,
                    'references': references,
                    'evaluation': evaluation
                }
                
        except Exception as e:
            end_time = time.time()
            logger.error(f'处理查询失败: {str(e)}')
            logger.error(f'处理查询失败: {traceback.format_exc()}')
            logger.info(f'性能监控: 查询处理失败, 总耗时: {(end_time - start_time)*1000:.2f}ms')
            if stream:
                # 返回错误信息生成器，确保前端能收到错误信息
                async def error_response_generator():
                    yield f'抱歉，处理查询失败: {str(e)}'
                    yield {'error': str(e), 'references': []}
                return error_response_generator()
            return {
                'error': f'处理查询失败: {str(e)}',
                'references': [],
                'evaluation': None
            }

    async def get_references_for_query(self, query: str, domains: List[str] = None) -> Dict[str, Any]:
        """
        获取查询的引用信息
        
        Args:
            query: 用户查询
            domains: 知识库领域
        Returns:
            包含引用信息的字典
        """
        # 添加性能监控
        start_time = time.time()
        try:
            # 检索相关内容
            references = await self.retriever.retrieve(query=query, domains=domains)
            
            end_time = time.time()
            logger.info(f'性能监控: 获取引用信息完成, 耗时: {(end_time - start_time)*1000:.2f}ms')
            
            return {
                'references': references
            }
        except Exception as e:
            end_time = time.time()
            logger.error(f'获取引用信息失败: {str(e)}')
            logger.info(f'性能监控: 获取引用信息失败, 耗时: {(end_time - start_time)*1000:.2f}ms')
            return {
                'references': []
            } 