# -*- coding: utf-8 -*-
from typing import List, Dict, Any
from ...core.logging import get_module_logger

from .base import BaseEvaluator
from ...llm import BaseLLMService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class RelevanceEvaluator(BaseEvaluator):
    """相关性评估器"""
    
    def __init__(self, llm_service: BaseLLMService):
        """
        初始化相关性评估器
        
        Args:
            llm_service: LLM 服务实例，用于评估相关性
        """
        self.llm_service = llm_service
    
    async def evaluate(
        self,
        query: str,
        response: str,
        references: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        评估回答与查询的相关性
        
        Args:
            query: 用户查询
            response: 生成的回答
            references: 参考的知识片段
            **kwargs: 其他参数
            
        Returns:
            评估结果，包含相关性分数和详细分析
        """
        try:
            # 构建评估提示词
            prompt = f'''请评估以下回答与用户查询的相关性:

用户查询: {query}

生成的回答: {response}

参考知识:
{self._format_references(references)}

请从以下几个方面进行评估:
1. 回答是否直接解答了用户的问题
2. 回答中使用的知识是否来自参考资料
3. 回答的详细程度是否合适

请给出 0-1 之间的相关性分数，并说明理由。
'''
            
            # 获取评估结果
            evaluation = await self.llm_service.generate(prompt)
            
            # 解析评估结果
            # TODO: 实现更结构化的结果解析
            return {
                'type': 'relevance',
                'result': evaluation
            }
            
        except Exception as e:
            logger.error(f'相关性评估失败: {str(e)}')
            return {
                'type': 'relevance',
                'error': str(e)
            }
    
    def _format_references(self, references: List[Dict[str, Any]]) -> str:
        """格式化参考知识"""
        formatted = []
        for i, ref in enumerate(references, 1):
            formatted.append(f'{i}. {ref.get("content", "")}')
        return '\n'.join(formatted)

class QualityEvaluator(BaseEvaluator):
    """质量评估器"""
    
    def __init__(self, llm_service: BaseLLMService):
        """
        初始化质量评估器
        
        Args:
            llm_service: LLM 服务实例，用于评估质量
        """
        self.llm_service = llm_service
    
    async def evaluate(
        self,
        query: str,
        response: str,
        references: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        评估回答的质量
        
        Args:
            query: 用户查询
            response: 生成的回答
            references: 参考的知识片段
            **kwargs: 其他参数
            
        Returns:
            评估结果，包含质量分数和详细分析
        """
        try:
            # 构建评估提示词
            prompt = f'''请评估以下回答的质量:

生成的回答: {response}

请从以下几个方面进行评估:
1. 语言表达是否清晰准确
2. 逻辑结构是否合理
3. 专业术语使用是否恰当
4. 是否存在错误信息

请给出 0-1 之间的质量分数，并说明理由。
'''
            
            # 获取评估结果
            evaluation = await self.llm_service.generate(prompt)
            
            # 解析评估结果
            # TODO: 实现更结构化的结果解析
            return {
                'type': 'quality',
                'result': evaluation
            }
            
        except Exception as e:
            logger.error(f'质量评估失败: {str(e)}')
            return {
                'type': 'quality',
                'error': str(e)
            } 