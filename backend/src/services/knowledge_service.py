# -*- coding: utf-8 -*-
import json
import traceback
from typing import List, Dict, Any, Optional, Tuple
import os
import asyncio

import aiofiles
from ..core.logging import get_module_logger

from ..core.document_parser import ParserFactory
from ..core.exceptions import ProcessingError, NotFoundError
from ..core.config import settings
from ..core.document_chunker import MarkdownChunker
from ..core.content_cleaner import ContentCleaner
from ..core.content_enhancer import ContentEnhancer
from ..repositories.import_task_repository import ImportTaskRepository
from ..repositories.knowledge_repository import KnowledgeRepository
from ..llm.base import BaseLLMService
from ..services.llm_service_factory import LLMServiceFactory
from ..services.rag.retrievers import MultiQueryRetriever, VectorRetriever, HybridRetriever

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class KnowledgeService:
    """知识库服务类"""
    
    def __init__(
        self,
        knowledge_repository: KnowledgeRepository,
        import_task_repository: Optional[ImportTaskRepository] = None,
        llm_service: Optional[BaseLLMService] = None
    ):
        """
        初始化知识库服务
        
        Args:
            knowledge_repository: 知识库仓库实例
            import_task_repository: 导入任务仓库实例
            llm_service: LLM服务实例，如果不提供则自动创建
        """
        self.knowledge_repository = knowledge_repository
        self.import_task_repository = import_task_repository
        self.llm_service = llm_service
        self.content_cleaner = ContentCleaner()
        self.content_enhancer = ContentEnhancer()
        
    async def process_document(
        self, 
        title: str, 
        content: str,
        url: Optional[str] = None,
        source_type: str = "manual",
        domain: str = None,
        metadata: Optional[Dict[str, Any]] = None,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> str:
        """
        处理文档并添加到知识库
        
        Args:
            title: 文档标题
            content: 文档内容（Markdown格式）
            url: 文档URL
            source_type: 来源类型
            domain: 知识领域
            metadata: 元数据
            chunk_size: 分块大小，如果不指定则使用配置值
            chunk_overlap: 分块重叠大小，如果不指定则使用配置值
            
        Returns:
            添加的知识条目ID
        """
        try:
            if not metadata:
                metadata = {}

            # 验证source_type
            if source_type not in ["manual", "web_page", "local_import"]:
                raise ProcessingError(f'不支持的来源类型: {source_type}, 暂时只支持"manual"或"web_page"或"local_import"')
                
            # 1. 清洗文档内容
            cleaned_content = self.content_cleaner.clean_content(content)

            # 文档级增强
            if settings.CONTENT_ENHANCEMENT_CONFIGS["generate_document_summary"]:
                summary = await self.content_enhancer.generate_document_summary(
                    title=title,
                    content=cleaned_content,
                )
                metadata["summary"] = summary
            
            # 2. 分割文档内容
            chunks = self._split_text(
                cleaned_content, 
                domain=domain,
                chunk_size=chunk_size or settings.CHUNK_SIZE,
                chunk_overlap=chunk_overlap or settings.CHUNK_OVERLAP
            )
            
            # 3. 处理知识块
            processed_chunks = []
            base_metadata = {
                "source_type": source_type,
                "title": title
            }
            if metadata:
                base_metadata.update(metadata)
            if domain:
                base_metadata["domain"] = domain

            if url:
                base_metadata["url"] = url
            elif base_metadata and base_metadata.get("url", None):
                url = base_metadata["url"]
            
            for i, chunk in enumerate(chunks):
                # 获取分块内容和Token数量
                chunk_text = chunk["content"]
                if not chunk_text.strip():
                    continue
                
                # 合并元数据
                chunk_metadata = {
                    **base_metadata,
                    "chunk_index": i,
                    "total_chunks": len(chunks)
                }
                
                # 如果分块中已有元数据，合并
                if "metadata" in chunk:
                    for key, value in chunk["metadata"].items():
                        # 不覆盖现有的基础元数据
                        if key not in chunk_metadata:
                            chunk_metadata[key] = value
                
                processed_chunk = {
                    "content": chunk_text,
                    "metadata": chunk_metadata
                }
                
                processed_chunks.append(processed_chunk)
            logger.debug(f"{title}分割出{len(processed_chunks)}个chunk")

            # 对所有块进行知识增强
            processed_chunks = await self.content_enhancer.enhance_chunks(processed_chunks)

            # 4. 添加到知识库
            knowledge_id = await self.knowledge_repository.add_knowledge(
                title=title,
                content=cleaned_content,
                url=url,
                source_type=source_type,
                domain=domain,
                metadata=metadata or {},
                chunks=processed_chunks
            )            
            return knowledge_id
            
        except Exception as e:
            logger.error(f"处理文档失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise ProcessingError(f"处理文档失败: {str(e)}")
    
    def _split_text(
        self, 
        text: str, 
        domain: Optional[str] = None, 
        chunk_size: int = 500, 
        chunk_overlap: int = 50
    ) -> List[Dict[str, Any]]:
        """
        将文本分割成块
        
        Args:
            text: 要分割的文本
            title: 文档标题，用于增强搜索内容
            domain: 知识领域，用于选择分块策略
            chunk_size: 每块的最大token数量
            chunk_overlap: 块之间的重叠token数量
            
        Returns:
            分割后的文本块列表
        """
        if not text or chunk_size <= 0:
            return []
        
        chunker = MarkdownChunker(chunk_size=chunk_size, overlap_size=chunk_overlap, domain=domain)
        chunks = chunker.chunk_markdown(text)
        
        # 验证分块完整性
        validation_stats = chunker.validate_chunks_integrity(chunks)
        if validation_stats["mismatched_chunks"]:
            logger.warning(f"分块过程中发现代码占位符与metadata不匹配的问题，详情请查看上述日志")
        
        return chunks
        
    async def search_knowledge(
        self, 
        query: str, 
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        use_multi_query: bool = None,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        搜索知识库
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            min_score: 最小相似度分数
            use_multi_query: 是否使用多查询扩展，None则使用配置默认值
            domains: 搜索领域过滤
            **kwargs: 其他参数
            
        Returns:
            搜索结果列表
        """
        # 确定是否使用多查询扩展
        if use_multi_query is None:
            use_multi_query = settings.USE_MULTI_QUERY
        
        try:
            
            # 创建基础向量检索器
            vector_retriever = VectorRetriever(self.knowledge_repository)
            
            if use_multi_query:
                # 确保LLM服务实例存在
                if not self.llm_service:
                    self.llm_service = LLMServiceFactory.create_service()
                
                # 创建用于生成查询变体的LLM服务实例
                variant_llm_service = LLMServiceFactory.create_variant_service()
                
                # 创建多查询检索器
                multi_query_retriever = MultiQueryRetriever(
                    base_retriever=vector_retriever,
                    llm_service=self.llm_service,
                    variant_llm_service=variant_llm_service
                )
                
                # 使用多查询检索器
                results = await multi_query_retriever.retrieve(
                    query=query,
                    limit=limit,
                    min_score=min_score,
                    domains=domains,
                    **kwargs
                )
                
                return results
            else:
                # 使用普通向量检索
                return await vector_retriever.retrieve(
                    query=query,
                    limit=limit,
                    min_score=min_score,
                    domains=domains,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"搜索知识库失败: {str(e)}")
            # 回退到基本检索
            try:
                return await self.knowledge_repository.search_relevant_content(
                    query=query,
                    limit=limit,
                    min_score=min_score,
                    domains=domains
                )
            except Exception as inner_e:
                logger.error(f"回退搜索也失败: {str(inner_e)}")
                return []  # 返回空列表，确保API不会崩溃
    
    async def search_with_hybrid(
        self,
        query: str,
        vector_weight: float = 0.5,
        bm25_weight: float = 0.5,
        fusion_method: str = "rrf",
        limit: int = settings.DEFAULT_SEARCH_LIMIT,
        min_score: float = settings.MIN_RELEVANCE_SCORE,
        domains: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        使用混合检索并返回详细信息（包括统计信息）
        
        Args:
            query: 查询文本
            vector_weight: 向量检索权重
            bm25_weight: BM25检索权重
            fusion_method: 融合方法
            limit: 返回结果数量限制
            min_score: 最小相似度分数
            domains: 搜索领域过滤
            **kwargs: 其他参数
            
        Returns:
            包含搜索结果和统计信息的字典
        """
        try:
            # 创建混合检索器
            hybrid_retriever = HybridRetriever(
                knowledge_repository=self.knowledge_repository,
                vector_weight=vector_weight,
                bm25_weight=bm25_weight,
                fusion_method=fusion_method
            )
            
            # 执行检索
            results = await hybrid_retriever.retrieve(
                query=query,
                limit=limit,
                min_score=min_score,
                domains=domains,
                **kwargs
            )
            
            # 获取统计信息
            stats = await hybrid_retriever.get_retrieval_stats(query, limit)
            
            return {
                "results": results,
                "stats": stats,
                "config": {
                    "vector_weight": vector_weight,
                    "bm25_weight": bm25_weight,
                    "fusion_method": fusion_method,
                    "limit": limit,
                    "min_score": min_score
                }
            }
            
        except Exception as e:
            logger.error(f"混合检索失败: {str(e)}")
            return {
                "results": [],
                "stats": {},
                "config": {
                    "vector_weight": vector_weight,
                    "bm25_weight": bm25_weight,
                    "fusion_method": fusion_method,
                    "limit": limit,
                    "min_score": min_score
                },
                "error": str(e)
            }
    
    async def get_knowledge_by_id(self, knowledge_id: str) -> Dict[str, Any]:
        """
        获取知识条目详情
        
        Args:
            knowledge_id: 知识条目ID
            
        Returns:
            知识条目详情
        """
        knowledge = await self.knowledge_repository.get_knowledge_by_id(knowledge_id)
        if not knowledge:
            raise NotFoundError(f"知识条目不存在: {knowledge_id}")
        return knowledge
        
    async def update_knowledge(
        self,
        knowledge_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        url: Optional[str] = None,
        domain: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        reprocess_chunks: bool = False,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> bool:
        """
        更新知识条目
        
        Args:
            knowledge_id: 知识条目ID
            title: 标题
            content: 内容
            url: URL
            domain: 知识领域
            metadata: 元数据
            reprocess_chunks: 是否重新处理文档块
            chunk_size: 分块大小，如果不指定则使用配置值
            chunk_overlap: 分块重叠大小，如果不指定则使用配置值
            
        Returns:
            是否更新成功
        """
        try:
            if not metadata:
                metadata = {}
                
            # 1. 如果需要重新处理文档块
            chunks = None
            if reprocess_chunks and content is not None:
                # 清洗内容
                cleaned_content = self.content_cleaner.clean_content(content)
                
                # 2. 分割文档内容
                chunks = self._split_text(
                    cleaned_content,
                    domain=domain,
                    chunk_size=chunk_size or settings.CHUNK_SIZE,
                    chunk_overlap=chunk_overlap or settings.CHUNK_OVERLAP
                )
                
                # 使用内容增强器为文档生成摘要
                if settings.CONTENT_ENHANCEMENT_CONFIGS["generate_document_summary"]:
                    summary = await self.content_enhancer.generate_document_summary(
                        title=title,
                        content=cleaned_content,
                    )
                    metadata["summary"] = summary
                
                # 处理知识块
                chunks = []
                base_metadata = {}
                if title:
                    base_metadata["title"] = title
                if url:
                    base_metadata["url"] = url
                if metadata:
                    base_metadata.update(metadata)
                if domain:
                    base_metadata["domain"] = domain
                
                for i, chunk in enumerate(chunks):
                    chunk_text = chunk["content"]
                    if not chunk_text.strip():
                        continue
                    
                    # 合并元数据
                    chunk_metadata = {
                        **base_metadata,
                        "chunk_index": i,
                        "total_chunks": len(chunks)
                    }
                    
                    # 如果分块中已有元数据，合并
                    if "metadata" in chunk:
                        for key, value in chunk["metadata"].items():
                            # 不覆盖现有的基础元数据
                            if key not in chunk_metadata:
                                chunk_metadata[key] = value
                    
                    logger.debug(f"chunk_metadata: {chunk_metadata}")

                    processed_chunk = {
                        "content": chunk_text,
                        "metadata": chunk_metadata
                    }
                    chunks.append(processed_chunk)
            

            # 对所有块进行知识增强
            chunks = await self.content_enhancer.enhance_chunks(chunks)

            # 2. 更新知识条目
            success = await self.knowledge_repository.update_knowledge(
                knowledge_id=knowledge_id,
                title=title,
                content=content,
                url=url,
                domain=domain,
                metadata=metadata,
                chunks=chunks if reprocess_chunks else None
            )
            
            return success
            
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"更新知识失败: {str(e)}")
            raise ProcessingError(f"更新知识失败: {str(e)}")
            
    async def delete_knowledge(self, knowledge_id: str) -> bool:
        """
        删除知识条目
        
        Args:
            knowledge_id: 知识条目ID
            
        Returns:
            是否删除成功
        """
        try:
            # 1. 从数据库删除
            success = await self.knowledge_repository.delete_knowledge(knowledge_id)
            
            if success:
                logger.info(f"删除知识条目成功: {knowledge_id}")
            else:
                logger.error(f"删除知识条目失败: {knowledge_id}")
            return success
            
        except Exception as e:
            logger.error(f"删除知识条目失败: {str(e)}")
            return False
        
    async def delete_all_knowledge(self) -> int:
        """
        删除所有知识条目
        
        Returns:
            删除的知识条目数量
        """
        return await self.knowledge_repository.delete_all_knowledge()
        
    async def get_knowledge_list(
        self, 
        page: int = 1, 
        page_size: int = 20,
        source_type: Optional[str] = None,
        domain: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取知识条目列表
        
        Args:
            page: 页码
            page_size: 每页数量
            source_type: 来源类型筛选
            domain: 知识领域筛选
            
        Returns:
            分页后的知识条目列表
        """
        return await self.knowledge_repository.get_all_knowledge(
            page=page,
            page_size=page_size,
            source_type=source_type,
            domain=domain
        )
        
    async def import_documents(
        self,
        file_paths: List[str],
        source_type: str = "manual",
        domain: Optional[str] = None,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> str:
        """
        批量导入文档
        
        Args:
            file_paths: 文件路径列表
            source_type: 来源类型，只能是"manual"或"web_page"或"local_import"，默认为"manual"
            domain: 知识领域，用于选择分块策略
            chunk_size: 分块大小
            chunk_overlap: 分块重叠大小
            
        Returns:
            导入任务ID
        """
        if not self.import_task_repository:
            raise ProcessingError("导入任务仓库未初始化")
            
        try:
            # 1. 创建导入任务
            task = await self.import_task_repository.create_task(
                total_files=len(file_paths)
            )
            
            # 2. 更新任务状态
            await self.import_task_repository.update_task(
                task_id=task.id,
                status="processing"
            )
            
            # 3. 处理文件
            processed_files = 0
            failed_files = 0
            results = {
                "success": [],
                "failed": []
            }
            
            for file_path in file_paths:
                try:
                    # 解析文档，获取统一JSON格式，内容统一为markdown
                    doc = await ParserFactory.async_parse(file_path)

                    # 添加到知识库，使用统一处理流程
                    knowledge_id = await self.process_document(
                        title=doc["title"],
                        content=doc["content"],
                        source_type=source_type,
                        domain=domain,
                        metadata={
                            **doc["metadata"],
                            "import_task_id": task.id
                        },
                        url=doc.get("url", None),
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )
                    
                    # 记录成功
                    results["success"].append({
                        "file_path": file_path,
                        "knowledge_id": knowledge_id
                    })
                    processed_files += 1
                    
                except Exception as e:
                    # 记录失败
                    results["failed"].append({
                        "file_path": file_path,
                        "error": str(e)
                    })
                    failed_files += 1
                    logger.error(f"处理文件失败: {file_path}, 错误: {str(e)}")
                    logger.error(traceback.format_exc())
                
                # 更新进度
                progress = (processed_files + failed_files) / len(file_paths) * 100
                await self.import_task_repository.update_task(
                    task_id=task.id,
                    processed_files=processed_files,
                    failed_files=failed_files,
                    progress=progress,
                    result=results
                )
            
            # 4. 完成导入
            status = "completed" if failed_files == 0 else "completed_with_errors"
            await self.import_task_repository.update_task(
                task_id=task.id,
                status=status
            )
            
            return task.id
            
        except Exception as e:
            logger.error(f"批量导入文档失败: {str(e)}")
            
            # 更新任务状态为失败
            if 'task' in locals():
                await self.import_task_repository.update_task(
                    task_id=task.id,
                    status="failed",
                    error=str(e)
                )
                
            raise ProcessingError(f"批量导入文档失败: {str(e)}")

    async def get_import_task(self, task_id: str) -> Dict[str, Any]:
        """
        获取导入任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务详情
        """
        if not self.import_task_repository:
            raise ProcessingError("导入任务仓库未初始化")
            
        task = await self.import_task_repository.get_task(task_id)
        return task.to_dict()

    async def list_import_tasks(
        self,
        skip: int = 0,
        limit: int = 20
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取导入任务列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务列表和总数
        """
        if not self.import_task_repository:
            raise ProcessingError("导入任务仓库未初始化")
            
        tasks = await self.import_task_repository.list_tasks(skip, limit)
        total = await self.import_task_repository.get_task_count()
        
        return [task.to_dict() for task in tasks], total
    