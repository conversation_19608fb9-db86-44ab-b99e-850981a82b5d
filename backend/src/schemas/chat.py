from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict

class MessageReference(BaseModel):
    title: str
    url: str
    content: str
    relevance: float

class ChatMessage(BaseModel):
    role: str = Field(..., pattern="^(user|assistant)$")
    content: str
    timestamp: datetime

class ChatMessageInDB(ChatMessage):
    id: str
    session_id: str
    references: List[MessageReference] = []

    class Config:
        from_attributes = True

class ChatHistoryMessage(ChatMessageInDB):
    pass

class SendMessageRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    context_messages: List[ChatMessage] = []
    stream: bool = False
    domains: Optional[List[str]] = Field(None, description="知识库领域筛选")

class ChatResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: dict
    timestamp: str

class SendMessageResponse(ChatResponse):
    data: dict = Field(..., example={
        "answer": "string",
        "references": [
            {
                "title": "string",
                "url": "string",
                "content": "string",
                "relevance": 0.95
            }
        ],
        "session_id": "string",
        "message_id": "string"
    })

class ChatHistoryResponse(ChatResponse):
    data: dict = Field(..., example={
        "messages": [
            {
                "id": "string",
                "role": "user|assistant",
                "content": "string",
                "timestamp": "string",
                "references": []
            }
        ],
        "total": 0,
        "page_size": 20,
        "page_number": 1
    })

class ChatSession(BaseModel):
    id: str
    title: str
    last_message: Optional[str] = None
    last_message_time: Optional[datetime] = None
    message_count: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class ChatSessionsResponse(ChatResponse):
    data: dict = Field(..., example={
        "sessions": [
            {
                "id": "string",
                "title": "string",
                "last_message": "string",
                "last_message_time": "string",
                "message_count": 0,
                "created_at": "string",
                "updated_at": "string"
            }
        ],
        "total": 0,
        "page_size": 20,
        "page_number": 1
    })

class CreateSessionResponse(ChatResponse):
    data: dict = Field(..., example={
        "session": {
            "id": "string",
            "title": "string",
            "message_count": 0,
            "created_at": "string",
            "updated_at": "string"
        }
    }) 