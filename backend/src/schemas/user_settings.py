from typing import Optional
from pydantic import BaseModel, ConfigDict

class UserSettingsBase(BaseModel):
    theme: Optional[str] = "light"
    language: Optional[str] = "zh"
    notification_enabled: Optional[bool] = True

class UserSettingsCreate(UserSettingsBase):
    pass

class UserSettingsUpdate(UserSettingsBase):
    pass

class UserSettingsInDBBase(UserSettingsBase):
    id: int
    user_id: int

    model_config = ConfigDict(from_attributes=True)

class UserSettings(UserSettingsInDBBase):
    pass

class UserSettingsResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: dict
    timestamp: str 