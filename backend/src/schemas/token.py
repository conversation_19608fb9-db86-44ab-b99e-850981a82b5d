from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenPayload(BaseModel):
    sub: Optional[int] = None

class UserInfo(BaseModel):
    id: str
    username: str
    role: str

class LoginResponseData(BaseModel):
    token: str
    expires_in: int
    user: UserInfo

class LoginResponse(BaseModel):
    code: int
    message: str
    data: LoginResponseData
    timestamp: str 