from typing import Dict, List, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator

from ..core.config import settings
from ..core.constants import KnowledgeDomain

# 请求模型
class KnowledgeBase(BaseModel):
    """知识条目基础模型"""
    title: str = Field(..., min_length=1, max_length=255, description="知识标题")
    content: str = Field(..., min_length=1, description="知识内容")
    url: Optional[str] = Field(None, description="知识来源URL")
    source_type: str = Field("manual", description="知识来源类型，如manual、crawl、import等")
    domain: str = Field(KnowledgeDomain.DEFAULT.value, description="知识领域")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

class KnowledgeCreate(KnowledgeBase):
    """创建知识条目请求模型"""
    pass

class KnowledgeUpdate(BaseModel):
    """更新知识条目请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="知识标题")
    content: Optional[str] = Field(None, min_length=1, description="知识内容")
    url: Optional[str] = Field(None, description="知识来源URL")
    domain: Optional[str] = Field(None, description="知识领域")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    reprocess_chunks: bool = Field(False, description="是否重新处理知识块")

class KnowledgeSearchRequest(BaseModel):
    """知识搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索查询")
    limit: int = Field(settings.DEFAULT_SEARCH_LIMIT, ge=1, le=20, description="返回结果数量")
    min_score: float = Field(settings.MIN_RELEVANCE_SCORE, ge=0.0, le=1.0, description="最小相似度分数")
    use_multi_query: Optional[bool] = Field(None, description="是否启用多查询扩展，None表示使用系统默认配置")
    domains: Optional[List[str]] = Field(None, description="搜索领域")

# 响应模型
class KnowledgeChunkResponse(BaseModel):
    """知识块响应模型"""
    id: str = Field(..., description="知识块ID")
    content: str = Field(..., description="知识块内容")
    metadata: Optional[Dict[str, Any]] = Field({}, description="元数据")

class KnowledgeResponse(BaseModel):
    """知识条目响应模型（搜索结果）"""
    id: str = Field(..., description="知识条目ID")
    title: str = Field(..., description="知识标题")
    content: str = Field(..., description="知识内容")
    url: Optional[str] = Field(None, description="知识来源URL")
    relevance: float = Field(..., description="相关度分数")
    type: str = Field(..., description="结果类型，document或chunk")
    knowledge_id: Optional[str] = Field(None, description="如果是chunk，所属的知识条目ID")
    domain: str = Field(..., description="知识领域")

class KnowledgeDetailResponse(BaseModel):
    """知识条目详情响应模型"""
    id: str = Field(..., description="知识条目ID")
    title: str = Field(..., description="知识标题")
    content: str = Field(..., description="知识内容")
    url: Optional[str] = Field(None, description="知识来源URL")
    metadata: Dict[str, Any] = Field({}, description="元数据")
    source_type: str = Field(..., description="知识来源类型")
    domain: str = Field(..., description="知识领域")
    chunks: List[KnowledgeChunkResponse] = Field([], description="知识块列表")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

class KnowledgeListItem(BaseModel):
    """知识条目列表项模型"""
    id: str = Field(..., description="知识条目ID")
    title: str = Field(..., description="知识标题")
    url: Optional[str] = Field(None, description="知识来源URL")
    source_type: str = Field(..., description="知识来源类型")
    domain: str = Field(..., description="知识领域")
    chunk_count: int = Field(0, description="知识块数量")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

class KnowledgeListResponse(BaseModel):
    """知识条目列表响应模型"""
    total: int = Field(..., description="总条目数")
    items: List[KnowledgeListItem] = Field([], description="知识条目列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页条目数")
    total_pages: int = Field(..., description="总页数")

class ImportTaskData(BaseModel):
    """导入任务数据模型"""
    id: str
    status: str
    total_files: int
    processed_files: int
    failed_files: int
    progress: float
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True

class ImportTaskResponse(BaseModel):
    """导入任务响应模型"""
    code: int = 200
    message: str = "success"
    data: ImportTaskData
    timestamp: str

class ImportTaskListData(BaseModel):
    """导入任务列表数据模型"""
    tasks: List[ImportTaskData]
    total: int
    page: int
    page_size: int

class ImportTaskListResponse(BaseModel):
    """导入任务列表响应模型"""
    code: int = 200
    message: str = "success"
    data: ImportTaskListData
    timestamp: str 