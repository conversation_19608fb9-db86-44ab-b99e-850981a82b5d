"""
领域过滤器 - 用于判断用户问题是否属于鸿蒙开发领域
"""

import re
from typing import List, Dict, Tuple, Optional

# 鸿蒙开发相关关键词
HARMONY_KEYWORDS = [
    # 鸿蒙系统相关
    "鸿蒙", "HarmonyOS", "OpenHarmony", "HarmonyOS Next", "HMOS", 
    "ArkUI", "ArkTS", "ArkCompiler", "方舟编译器", "方舟",
    
    # 开发框架和组件
    "Stage模型", "FA模型", "UIAbility", "ServiceExtensionAbility", "DataShareExtensionAbility",
    "FormExtensionAbility", "Ability", "AbilityStage", "WindowStage", "Component",
    "AppStorage", "LocalStorage", "Environment", "EnvironmentCallback",
    
    # UI组件
    "@Builder", "@Component", "@Entry", "@Extend", "@CustomDialog", "@Observed", "@ObjectLink",
    "@Prop", "@State", "@Link", "@Provide", "@Consume", "@Watch",
    "build", "aboutToAppear", "aboutToDisappear", "onPageShow", "onPageHide", "onBackPress",
    "Column", "Row", "Stack", "Flex", "Grid", "List", "Swiper", "Tabs", "Text", "Button",
    "Image", "TextInput", "Toggle", "Slider", "Progress", "LoadingProgress", "Divider",
    
    # API和服务
    "hilog", "preferences", "relationalStore", "fileio", "request", "http", "socket",
    "worker", "util", "app", "window", "display", "sensor", "geolocation", "vibrator",
    "brightness", "battery", "power", "bundle", "featureAbility", "reminderAgent",
    "notificationManager", "backgroundTaskManager", "resourceManager", "i18n",
    
    # 工具和开发环境
    "DevEco Studio", "HUAWEI DevEco Studio", "HDB", "HDC", "hdc_std", "Previewer",
    "模拟器", "真机调试", "日志", "调试", "编译", "打包", "签名", "证书",
    
    # 应用分发和上架
    "AppGallery", "华为应用市场", "应用上架", "应用分发", "HAP", "HAR", "HSP", "HPK",
    "应用包", "原子化服务", "Feature", "多HAP", "Entry", "Feature Ability",
    
    # 开发语言
    "ArkTS", "TS", "TypeScript", "JS", "JavaScript", "C", "C++",
    
    # 设计规范
    "HarmonyOS设计", "原子化设计", "设计规范", "自然畅享设计",
    
    # 硬件和设备
    "分布式", "Super Device", "超级终端", "一次开发，多端部署", "多设备协同",
    "手机", "平板", "智慧屏", "智能手表", "车机", "IoT"
]

# 生活类关键词（用于识别非开发领域问题）
LIFE_KEYWORDS = [
    # 烹饪相关
    "菜谱", "食谱", "做饭", "烹饪", "煮", "炒", "蒸", "煎", "炸", "烤", "煮饭", 
    "食材", "调料", "厨房", "美食", "食物", "食品", "饮食", "餐", "吃", "喝",
    "早餐", "午餐", "晚餐", "宵夜", "点心", "零食", "小吃",
    "米饭", "面条", "面包", "蛋糕", "饼干", "糕点",
    
    # 常见食物
    "蛋炒饭", "炒饭", "炒面", "盖浇饭", "汤", "粥", "饺子", "包子", "馒头", "花卷",
    "牛肉", "猪肉", "鸡肉", "羊肉", "鱼", "虾", "蟹", "贝", "海鲜",
    "蔬菜", "水果", "豆腐", "豆制品", "奶制品", "乳制品",
    
    # 家居生活
    "打扫", "清洁", "洗衣", "洗碗", "拖地", "擦窗", "整理", "收纳",
    "装修", "家具", "家电", "床", "沙发", "桌子", "椅子", "柜子",
    
    # 健康医疗
    "感冒", "发烧", "头痛", "咳嗽", "喉咙痛", "肚子痛", "腹泻", "便秘",
    "药", "医院", "医生", "护士", "诊所", "急诊", "挂号", "体检",
    
    # 旅游出行
    "旅游", "旅行", "出游", "景点", "景区", "门票", "酒店", "民宿", "航班", "火车",
    "高铁", "动车", "汽车", "公交", "地铁", "出租车", "打车", "导航",
    
    # 娱乐休闲
    "电影", "电视剧", "综艺", "动漫", "游戏", "小说", "音乐", "歌曲", "演唱会",
    "KTV", "唱歌", "跳舞", "运动", "健身", "瑜伽", "游泳", "跑步", "篮球", "足球",
    
    # 日常事务
    "天气", "日期", "时间", "日历", "提醒", "闹钟", "备忘录",
    "购物", "买", "卖", "价格", "打折", "促销", "优惠", "便宜", "贵",
    "快递", "物流", "快递单号", "收货", "发货", "退货", "换货",
    
    # 社交关系
    "朋友", "同学", "同事", "亲戚", "家人", "父母", "子女", "兄弟", "姐妹",
    "男朋友", "女朋友", "夫妻", "婚姻", "恋爱", "相亲", "约会", "聚会", "聚餐",
    
    # 教育学习
    "学习", "考试", "作业", "论文", "毕业", "升学", "考研", "考公", "考证",
    "学校", "大学", "高中", "初中", "小学", "幼儿园", "老师", "学生", "课程",
    
    # 工作就业
    "工作", "就业", "求职", "面试", "简历", "职位", "岗位", "薪资", "工资",
    "加班", "请假", "休假", "辞职", "跳槽", "升职", "降职", "绩效", "考核",
    
    # 金融理财
    "钱", "存款", "贷款", "信用卡", "借记卡", "银行", "支付", "转账", "汇款",
    "投资", "理财", "股票", "基金", "债券", "保险", "房产", "房子", "房贷"
]

def is_harmony_domain(query: str) -> Tuple[bool, Optional[str]]:
    """
    判断用户查询是否属于鸿蒙开发领域
    
    Args:
        query: 用户输入的查询文本
        
    Returns:
        (is_harmony, reason): 是否属于鸿蒙领域的布尔值和原因说明
    """
    # 转换为小写进行匹配
    query_lower = query.lower()
    
    # 检查是否包含鸿蒙相关关键词
    harmony_matches = [keyword for keyword in HARMONY_KEYWORDS if keyword.lower() in query_lower]
    
    # 检查是否包含生活类关键词
    life_matches = [keyword for keyword in LIFE_KEYWORDS if keyword.lower() in query_lower]
    
    # 如果包含鸿蒙关键词且不包含生活关键词，则认为是鸿蒙领域
    if harmony_matches and not life_matches:
        return True, f"检测到鸿蒙相关关键词: {', '.join(harmony_matches[:3])}"
    
    # 如果包含生活关键词且不包含鸿蒙关键词，则认为不是鸿蒙领域
    if life_matches and not harmony_matches:
        return False, f"检测到非鸿蒙领域关键词: {', '.join(life_matches[:3])}"
    
    # 如果两种关键词都包含，则进行更深入的分析
    if harmony_matches and life_matches:
        # 计算关键词匹配的数量和权重
        harmony_score = len(harmony_matches) * 1.5  # 给鸿蒙关键词更高的权重
        life_score = len(life_matches)
        
        if harmony_score > life_score:
            return True, f"主要包含鸿蒙相关内容，但也有非领域内容"
        else:
            return False, f"主要是非鸿蒙领域内容，虽然提及了一些鸿蒙关键词"
    
    # 如果没有明确的关键词匹配，默认不属于鸿蒙领域
    return False, "未检测到明确的鸿蒙开发相关内容"

def get_rejection_response(query: str) -> str:
    """
    生成拒绝回答非鸿蒙领域问题的回复
    
    Args:
        query: 用户输入的查询文本
        
    Returns:
        拒绝回答的文本
    """
    _, reason = is_harmony_domain(query)
    
    return f"""很抱歉，我是一个专注于鸿蒙开发领域的助手，无法回答关于"{query}"的问题。

{reason}

如果您有关于HarmonyOS、ArkTS、ArkUI或其他鸿蒙开发相关的问题，我很乐意为您提供帮助。""" 