from typing import List, Dict, Any, Optional
import os

from ..core.constants import KnowledgeDomain
from ..core.logging import get_module_logger
from ..repositories.knowledge_repository import KnowledgeRepository
from ..services.knowledge_service import KnowledgeService
from .fetchers import FetcherFactory
from .config import settings
import json
from datetime import datetime

# 初始化模块级logger
logger = get_module_logger(__name__)

class KnowledgeBase:
    """知识库爬虫管理器"""
    
    def __init__(self, repository: Optional[KnowledgeRepository] = None):
        self.repository = repository
        self.service = KnowledgeService(repository) if repository else None
        # 创建临时存储目录（用于测试）
        self.temp_dir = os.path.join(settings.data_dir, "temp_knowledge")
        os.makedirs(self.temp_dir, exist_ok=True)
        
    async def fetch_and_store(self, url: str, source_type: str = "web_page", domain: Optional[str] = KnowledgeDomain.DEFAULT) -> Dict[str, Any]:
        """
        获取并存储知识
        
        Args:
            url: 内容URL
            source_type: 内容源类型，默认为web_page
            domain: 知识领域，默认为default
        Returns:
            Dict: 存储的知识条目
        """
        try:
            # 获取内容
            fetcher = FetcherFactory.create(source_type)
            if not fetcher.validate_url(url):
                raise ValueError(f"Invalid URL: {url}")
                
            content = fetcher.fetch(url)
            
            # 如果有repository，使用service存储
            if self.service:
                knowledge_id = await self.service.process_document(
                    title=url,  # 暂时使用URL作为标题
                    content=content,
                    url=url,
                    source_type=source_type,
                    domain=domain,
                    metadata={"fetched_at": datetime.now().isoformat()}
                )
                return {"id": knowledge_id, "status": "success"}
            
            # 否则使用文件系统存储（用于测试）
            knowledge_item = {
                "url": url,
                "source_type": source_type,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
            filename = f"{hash(url)}.json"
            filepath = os.path.join(self.temp_dir, filename)
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(knowledge_item, f, ensure_ascii=False, indent=2)
                
            logger.info(f"Successfully stored knowledge from {url}")
            return knowledge_item
            
        except Exception as e:
            logger.error(f"Error processing {url}: {str(e)}")
            return {
                "url": url,
                "source_type": source_type,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }
            
    async def batch_fetch(self, urls: List[str], source_type: str = "web_page", domain: Optional[str] = KnowledgeDomain.DEFAULT) -> List[Dict[str, Any]]:
        """
        批量获取并存储知识
        
        Args:
            urls: URL列表
            source_type: 内容源类型
            domain: 知识领域，默认为default
        Returns:
            List[Dict]: 处理结果列表
        """
        results = []
        for url in urls:
            result = await self.fetch_and_store(url, source_type, domain)
            results.append(result)
        return results
        
    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        简单的知识检索（后续可以替换为向量检索）
        
        Args:
            query: 检索关键词
            
        Returns:
            List[Dict]: 匹配的知识条目列表
        """
        results = []
        for filename in os.listdir(self.knowledge_dir):
            if not filename.endswith(".json"):
                continue
                
            filepath = os.path.join(self.knowledge_dir, filename)
            try:
                with open(filepath, "r", encoding="utf-8") as f:
                    item = json.load(f)
                    if query.lower() in item.get("content", "").lower():
                        results.append(item)
            except Exception as e:
                logger.error(f"Error reading {filepath}: {str(e)}")
                
        return results
        
    def list_all(self) -> List[Dict[str, Any]]:
        """获取所有知识条目"""
        results = []
        for filename in os.listdir(self.knowledge_dir):
            if not filename.endswith(".json"):
                continue
                
            filepath = os.path.join(self.knowledge_dir, filename)
            try:
                with open(filepath, "r", encoding="utf-8") as f:
                    results.append(json.load(f))
            except Exception as e:
                logger.error(f"Error reading {filepath}: {str(e)}")
                
        return results 