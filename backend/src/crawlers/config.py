import os
from typing import Dict, Any
import yaml
from loguru import logger
from pathlib import Path

class Settings:
    """配置管理"""
    
    def __init__(self):
        # 获取项目根目录的绝对路径
        root_dir = Path(__file__).parent.parent.parent.parent.absolute()
        config_path_env = os.getenv("CONFIG_PATH")
        
        if config_path_env:
            # 如果环境变量设置了路径，优先使用
            self.config_path = config_path_env
        else:
            # 否则使用项目根目录下的配置文件
            self.config_path = os.path.join(root_dir, "config", "crawler_config.yaml")
            
        logger.info(f"加载爬虫配置文件: {self.config_path}")
        self.config: Dict[str, Any] = self._load_config()
        
        # 数据存储路径
        self.data_dir = self.config.get("data_dir", "data")
        self.vector_db_path = os.path.join(self.data_dir, "vector_db")
        
        # 文本处理配置
        self.text_config = self.config.get("text_processing", {})
        self.min_length = self.text_config.get("min_length", 10)
        self.max_length = self.text_config.get("max_length", 1000)
        
        # 模型配置
        self.model_config = self.config.get("model", {})
        self.model_name = self.model_config.get(
            "name",
            "paraphrase-multilingual-MiniLM-L12-v2"
        )
        
        # 爬虫配置
        self.crawler_config = self.config.get("crawlers", {})
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return yaml.safe_load(f)
            else:
                logger.warning(f"配置文件不存在: {self.config_path}，将使用默认配置")
                # 尝试创建配置文件目录
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                return {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            return {}

settings = Settings() 