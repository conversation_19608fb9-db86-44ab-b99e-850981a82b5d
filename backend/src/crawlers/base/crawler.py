from abc import ABC, abstractmethod
from typing import List, Dict, Any
import requests
from bs4 import BeautifulSoup
from ...core.logging import get_module_logger

# 初始化模块级logger
logger = get_module_logger(__name__)

class BaseCrawler(ABC):
    """爬虫基类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
    
    @abstractmethod
    def crawl(self) -> List[Dict[str, Any]]:
        """执行爬虫任务"""
        pass
    
    def get_page(self, url: str) -> BeautifulSoup:
        """获取页面内容"""
        try:
            response = self.session.get(url, headers=self.headers)
            response.raise_for_status()
            return BeautifulSoup(response.text, "html.parser")
        except Exception as e:
            logger.error(f"Error fetching page {url}: {str(e)}")
            raise 