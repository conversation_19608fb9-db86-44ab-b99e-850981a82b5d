from typing import List, Dict, Any
import uuid
from ..base.crawler import BaseCrawler
from ...core.logging import get_module_logger
import time
import json
import requests
import random

# 初始化模块级logger
logger = get_module_logger(__name__)

class HarmonyApiCrawler(BaseCrawler):
    """鸿蒙OS API文档爬虫"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://developer.huawei.com/consumer/cn/doc/harmonyos-references/"
        self.doc_api_url = "https://svc-drcn.developer.huawei.com/community/servlet/consumer/cn/documentPortal/getDocumentById"
        self.catalog_api_url = "https://svc-drcn.developer.huawei.com/community/servlet/consumer/cn/documentPortal/getCatalogTree"
        
        # 配置API请求头
        self.headers.update({
            "authority": "svc-drcn.developer.huawei.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://developer.huawei.com",
            "priority": "u=1, i",
            "referer": "https://developer.huawei.com/",
            "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "Content-Type": "application/json"
        })
    
    def get_catalog_tree(self) -> Dict[str, Any]:
        """获取文档目录树，提取所有文档ID和路径"""
        try:
            # 添加随机延迟
            time.sleep(random.uniform(1, 3))
            
            # 准备请求数据
            data = {
                "catalogName": "harmonyos-references",
                "language": "cn"
            }
            
            # 尝试多次请求
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.session.post(
                        self.catalog_api_url,
                        headers=self.headers,
                        json=data,
                        timeout=30
                    )
                    response.raise_for_status()
                    
                    # 打印响应信息以便调试
                    logger.debug(f"Catalog response status: {response.status_code}")
                    
                    # 解析响应内容
                    result = response.json()
                    
                    # 检查响应格式
                    if not result:
                        raise ValueError("Empty response")
                        
                    # 检查响应结构
                    if "code" not in result or "value" not in result:
                        logger.warning(f"Unexpected catalog response structure: {result.keys()}")
                        if "message" in result:
                            logger.warning(f"Error message: {result['message']}")
                        raise ValueError("Invalid response format")
                    
                    # 检查响应状态码
                    if result["code"] != 0:
                        logger.warning(f"API error: {result['message']}")
                        raise ValueError(f"API error: {result['message']}")
                    
                    # 解析目录树，提取所有文档ID
                    catalog_tree = result["value"]
                    logger.debug(f"Catalog tree structure: {json.dumps(catalog_tree, ensure_ascii=False, indent=2)[:1000]}...")
                    
                    # 提取所有文档ID
                    document_ids = self._extract_document_ids_from_tree(catalog_tree)
                    logger.info(f"Found {len(document_ids)} documents in catalog tree")
                    
                    # 提取所有文档路径
                    document_paths = self._extract_document_paths_from_tree(catalog_tree)
                    logger.info(f"Found {len(document_paths)} document paths in catalog tree")
                    
                    return {
                        "document_ids": document_ids,
                        "document_paths": document_paths
                    }
                    
                except requests.RequestException as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                    time.sleep(random.uniform(2, 5))
                    
        except Exception as e:
            logger.error(f"Error fetching catalog tree: {str(e)}")
            raise
    
    def _extract_document_ids_from_tree(self, catalog_tree: List[Dict[str, Any]]) -> List[str]:
        """从目录树中递归提取所有文档ID"""
        document_ids = []
        
        def extract_ids(nodes):
            if not nodes:
                return
                
            for node in nodes:
                node_name = node.get("nodeName", "未命名")
                node_id = node.get("nodeId", "")
                
                # 处理叶子节点
                if node.get("isLeaf") is True:
                    # 提取文档ID
                    if "relateDocument" in node and node["relateDocument"]:
                        doc_id = node["relateDocument"]
                        document_ids.append(doc_id)
                        logger.debug(f"提取文档ID: {doc_id}, 节点: {node_name}")
                    elif "relateDocId" in node and node["relateDocId"]:
                        doc_id = node["relateDocId"]
                        document_ids.append(doc_id)
                        logger.debug(f"提取文档ID: {doc_id}, 节点: {node_name}")
                    elif "idpNodeId" in node and node["idpNodeId"]:
                        potential_id = node["idpNodeId"]
                        if isinstance(potential_id, str) and potential_id.startswith("ZH-CN_TOPIC_"):
                            document_ids.append(potential_id)
                            logger.debug(f"提取文档ID: {potential_id}, 节点: {node_name}")
                    else:
                        logger.warning(f"叶子节点缺少文档引用: {node_name} (nodeId: {node_id})")
                        
                # 递归处理子节点
                if "children" in node and isinstance(node["children"], list):
                    extract_ids(node["children"])
        
        # 处理目录树结构
        if isinstance(catalog_tree, list):
            extract_ids(catalog_tree)
        elif isinstance(catalog_tree, dict):
            if "catalogTreeList" in catalog_tree and isinstance(catalog_tree["catalogTreeList"], list):
                extract_ids(catalog_tree["catalogTreeList"])
            elif "children" in catalog_tree and isinstance(catalog_tree["children"], list):
                extract_ids(catalog_tree["children"])
            else:
                logger.warning(f"目录树结构异常: {list(catalog_tree.keys())}")
        
        # 去重返回
        unique_ids = list(set(document_ids))
        logger.info(f"从目录树中提取了 {len(unique_ids)} 个唯一文档ID")
        return unique_ids
        
    def _extract_document_paths_from_tree(self, catalog_tree: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """从目录树中递归提取文档ID和其对应的路径
        
        Args:
            catalog_tree: 目录树数据
            
        Returns:
            文档ID到路径列表的映射字典
        """
        document_paths = {}
        
        def extract_path(nodes, current_path=[]):
            if not nodes:
                return
                
            for node in nodes:
                node_name = node.get("nodeName", "未命名")
                
                # 构建当前节点的路径
                node_path = current_path + [node_name]
                
                # 处理叶子节点
                if node.get("isLeaf") is True:
                    # 提取文档ID
                    doc_id = None
                    if "relateDocument" in node and node["relateDocument"]:
                        doc_id = node["relateDocument"]
                    elif "relateDocId" in node and node["relateDocId"]:
                        doc_id = node["relateDocId"]
                    elif "idpNodeId" in node and node["idpNodeId"]:
                        potential_id = node["idpNodeId"]
                        if isinstance(potential_id, str) and potential_id.startswith("ZH-CN_TOPIC_"):
                            doc_id = potential_id
                    
                    if doc_id:
                        # 保存文档ID和路径列表
                        document_paths[doc_id] = node_path
                        logger.debug(f"文档ID: {doc_id}, 路径: {'>'.join(node_path)}")
                
                # 递归处理子节点
                if "children" in node and isinstance(node["children"], list):
                    extract_path(node["children"], node_path)
        
        # 处理目录树结构
        if isinstance(catalog_tree, list):
            extract_path(catalog_tree)
        elif isinstance(catalog_tree, dict):
            if "catalogTreeList" in catalog_tree and isinstance(catalog_tree["catalogTreeList"], list):
                extract_path(catalog_tree["catalogTreeList"])
            elif "children" in catalog_tree and isinstance(catalog_tree["children"], list):
                extract_path(catalog_tree["children"])
            else:
                logger.warning(f"目录树结构异常: {list(catalog_tree.keys())}")
        
        logger.info(f"从目录树中提取了 {len(document_paths)} 个文档路径")
        return document_paths
    
    def get_document(self, object_id: str) -> Dict[str, Any]:
        """获取文档内容
        
        Args:
            object_id: 文档ID
            
        Returns:
            文档内容字典
        """
        try:
            # 处理特殊格式的文档ID
            if object_id.startswith("ZH-CN_TOPIC_"):
                logger.debug(f"处理特殊格式文档ID: {object_id}")
            
            # 添加请求延迟
            time.sleep(random.uniform(1, 3))
            
            # 准备请求数据
            data = {
                "objectId": object_id,
                "version": "",
                "catalogName": "harmonyos-references",
                "language": "cn"
            }
            
            # 尝试多次请求
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.session.post(
                        self.doc_api_url,
                        headers=self.headers,
                        json=data,
                        timeout=30
                    )
                    response.raise_for_status()
                    
                    logger.debug(f"响应状态码: {response.status_code}")
                    logger.debug(f"响应头: {response.headers}")
                    
                    # 解析响应内容
                    result = response.json()
                    logger.debug(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 检查响应格式
                    if not result:
                        raise ValueError("响应内容为空")
                        
                    # 检查响应结构
                    if "code" not in result or "value" not in result:
                        logger.warning(f"响应结构异常: {result.keys()}")
                        if "message" in result:
                            logger.warning(f"错误信息: {result['message']}")
                        raise ValueError("响应格式无效")
                    
                    # 检查响应状态码
                    if result["code"] != 0:
                        logger.warning(f"API错误: {result['message']}")
                        raise ValueError(f"API错误: {result['message']}")
                        
                    return result["value"]
                    
                except requests.RequestException as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"第 {attempt + 1} 次请求失败: {str(e)}")
                    time.sleep(random.uniform(2, 5))
                    
        except Exception as e:
            logger.error(f"获取文档 {object_id} 失败: {str(e)}")
            raise
            
    def _extract_html_elements(self, html_content: str) -> Dict[str, List[str]]:
        """从HTML内容中提取代码示例和图片URL
        
        Args:
            html_content: HTML文本内容
            
        Returns:
            包含代码示例和图片URL的字典
        """
        import re
        from bs4 import BeautifulSoup

        result = {
            "code_examples": [],
            "image_urls": []
        }
        
        if not html_content:
            return result
            
        try:
            # 解析HTML内容
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取代码示例
            code_tags = soup.find_all(['pre', 'code'])
            for tag in code_tags:
                code_text = tag.get_text(strip=True)
                if code_text and len(code_text) > 10:
                    result["code_examples"].append({
                        "language": tag.get('class', [''])[0] if tag.get('class') else "",
                        "code": code_text
                    })
                    logger.debug(f"提取代码示例: {code_text[:50]}...")
            
            # 提取图片URL
            img_tags = soup.find_all('img')
            for img in img_tags:
                src = img.get('src', '')
                if src:
                    # 处理相对URL
                    if not src.startswith(('http://', 'https://')):
                        if src.startswith('/'):
                            src = f"https://developer.huawei.com{src}"
                        else:
                            src = f"https://developer.huawei.com/consumer/cn/doc/harmonyos-references/{src}"
                    result["image_urls"].append(src)
                    logger.debug(f"提取图片URL: {src}")
            
            logger.info(f"提取了 {len(result['code_examples'])} 个代码示例和 {len(result['image_urls'])} 个图片")
            
        except Exception as e:
            logger.error(f"提取HTML元素失败: {str(e)}")
            
        return result
            
    def _extract_content(self, doc_data: Dict[str, Any], url: str, doc_path: List[str] = None) -> Dict[str, Any]:
        """从API响应中提取内容
        
        Args:
            doc_data: API返回的文档数据
            url: 文档URL
            doc_path: 文档在目录树中的路径列表
            
        Returns:
            处理后的文档内容字典
        """
        try:
            # 提取文档标题
            title = doc_data.get("title", "")
            
            # 提取文档分类
            category = []
            if "businessTypeName" in doc_data:
                category.append(doc_data["businessTypeName"])
            if "businessName" in doc_data:
                category.append(doc_data["businessName"])
            
            # 提取文档内容
            content = ""
            if "content" in doc_data and isinstance(doc_data["content"], dict):
                content = doc_data["content"].get("content", "")
            
            # 提取导航目录
            toc = []
            if "anchorList" in doc_data:
                toc = doc_data["anchorList"]
            
            # 提取代码示例和图片
            html_elements = {"code_examples": [], "image_urls": []}
            if content:
                html_elements = self._extract_html_elements(content)
            
            # 提取元数据
            metadata = {
                "docId": doc_data.get("docId", ""),
                "fileName": doc_data.get("fileName", ""),
                "version": doc_data.get("version", ""),
                "updatedDate": doc_data.get("updatedDate", ""),
                "businessName": doc_data.get("businessName", ""),
                "businessType": doc_data.get("businessType", ""),
                "businessTypeName": doc_data.get("businessTypeName", ""),
                "versionLabels": doc_data.get("versionLabels", [])
            }
            
            # 添加文档路径信息
            if doc_path:
                metadata["path"] = doc_path
            
            # 构建结果
            result = {
                "title": title,
                "category": category,
                "url": url,
                "content": content,
                "toc": toc,
                "code_examples": html_elements["code_examples"],
                "image_urls": html_elements["image_urls"],
                "metadata": metadata,
                "timestamp": time.time()
            }
            
            # 记录提取结果摘要
            logger.debug(f"文档 {url} 内容提取摘要:")
            logger.debug(f"标题: {result['title']}")
            logger.debug(f"分类: {', '.join(result['category'])}")
            logger.debug(f"内容长度: {len(result['content'])}")
            logger.debug(f"目录项数: {len(result['toc'])}")
            logger.debug(f"代码示例数: {len(result['code_examples'])}")
            logger.debug(f"图片数: {len(result['image_urls'])}")
            if doc_path:
                logger.debug(f"路径: {'>'.join(doc_path)}")
            
            return result
            
        except Exception as e:
            logger.error(f"提取内容失败: {str(e)}")
            return {}
    
    def _get_related_documents(self, doc_data: Dict[str, Any]) -> List[str]:
        """获取相关文档ID列表
        
        Args:
            doc_data: 文档数据
            
        Returns:
            相关文档ID列表
        """
        related_docs = []
        
        # 从relateDocList获取相关文档
        if "relateDocList" in doc_data and isinstance(doc_data["relateDocList"], list):
            for doc in doc_data["relateDocList"]:
                if "relateDocument" in doc and doc["relateDocument"]:
                    related_docs.append(doc["relateDocument"])
                    logger.debug(f"获取相关文档ID: {doc['relateDocument']}")
                elif "fileName" in doc and doc["fileName"]:
                    related_docs.append(doc["fileName"])
                    logger.debug(f"获取相关文档ID: {doc['fileName']}")
                elif "docId" in doc and doc["docId"]:
                    related_docs.append(doc["docId"])
                    logger.debug(f"获取相关文档ID: {doc['docId']}")
                else:
                    logger.warning(f"相关文档缺少ID: {doc}")
        
        # 从relateDocs获取相关文档
        if "relateDocs" in doc_data and isinstance(doc_data["relateDocs"], list):
            for doc in doc_data["relateDocs"]:
                if isinstance(doc, str):
                    related_docs.append(doc)
                    logger.debug(f"获取相关文档ID: {doc}")
                elif isinstance(doc, dict):
                    for field in ["id", "docId", "fileName", "relateDocument"]:
                        if field in doc and doc[field]:
                            related_docs.append(doc[field])
                            logger.debug(f"获取相关文档ID: {doc[field]}")
                            break
        
        # 检查relateDocId字段
        if "relateDocId" in doc_data and doc_data["relateDocId"]:
            doc_id = doc_data["relateDocId"]
            related_docs.append(doc_id)
            logger.debug(f"获取相关文档ID: {doc_id}")
        
        # 添加当前文档ID
        if "fileName" in doc_data and doc_data["fileName"] and doc_data["fileName"] not in related_docs:
            related_docs.append(doc_data["fileName"])
            logger.debug(f"添加当前文档ID: {doc_data['fileName']}")
        
        # 去重
        unique_docs = list(set(related_docs))
        logger.info(f"获取了 {len(unique_docs)} 个相关文档ID")
        return unique_docs
    
    def save_results(self, documents: List[Dict[str, Any]], output_dir: str = "./data/harmony_api_references") -> None:
        """保存爬取结果到文件
        
        Args:
            documents: 文档列表
            output_dir: 输出目录路径
        """
        import os
        import json
        from datetime import datetime
        
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成时间戳文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = os.path.join(output_dir, f"summary_{timestamp}.json")
            
            # 保存汇总信息
            with open(summary_file, 'w', encoding='utf-8') as f:
                summary = {
                    "total_documents": len(documents),
                    "timestamp": timestamp,
                    "documents": []
                }
                
                for i, doc in enumerate(documents):
                    # 为每个文档创建单独的文件
                    doc_id = doc["metadata"].get("docId", "doc" + str(uuid.uuid4()))
                    doc_filename = f"{doc_id}_{timestamp}.json"
                    doc_path = os.path.join(output_dir, doc_filename)
                    
                    # 保存文档详情
                    doc["metadata"]["docId"] = doc_id
                    with open(doc_path, 'w', encoding='utf-8') as doc_file:
                        json.dump(doc, doc_file, ensure_ascii=False, indent=2)

                    # 添加文档摘要
                    doc_summary = {
                        "id": doc_id,
                        "title": doc["title"],
                        "url": doc["url"],
                        "category": doc["category"],
                        "content_length": len(doc["content"]),
                        "code_examples": len(doc["code_examples"]),
                        "images": len(doc["image_urls"]),
                        "filename": doc_filename
                    }
                    summary["documents"].append(doc_summary)
                
                # 保存汇总信息
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(documents)} 个文档到 {output_dir}")
            logger.info(f"汇总文件: {summary_file}")
            
            return os.path.abspath(summary_file)
            
        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            raise

    def crawl(self, max_docs: int = 50) -> List[Dict[str, Any]]:
        """执行爬虫任务
        
        Args:
            max_docs: 最大爬取文档数，默认为50
            
        Returns:
            爬取的文档列表
        """
        try:
            documents = []
            visited_ids = set()
            failed_ids = set()
            document_paths = {}
            
            # 获取文档ID列表和路径信息
            logger.info("获取目录树...")
            try:
                catalog_data = self.get_catalog_tree()
                docs_to_visit = catalog_data["document_ids"]
                document_paths = catalog_data["document_paths"]
                
                if not docs_to_visit:
                    raise ValueError("目录树中未找到文档ID")
                    
                logger.info(f"获取到 {len(docs_to_visit)} 个文档ID和 {len(document_paths)} 个文档路径")
            except Exception as e:
                logger.warning(f"获取目录树失败: {str(e)}")
                logger.warning("使用默认起始文档")
                docs_to_visit = ["development-intro-api"]  # 修改默认起始文档ID
                
            # 设置重试参数
            max_retries_per_doc = 2
            
            logger.info(f"开始爬取文档，最大数量: {max_docs}")
            
            while docs_to_visit and len(visited_ids) < max_docs:
                doc_id = docs_to_visit.pop(0)
                
                # 跳过已处理的文档
                if doc_id in visited_ids or doc_id in failed_ids:
                    continue
                
                logger.info(f"正在爬取文档 {len(visited_ids)+1}/{max_docs}: {doc_id}")
                
                # 获取文档路径
                doc_path = document_paths.get(doc_id)
                if doc_path:
                    logger.info(f"文档路径: {'>'.join(doc_path)}")
                
                # 尝试获取文档
                retry_count = 0
                success = False
                
                while retry_count < max_retries_per_doc and not success:
                    try:
                        # 获取文档内容
                        doc_data = self.get_document(doc_id)
                        
                        # 构建文档URL
                        doc_url = f"https://developer.huawei.com/consumer/cn/doc/harmonyos-references/{doc_id}"
                        
                        # 提取内容
                        doc_content = self._extract_content(doc_data, doc_url, doc_path)
                        if doc_content:
                            documents.append(doc_content)
                            logger.info(f"成功提取文档内容: {doc_url}")
                            
                            # 获取相关文档
                            new_docs = self._get_related_documents(doc_data)
                            logger.info(f"找到 {len(new_docs)} 个相关文档")
                            
                            # 添加新文档ID到队列
                            for new_id in new_docs:
                                if new_id not in visited_ids and new_id not in failed_ids:
                                    docs_to_visit.append(new_id)
                            
                            success = True
                        else:
                            logger.warning(f"未能提取文档内容: {doc_url}")
                            retry_count += 1
                            time.sleep(random.uniform(2, 5))
                        
                    except Exception as e:
                        logger.error(f"处理文档 {doc_id} 失败: {str(e)}")
                        retry_count += 1
                        if retry_count < max_retries_per_doc:
                            logger.info(f"重试文档 {doc_id}，第 {retry_count+1} 次")
                            time.sleep(random.uniform(3, 8))
                        else:
                            failed_ids.add(doc_id)
                            logger.warning(f"文档 {doc_id} 处理失败，已重试 {max_retries_per_doc} 次")
                            break
                
                # 标记为已访问
                visited_ids.add(doc_id)
                
                # 检查是否达到最大文档数
                if len(documents) >= max_docs:
                    logger.info(f"已达到最大文档数: {max_docs}")
                    break
                
                # 添加延迟
                time.sleep(random.uniform(2, 4))
                
            # 输出统计信息
            logger.info(f"爬取完成。已处理 {len(visited_ids)} 个文档，失败 {len(failed_ids)} 个，提取 {len(documents)} 个")
            
            return documents
            
        except Exception as e:
            logger.error(f"爬取鸿蒙API文档失败: {str(e)}")
            raise 