from typing import List, Dict, Any
from ..base.crawler import BaseCrawler
from ...core.logging import get_module_logger
import yaml
import os
from urllib.parse import urljoin
import time
from bs4 import BeautifulSoup

# 初始化模块级logger
logger = get_module_logger(__name__)

class HarmonyDocsCrawler(BaseCrawler):
    """鸿蒙开发文档爬虫"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://developer.harmonyos.com/cn/docs/"
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载爬虫配置"""
        config_path = os.path.join("config", "crawler_config.yaml")
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                return config.get("harmony_docs", {})
        except Exception as e:
            logger.error(f"Error loading crawler config: {str(e)}")
            return {}
    
    def _extract_content(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """提取页面内容"""
        try:
            # 提取文档标题
            title = soup.select_one("h1.doc-title")
            title = title.text.strip() if title else ""
            
            # 提取文档分类
            breadcrumb = soup.select("nav.breadcrumb li")
            category = [item.text.strip() for item in breadcrumb if item.text.strip()]
            
            # 提取文档内容
            content = soup.select_one("article.doc-content")
            if not content:
                return {}
                
            # 提取代码示例
            code_blocks = content.select("pre code")
            code_examples = [block.text.strip() for block in code_blocks]
            
            # 提取API表格
            tables = content.select("table")
            api_tables = []
            for table in tables:
                headers = [th.text.strip() for th in table.select("th")]
                rows = []
                for tr in table.select("tbody tr"):
                    row = [td.text.strip() for td in tr.select("td")]
                    if len(row) == len(headers):
                        rows.append(dict(zip(headers, row)))
                if rows:
                    api_tables.append(rows)
            
            return {
                "title": title,
                "category": category,
                "url": url,
                "content": content.text.strip(),
                "code_examples": code_examples,
                "api_tables": api_tables,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {str(e)}")
            return {}
    
    def _get_doc_links(self, soup: BeautifulSoup) -> List[str]:
        """获取文档链接"""
        links = []
        for a in soup.select("a[href]"):
            href = a.get("href", "")
            if href.startswith("/cn/docs/"):
                links.append(urljoin(self.base_url, href))
        return links
    
    def crawl(self) -> List[Dict[str, Any]]:
        """执行爬虫任务"""
        try:
            documents = []
            visited_urls = set()
            urls_to_visit = [self.base_url]
            
            max_depth = self.config.get("depth", 3)
            rate_limit = self.config.get("rate_limit", 1)
            
            while urls_to_visit and len(visited_urls) < max_depth:
                url = urls_to_visit.pop(0)
                if url in visited_urls:
                    continue
                    
                logger.info(f"Crawling {url}")
                soup = self.get_page(url)
                
                # 提取页面内容
                doc_content = self._extract_content(soup, url)
                if doc_content:
                    documents.append(doc_content)
                
                # 获取新的链接
                new_links = self._get_doc_links(soup)
                urls_to_visit.extend([link for link in new_links if link not in visited_urls])
                
                visited_urls.add(url)
                time.sleep(rate_limit)  # 限制请求频率
                
            return documents
            
        except Exception as e:
            logger.error(f"Error crawling HarmonyOS docs: {str(e)}")
            raise 