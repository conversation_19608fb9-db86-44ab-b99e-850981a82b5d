from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

class ContentFetcher(ABC):
    """内容获取器基类"""
    
    @abstractmethod
    def fetch(self, url: str) -> str:
        """获取内容的抽象方法"""
        pass

    def validate_url(self, url: str) -> bool:
        """验证URL是否合法且可访问"""
        return True  # 子类可以重写此方法进行具体验证

class FetcherFactory:
    """内容获取器工厂类"""
    
    _fetchers = {}
    
    @classmethod
    def register(cls, source_type: str, fetcher_class):
        """注册内容获取器"""
        cls._fetchers[source_type] = fetcher_class
        
    @classmethod
    def create(cls, source_type: str, **kwargs):
        """创建内容获取器实例"""
        fetcher_class = cls._fetchers.get(source_type)
        if not fetcher_class:
            raise ValueError(f"Unsupported source type: {source_type}")
        return fetcher_class(**kwargs)

    @classmethod
    def get_supported_types(cls) -> list:
        """获取支持的内容源类型"""
        return list(cls._fetchers.keys()) 