import requests
from urllib.parse import urlparse
from .base import ContentFetcher, FetcherFactory
from ...core.logging import get_module_logger

logger = get_module_logger(__name__)

class WebPageFetcher(ContentFetcher):
    """网页内容获取器"""
    
    def __init__(self, timeout: int = 30, headers: dict = None):
        self.timeout = timeout
        self.headers = headers or {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def fetch(self, url: str) -> str:
        """
        获取网页内容
        
        Args:
            url: 网页URL
            
        Returns:
            str: 网页内容
            
        Raises:
            requests.exceptions.RequestException: 请求失败时抛出异常
        """
        try:
            response = requests.get(url, timeout=self.timeout, headers=self.headers)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching web page {url}: {str(e)}")
            raise
            
    def validate_url(self, url: str) -> bool:
        """
        验证URL是否为有效的网页地址
        
        Args:
            url: 要验证的URL
            
        Returns:
            bool: URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

# 注册获取器
FetcherFactory.register('web_page', WebPageFetcher) 