import requests
import logging
from typing import Optional, Dict
from urllib.parse import urlparse
import json
import yaml
from .base import ContentFetcher, FetcherFactory

logger = logging.getLogger(__name__)

class ApiDocFetcher(ContentFetcher):
    """API文档获取器"""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 headers: Optional[Dict[str, str]] = None,
                 timeout: int = 30):
        """
        初始化API文档获取器
        
        Args:
            api_key: API密钥
            headers: 自定义请求头
            timeout: 请求超时时间（秒）
        """
        self.api_key = api_key
        self.timeout = timeout
        self.headers = headers or {}
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
            
    def fetch(self, url: str) -> str:
        """
        获取API文档内容
        
        Args:
            url: API文档URL
            
        Returns:
            str: API文档内容
            
        Raises:
            requests.exceptions.RequestException: 请求失败时抛出异常
        """
        try:
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 尝试解析响应内容
            content_type = response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                return self._format_json(response.json())
            elif 'application/yaml' in content_type or 'application/x-yaml' in content_type:
                return self._format_yaml(response.text)
            else:
                return response.text
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching API doc {url}: {str(e)}")
            raise
            
    def _format_json(self, data: Dict) -> str:
        """格式化JSON数据"""
        try:
            return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error formatting JSON: {str(e)}")
            return str(data)
            
    def _format_yaml(self, content: str) -> str:
        """格式化YAML数据"""
        try:
            data = yaml.safe_load(content)
            return yaml.dump(data, allow_unicode=True, default_flow_style=False)
        except Exception as e:
            logger.error(f"Error formatting YAML: {str(e)}")
            return content
            
    def validate_url(self, url: str) -> bool:
        """验证是否为有效的API文档URL"""
        try:
            result = urlparse(url)
            path = result.path.lower()
            return all([result.scheme, result.netloc]) and (
                path.endswith('.json') or 
                path.endswith('.yaml') or 
                path.endswith('.yml') or
                'api' in path or 
                'swagger' in path or 
                'openapi' in path
            )
        except Exception:
            return False

# 注册获取器
FetcherFactory.register('api_doc', ApiDocFetcher) 