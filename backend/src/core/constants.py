"""
常量定义模块 - 存放系统和接口中使用的各种常量值
"""
from enum import Enum, auto

class KnowledgeDomain(str, Enum):
    """知识领域枚举类型"""
    DEFAULT = 'default'
    API_DOCUMENTATION = 'api_documentation'  # API文档
    BEST_PRACTICES = 'best_practices'  # 最佳实践
    HARMONY_EXPERIENCE_2012 = 'harmony_exp_2012'  # 鸿蒙突击队经验复盘-2012网站
    HARMONY_EXPERIENCE_HDN = 'harmony_exp_HDN'  # 鸿蒙突击队经验-黄大年
    GAMES_TEAM_WIKI = 'game_team_wiki'  # 游戏分队经验-wiki

    def __str__(self) -> str:
        return self.value

class GroupId(Enum):
    """组ID枚举类型"""
    ADMIN = 99
    COMMON = 1
    GAME_TEAM = 2
    BLUE_TEST = 3

    def __str__(self) -> str:
        return str(self.value)


GROUP_DOMAINS = {
    GroupId.ADMIN: [
        KnowledgeDomain.API_DOCUMENTATION, 
        KnowledgeDomain.BEST_PRACTICES, 
        KnowledgeDomain.HARMONY_EXPERIENCE_2012, 
        KnowledgeDomain.HARMONY_EXPERIENCE_HDN, 
        KnowledgeDomain.GAMES_TEAM_WIKI
    ],
    GroupId.COMMON: [
        KnowledgeDomain.API_DOCUMENTATION, 
        KnowledgeDomain.HARMONY_EXPERIENCE_2012
    ],
    GroupId.GAME_TEAM: [
        KnowledgeDomain.API_DOCUMENTATION, 
        KnowledgeDomain.HARMONY_EXPERIENCE_2012, 
        KnowledgeDomain.GAMES_TEAM_WIKI
    ],
    GroupId.BLUE_TEST: [
        KnowledgeDomain.API_DOCUMENTATION, 
        KnowledgeDomain.BEST_PRACTICES, 
        KnowledgeDomain.HARMONY_EXPERIENCE_HDN
    ]
}
