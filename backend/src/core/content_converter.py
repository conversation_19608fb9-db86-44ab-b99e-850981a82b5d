# -*- coding: utf-8 -*-
from markdownify import markdownify
from bs4 import BeautifulSoup
from .logging import get_module_logger
import re
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Type
import html

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class FormatDetector:
    """检测内容格式的工具类"""
    
    @staticmethod
    def is_html(content: str) -> bool:
        """
        检测内容是否为HTML格式
        
        Args:
            content: 要检测的内容
            
        Returns:
            bool: 如果内容似乎是HTML格式则返回True
        """
        # 1. 检查是否包含HTML标签
        html_patterns = [
            r'<\s*[a-zA-Z]+[^>]*>',  # HTML开始标签
            r'<\s*/\s*[a-zA-Z]+\s*>', # HTML结束标签
            r'<\s*[a-zA-Z]+[^>]*/>',  # 自闭合标签
            r'&[a-zA-Z]+;',           # HTML实体
            r'&#\d+;'                 # 数字HTML实体
        ]
        
        # 如果含有足够多的HTML模式，则认为是HTML
        html_pattern_count = sum(1 for pattern in html_patterns if re.search(pattern, content))
        if html_pattern_count >= 2:
            return True
            
        # 2. 尝试用BeautifulSoup解析，看是否能找到HTML结构
        try:
            soup = BeautifulSoup(content, 'html.parser')
            # 如果有实际的HTML结构（不只是纯文本），则认为是HTML
            if len(soup.find_all(['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'table'])) > 0:
                return True
        except:
            pass
            
        return False
    
    @staticmethod
    def is_markdown(content: str) -> bool:
        """
        检测内容是否为Markdown格式
        
        Args:
            content: 要检测的内容
            
        Returns:
            bool: 如果内容似乎是Markdown格式则返回True
        """
        # Markdown特征模式
        md_patterns = [
            r'^#{1,6}\s+.+$',                  # 标题
            r'^\s*[-*+]\s+.+$',                # 无序列表
            r'^\s*\d+\.\s+.+$',                # 有序列表
            r'^\s*>\s+.+$',                    # 引用
            r'^```.*$',                        # 代码块
            r'`[^`]+`',                        # 行内代码
            r'\[.+?\]\(.+?\)',                 # 链接
            r'!\[.+?\]\(.+?\)',                # 图片
            r'^[-*_]{3,}$',                    # 分隔线
            r'\*\*[^*]+\*\*',                  # 粗体
            r'_[^_]+_'                         # 斜体
        ]
        
        # 如果包含足够多的Markdown模式，则认为是Markdown
        lines = content.strip().split('\n')
        md_pattern_count = sum(1 for line in lines for pattern in md_patterns if re.search(pattern, line, re.MULTILINE))
        
        # 如果内容似乎是HTML但同时也有Markdown特征，优先判断为HTML
        if FormatDetector.is_html(content) and md_pattern_count < 3:
            return False
            
        return md_pattern_count >= 2

class BaseConverter(ABC):
    """转换器基类，定义所有格式转换器必须实现的接口"""
    
    @property
    @abstractmethod
    def source_format(self) -> str:
        """返回源格式名称"""
        pass
    
    @property
    @abstractmethod
    def target_format(self) -> str:
        """返回目标格式名称"""
        pass
    
    @abstractmethod
    def convert(self, content: Any, **options) -> Any:
        """执行转换操作"""
        pass

class HtmlToMarkdownConverter(BaseConverter):
    """HTML转Markdown的转换器实现"""
    
    @property
    def source_format(self) -> str:
        return "html"
    
    @property
    def target_format(self) -> str:
        return "markdown"
    
    def convert(self, content: str, **options) -> str:
        """
        将HTML内容转换为Markdown格式
        
        Args:
            content (str): HTML格式的内容
            **options: 额外的转换选项
                - heading_style: 标题样式，默认为"ATX"(#格式)
                - strip_tags: 需要移除的标签列表，默认为["script", "style"]
                - escape_asterisks: 是否转义星号，默认为True
                - escape_underscores: 是否转义下划线，默认为True
                - toc: 目录结构数据，用于设置正确的标题层级
                - doc_title: 文档标题，用于识别主标题
                
        Returns:
            str: 转换后的Markdown内容
        """
        # 安全检查：确保输入确实是HTML
        if not FormatDetector.is_html(content):
            logger.warning('输入内容不是HTML格式，但尝试进行HTML转Markdown转换')
            
        try:
            # 清理和预处理HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 清理脚本和样式
            for element in soup.find_all(['script', 'style']):
                element.extract()
            
            # 获取文档标题
            doc_title = options.pop('doc_title', None)
            
            # 根据TOC处理标题层级（如果提供）
            toc = options.pop('toc', None)
            if toc:
                self._preprocess_headings_with_toc(soup, toc, doc_title)
            else:
                # 使用关键词进行备用处理
                self._preprocess_headings(soup)
                
            # 设置默认选项
            default_options = {
                'heading_style': "ATX",  # 使用 # 格式的标题
                'strip': ["script", "style"],  # 移除script和style标签
                'convert_links': True,  # 转换链接
                'autolinks': True,  # 自动处理URL
                'escape_asterisks': True,  # 转义星号
                'escape_underscores': True  # 转义下划线
            }
            
            # 更新选项
            default_options.update(options)
            
            # 预处理代码块，保留语言信息
            self._preprocess_code_blocks(soup)
            
            # 直接使用markdownify函数
            md_content = markdownify(str(soup), **default_options)
            
            # 后处理
            md_content = self._post_process_markdown(md_content)
            
            return md_content
            
        except Exception as e:
            logger.error(f"HTML转Markdown失败: {str(e)}")
            # 尝试使用BeautifulSoup简单处理
            try:
                # 重新导入BeautifulSoup，避免访问外部变量
                from bs4 import BeautifulSoup as BS
                soup = BS(content, 'html.parser')
                # 移除script和style标签
                for script in soup(["script", "style"]):
                    script.extract()
                    
                # 获取文本
                text = soup.get_text(separator='\n\n')
                return text
            except Exception as e:
                logger.error(f"BeautifulSoup处理失败: {str(e)}")
                # 返回原始内容作为备选方案
                return content
    
    def _preprocess_headings_with_toc(self, soup, toc, doc_title=None):
        """
        根据TOC数据处理HTML中的标题层级
        
        Args:
            soup: BeautifulSoup对象
            toc: 目录结构数据列表
            doc_title: 文档标题，用于识别主标题
        """
        # 创建标题ID到层级的映射
        heading_levels = {}
        
        # 用于跟踪是否已经处理了主标题
        main_title_processed = False
        
        for item in toc:
            anchor_id = item.get('anchorId')
            level = item.get('level')
            if anchor_id and level:
                try:
                    level_num = int(level)
                    # 调整映射逻辑：
                    # 如果是level=0，且没有处理过主标题，则映射为h1
                    # 否则，映射为level+1，确保除了主标题外都降级
                    if level_num == 0 and not main_title_processed:
                        heading_levels[anchor_id] = 1  # h1
                        main_title_processed = True
                    else:
                        heading_levels[anchor_id] = level_num + 1  # level 1 -> h2, level 2 -> h3
                except ValueError:
                    logger.warning(f'无法将TOC项的层级转换为数字: {level}')
        
        # 存储已处理的标题
        processed_headings = set()
        
        # 首先处理常见的div.section > h4模式
        for section_div in soup.find_all('div', class_='section'):
            if section_div.has_attr('id'):
                section_id = section_div['id']
                if section_id in heading_levels:
                    # 查找section内部的标题
                    heading = section_div.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    if heading and heading not in processed_headings:
                        level_num = heading_levels[section_id]
                        # 将标题转换为对应的层级
                        heading.name = f'h{level_num}'
                        # 处理特殊前缀
                        text = heading.get_text()
                        if '[h2]' in text:
                            heading.string = text.replace('[h2]', '')
                        processed_headings.add(heading)
        
        # 处理剩余的HTML中的标题
        first_heading = True
        for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            if heading in processed_headings:
                continue
            
            # 如果是第一个标题且与文档标题匹配，保留为h1
            if first_heading and doc_title and doc_title in heading.get_text():
                heading.name = 'h1'
                first_heading = False
                processed_headings.add(heading)
                continue
                
            # 查找标题的名称（name）属性或ID
            anchor_id = None
            
            # 检查是否有name属性
            if heading.has_attr('name'):
                anchor_id = heading['name']
            
            # 检查是否有id属性
            if not anchor_id and heading.has_attr('id'):
                anchor_id = heading['id']
            
            # 查找周围的锚点
            if not anchor_id:
                # 查找紧邻标题的<a>标签作为锚点
                prev_a = heading.find_previous_sibling('a')
                if prev_a and prev_a.has_attr('name'):
                    anchor_id = prev_a['name']
                
                # 还可以检查标题内部的锚点
                if not anchor_id:
                    a_tag = heading.find('a')
                    if a_tag and a_tag.has_attr('name'):
                        anchor_id = a_tag['name']
                
                # 检查父元素的id
                if not anchor_id:
                    parent = heading.parent
                    while parent and parent.name != 'body':
                        if parent.has_attr('id'):
                            anchor_id = parent['id']
                            break
                        parent = parent.parent
            
            # 如果找到了匹配的anchor_id并且在映射中存在
            if anchor_id and anchor_id in heading_levels:
                level_num = heading_levels[anchor_id]
                # 将标题转换为对应的层级
                heading.name = f'h{level_num}'
                processed_headings.add(heading)
            else:
                # 清理标题文本
                text = heading.get_text()
                
                # 处理特殊前缀
                if '[h2]' in text:
                    heading.string = text.replace('[h2]', '')
                    heading.name = 'h3'  # 原来是h2，现在降级为h3
                    processed_headings.add(heading)
                # 如果不是特殊前缀且仍是h4，则降级为h2或h3
                elif heading.name == 'h4':
                    # 检查是否可能是主要章节标题（概述、场景说明等）
                    if any(keyword in text for keyword in ['概述', '场景说明', '场景分析', '场景实现', '示例代码']):
                        heading.name = 'h2'
                    else:
                        heading.name = 'h3'
                    processed_headings.add(heading)
        
        # 如果有未处理的标题，使用标题文本内容与TOC标题匹配
        if toc and len(processed_headings) < len([h for h in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])]):
            # 创建标题文本到层级的映射
            title_to_level = {}
            for item in toc:
                if item.get('title') and item.get('level'):
                    try:
                        # 调整层级映射
                        level_num = int(item.get('level'))
                        if level_num == 0:
                            title_to_level[item.get('title')] = 1  # h1
                        else:
                            title_to_level[item.get('title')] = level_num + 2  # level 1 -> h3, etc.
                    except ValueError:
                        continue
            
            for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                if heading in processed_headings:
                    continue
                
                # 获取标题文本
                text = heading.get_text().strip()
                
                # 尝试匹配TOC中的标题
                if text in title_to_level:
                    level_num = title_to_level[text]
                    heading.name = f'h{level_num}'
                    processed_headings.add(heading)
    
    def _preprocess_headings(self, soup):
        """处理HTML中的标题层级（备用方法，当没有TOC数据时使用）"""
        # 一级标题关键词列表
        h1_keywords = ['概述', '场景说明', '场景分析', '场景实现', '示例代码']
        
        for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            text = heading.get_text()
            
            # 情况1: 包含[h2]前缀的标题转为二级标题
            if '[h2]' in text:
                # 移除前缀标记
                heading.string = text.replace('[h2]', '')
                # 将标签改为h2
                heading.name = 'h2'
                continue
                
            # 情况2: 匹配一级标题关键词列表
            for keyword in h1_keywords:
                if keyword in text:
                    heading.name = 'h1'
                    break
            else:
                # 默认情况: 如果没有特殊标记且不在一级标题列表中，认为是二级标题
                if heading.name == 'h4' and not any(kw in text for kw in h1_keywords):
                    heading.name = 'h2'
    
    def _preprocess_code_blocks(self, soup):
        """预处理代码块，确保保留语言信息"""
        # 查找所有代码块
        for pre in soup.find_all('pre'):
            code = pre.find('code')
            if code:
                # 尝试从class属性中提取语言信息
                lang = ""
                if code.has_attr('class'):
                    for cls in code['class']:
                        if cls.startswith('language-') or cls.startswith('lang-'):
                            lang = cls.split('-', 1)[1]
                            break
                
                # 如果没有找到语言信息，检查pre标签
                if not lang and pre.has_attr('class'):
                    for cls in pre['class']:
                        if cls.startswith('language-') or cls.startswith('lang-'):
                            lang = cls.split('-', 1)[1]
                            break
                
                # 在代码内容前添加一个特殊标记，稍后在后处理中识别
                if lang:
                    code_wrapper = soup.new_tag('div')
                    code_wrapper['class'] = 'code-language-wrapper'
                    code_wrapper['data-language'] = lang
                    
                    # 包装代码标签
                    code.wrap(code_wrapper)
                
    def _post_process_markdown(self, md_content):
        """
        对生成的Markdown进行后处理
        
        Args:
            md_content (str): 转换后的Markdown内容
            
        Returns:
            str: 后处理后的Markdown内容
        """
        # 修复可能的多余空行
        lines = md_content.split('\n')
        processed_lines = []
        
        # 标记是否在代码块中
        in_code_block = False
        consecutive_empty_lines = 0
        
        # 处理代码语言标记
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 处理代码块开始
            if line.strip().startswith('```') and not in_code_block:
                in_code_block = True
                
                # 检查前一行和当前行是否包含代码语言信息包装器
                lang = ""
                
                # 方法1: 从前一行中检查code-language-wrapper
                if i > 0 and '<div class="code-language-wrapper"' in lines[i-1]:
                    match = re.search(r'data-language="([^"]+)"', lines[i-1])
                    if match:
                        lang = match.group(1)
                    # 跳过这一行，不添加到处理后的列表中
                    if len(processed_lines) > 0:
                        processed_lines.pop()
                
                # 方法2: 检查现有代码块起始行是否已包含语言信息
                code_start_match = re.match(r'^```(\w*)$', line.strip())
                if code_start_match and code_start_match.group(1):
                    # 代码块已经有语言信息，优先使用
                    lang = code_start_match.group(1)
                
                # 添加带有语言信息的代码块开始标记
                processed_lines.append(f"```{lang}")
                consecutive_empty_lines = 0
                i += 1
                continue
            
            # 处理代码块结束
            elif line.strip().startswith('```') and in_code_block:
                in_code_block = False
                processed_lines.append(line)
                consecutive_empty_lines = 0
                i += 1
                continue
            
            # 在代码块中的内容完全保留原样
            if in_code_block:
                processed_lines.append(line)
                i += 1
                continue
            
            # 恢复表格中的图片
            if '<span class="table-image-placeholder"' in line:
                # 提取图片属性
                src_match = re.search(r'data-src="([^"]+)"', line)
                alt_match = re.search(r'data-alt="([^"]+)"', line)
                title_match = re.search(r'data-title="([^"]+)"', line)
                
                if src_match:
                    src = src_match.group(1)
                    alt = alt_match.group(1) if alt_match else ""
                    title = title_match.group(1) if title_match else ""
                    
                    # 修复：创建Markdown格式的图片时正确处理URL和标题中的括号
                    # 检查URL和标题中是否包含括号，如果有需要转义
                    src = src.replace("(", "%28").replace(")", "%29")
                    
                    # 创建Markdown格式的图片
                    img_markdown = f"![{alt}]({src}"
                    if title:
                        # 转义标题中的引号，避免标题中的引号与 Markdown 语法冲突
                        title = title.replace('"', '\\"')
                        img_markdown += f' "{title}"'
                    img_markdown += ")"
                    
                    # 替换占位符为Markdown图片
                    line = re.sub(r'<span class="table-image-placeholder"[^>]+></span>', img_markdown, line)
            
            # 处理空行
            if not line.strip():
                consecutive_empty_lines += 1
                if consecutive_empty_lines <= 2:  # 最多允许2个连续空行
                    processed_lines.append(line)
            else:
                consecutive_empty_lines = 0
                processed_lines.append(line)
            
            i += 1
                
        return '\n'.join(processed_lines)

class DocConverter:
    """Doc转换器，支持智能检测内容类型"""
    
    def __init__(self):
        # 注册转换器
        self.html2md = HtmlToMarkdownConverter()
    
    def doc_content_to_markdown(self, doc_json: Dict[str, Any], **options) -> str:
        """
        转换JSON文档，自动检测内容类型
        
        Args:
            doc_json: 包含内容的JSON对象
            content_format: 强制指定内容格式("html"或"markdown")
            **options: 额外的转换选项
                
        Returns:
            str: 处理后的Markdown内容
        """
        if 'content' not in doc_json:
            raise ValueError('文档JSON必须包含"content"字段')
            
        # 提取内容和元数据
        content = doc_json['content']
        toc = doc_json.get('toc')
        doc_title = doc_json.get('title')

        content_format = doc_json.get('content_format', None)
        if content_format is None:
            content_format = 'html' if FormatDetector.is_html(content) else 'markdown'

        # 使用共享方法进行格式检测和转换
        return self.to_markdown(
            content=content, 
            content_format=content_format, 
            toc=toc, 
            doc_title=doc_title, 
            **options
        )
    
    def to_markdown(self, content: str, content_format: str = None, toc: List[Dict] = None, 
                           doc_title: str = None, **options) -> str:
        """
        检测内容格式并转换为Markdown
        
        Args:
            content: 要转换的内容
            content_format: 强制指定内容格式("html"或"markdown")
            toc: 目录结构数据，用于设置正确的标题层级
            doc_title: 文档标题，用于识别主标题
            **options: 其他转换选项
                
        Returns:
            str: 转换后的Markdown内容
        """
        
        # 根据内容类型选择适当的处理方式
        if content_format == 'html':
            logger.info('HTML内容，使用HTML转Markdown转换器')
            # 处理TOC和文档标题
            convert_options = dict(options)
            if toc:
                convert_options['toc'] = toc
            if doc_title:
                convert_options['doc_title'] = doc_title
                
            return self.html2md.convert(content, **convert_options)
        if content_format == 'markdown':
            logger.info('Markdown内容，直接返回原始内容')
            return content 
        else:
            logger.error(f'不支持的内容格式: {content_format}')
            return content
        
