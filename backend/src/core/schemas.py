# -*- coding: utf-8 -*-
from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel
from datetime import datetime, UTC

T = TypeVar('T')

class ResponseBase(BaseModel, Generic[T]):
    code: int = 200
    message: str = 'success'
    data: Optional[T] = None
    timestamp: str = str(datetime.now(UTC))

class ErrorResponse(BaseModel):
    code: int
    message: str
    details: Optional[Any] = None
    timestamp: str = str(datetime.now(UTC)) 