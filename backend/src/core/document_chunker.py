# -*- coding: utf-8 -*-
import json
import re
import uuid
from typing import List, Dict, Optional, Tuple, Set, Any
from dataclasses import dataclass, field
from .constants import KnowledgeDomain
from .logging import get_module_logger

logger = get_module_logger(__name__)


class Block:
    """表示一个Markdown块的类"""
    content: str
    headers: List[str]
    level: int
    block_type: str
    parent: Optional['Block'] = None
    children: List['Block'] = field(default_factory=list)
    start_line: int = 0
    end_line: int = 0
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    prev_block: Optional['Block'] = None
    next_block: Optional['Block'] = None
    codes: Dict[str, str] = field(default_factory=dict)  # 保存代码块字典，key为索引，value为代码内容
    
    def __init__(self, content: str='', headers: List[str]=[], level: int=0, block_type: str='', start_line: int = 0, end_line: int = 0):
        self.content = content
        self.headers = headers
        self.level = level
        self.block_type = block_type
        self.start_line = start_line
        self.end_line = end_line
        self.parent = None
        self.children = []
        self.codes = {}
        self.id = str(uuid.uuid4())
        self.prev_block = None
        self.next_block = None

    def size(self) -> int:
        """返回块的字符数量"""
        return len(self.content)
    
    def total_size(self) -> int:
        """返回块及其所有后代节点的总字符数量"""
        total = self.size()
        for child in self.children:
            total += child.size()
        return total
    
    def add_child(self, child: 'Block') -> None:
        """添加子块"""
        self.children.append(child)
        child.parent = self
    
    def add_code(self, index: str, code: str) -> None:
        """添加代码块"""
        self.codes[index] = code
    
    def __dict__(self) -> Dict[str, Any]:
        return self.to_dict()
    
    def to_dict(self) -> Dict[str, Any]:
        """将块转换为字典表示"""
        
        return {
                'content': self.content, # 原始content不包含代码内容
                'size_count': self.size(),
                'metadata': {
                    'block_id': self.id,
                    'chunk_type': self.block_type,
                    'headers': self.headers,
                    'level': self.level,
                    'prev_block_id': self.prev_block.id if self.prev_block else None,
                    'next_block_id': self.next_block.id if self.next_block else None,
                    'code': self.codes  # 代码字典，key为索引，value为代码内容
                }
            }
    
def save_to_json(blocks: List[Block], filename: str) -> None:
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump([b.to_dict() for b in blocks], f, ensure_ascii=False, indent=2)


# =============================== PIPELINE COMPONENTS ========================

class BlockExtractor:
    """Stage ① 线性扫描 Markdown, 生成扁平 Block 列表"""

    def __init__(
        self,
        chunk_size: int,
        overlap_size: int,
        max_heading_level: int,
    ) -> None:
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.max_heading_level = max_heading_level
        # 分块后还需要用到 split_text、split_sentences, 直接复用私有方法

    # ---------------------------------------------------------------------
    # Public API
    def extract(self, text: str) -> List[Block]:
        return self._extract_blocks(text)

    def _extract_blocks(self, text: str) -> List[Block]:
        lines = text.splitlines(True)  # 保留换行符
        blocks: List[Block] = []
        i = 0
        current_level = 0
        current_headers: List[str] = []
        current_text_content: List[str] = []
        current_text_start = 0
        # 改为全局代码映射，key为索引，value为代码内容
        all_codes: Dict[int, str] = {}
        global_code_index = 0

        while i < len(lines):
            line = lines[i]
            
            # ---------- 1. heading ------------------------------------------------
            header_match = re.match(r'^(#{1,6})\s+(.*?)(?:\s+#+)?$', line.strip())
            if header_match:
                level = len(header_match.group(1))
                header_text = header_match.group(2).strip()

                # 若在处理层级以内
                if level <= self.max_heading_level:
                    if current_text_content:
                        self._flush_text_block(
                            blocks,
                            current_text_content,
                            current_headers,
                            current_level,
                            current_text_start,
                            i - 1,
                            all_codes,
                        )

                        current_text_content = []

                    # 更新标题栈
                    while current_headers and current_level >= level:
                        current_headers.pop()
                        current_level = len(current_headers)

                    current_headers.append(header_text)
                    current_level = level

                    # 标题行也算文本内容一部分
                    current_text_content.append(line)
                    current_text_start = i

                    # 插入一个 heading block
                    blocks.append(
                        Block(
                            content=line,
                            headers=current_headers.copy(),
                            level=level,
                            block_type='heading',
                            start_line=i,
                            end_line=i,
                        )
                    )

                else:  # 超出层级当普通文本
                    if not current_text_content:
                        current_text_start = i
                    current_text_content.append(line)

                i += 1
                continue

            # ---------- 2. fenced code -----------------------------------------
            if line.strip().startswith('```'):
                code_content = [line]
                i += 1
                while i < len(lines) and not lines[i].strip().startswith('```'):
                    code_content.append(lines[i])
                    i += 1
                if i < len(lines):
                    code_content.append(lines[i])
                    i += 1

                if len(code_content) > 3:
                    placeholder = f'__CODE_{global_code_index}__\n'
                    all_codes[global_code_index] = ''.join(code_content)
                    global_code_index += 1
                    current_text_content.append(placeholder)
                else:
                    current_text_content.extend(code_content)
                continue

            # ---------- 3. table ----------------------------------------------
            if '|' in line and (
                i + 1 < len(lines)
                and re.match(r'^\s*\|([\s\-\:\|]+)\|\s*$', lines[i + 1].strip())
            ):
                table_start = i
                table_content = [line]
                i += 1
                table_content.append(lines[i])  # 分隔行
                i += 1
                while i < len(lines) and '|' in lines[i] and not lines[i].strip().startswith('#'):
                    table_content.append(lines[i])
                    i += 1
                # blocks.append(
                #     Block(
                #         content="".join(table_content),
                #         headers=current_headers.copy(),
                #         level=current_level,
                #         block_type="table",
                #         start_line=table_start,
                #         end_line=i - 1,
                #     )
                # )
                current_text_content.extend(table_content)
                continue

            # ---------- 4. plain text -----------------------------------------
            if line.strip():
                if not current_text_content:
                    current_text_start = i
                current_text_content.append(line)
                i += 1
                continue

            # ---------- 5. empty line -----------------------------------------
            # 多个空行压缩为一行
            if not line.strip():
                while i + 1 < len(lines) and not lines[i + 1].strip():
                    i += 1
                current_text_content.append(line)
                i += 1
                continue

        # 处理文件尾部剩余内容
        if current_text_content:
            self._flush_text_block(
                blocks,
                current_text_content,
                current_headers,
                current_level,
                current_text_start,
                len(lines) - 1,
                all_codes,
            )

        # sort by start_line 保持原顺序
        blocks.sort(key=lambda b: b.start_line)
        return blocks

    # ------------------------------------------------------------------ util

    def _flush_text_block(
        self,
        blocks: List[Block],
        text_content: List[str],
        headers: List[str],
        level: int,
        start_line: int,
        end_line: int,
        all_codes: Dict[int, str],
    ) -> None:
        text = ''.join(text_content)
        # 长文本需要分片
        if len(text) > self.chunk_size:
            chunks = self._split_text(text, self.chunk_size, self.overlap_size)
            for idx, chunk in enumerate(chunks):
                blk = Block(
                    content=chunk,
                    headers=headers.copy(),
                    level=level,
                    block_type='text',
                    start_line=start_line,
                    end_line=end_line,
                )
                # 查找chunk中的所有代码占位符，并关联对应的代码
                import re
                placeholders = re.findall(r'__CODE_(\d+)__', chunk)
                for placeholder_idx in placeholders:
                    code_index = int(placeholder_idx)
                    if code_index in all_codes:
                        blk.add_code(placeholder_idx, all_codes[code_index])
                        logger.debug(f'成功关联代码: __CODE_{placeholder_idx}__, 代码内容长度: {len(all_codes[code_index])}')
                    else:
                        logger.warning(f'未找到代码内容: __CODE_{placeholder_idx}__')
                blocks.append(blk)
        else:
            blk = Block(
                content=text,
                headers=headers.copy(),
                level=level,
                block_type='text',
                start_line=start_line,
                end_line=end_line,
            )
            # 查找text中的所有代码占位符，并关联对应的代码
            import re
            placeholders = re.findall(r'__CODE_(\d+)__', text)
            for placeholder_idx in placeholders:
                code_index = int(placeholder_idx)
                if code_index in all_codes:
                    blk.add_code(placeholder_idx, all_codes[code_index])
                    logger.debug(f'成功关联代码: __CODE_{placeholder_idx}__, 代码内容长度: {len(all_codes[code_index])}')
                else:
                    logger.warning(f'未找到代码内容: __CODE_{placeholder_idx}__')
            blocks.append(blk)

    # ----------------------- split helpers (保持原逻辑) --------------------

    @staticmethod
    def _split_sentences(text: str) -> List[str]:
        pattern = r'([。！？])'
        segments = re.split(pattern, text)
        sentences = []
        current = ''
        for i, seg in enumerate(segments):
            current += seg
            if seg in ['。', '！', '？'] and i < len(segments) - 1:
                sentences.append(current)
                current = ''
        if current:
            sentences.append(current)
        return sentences

    def _get_last_sentences(self, text: str, target: int) -> List[str]:
        sentences = self._split_sentences(text)
        result: List[str] = []
        size = 0
        for sent in reversed(sentences):
            if size + len(sent) <= target:
                result.insert(0, sent)
                size += len(sent)
            else:
                break
        return result

    def _split_text(self, text: str, max_size: int, overlap: int = 0) -> List[str]:
        chunks: List[str] = []
        paragraphs = re.split(r'\n\s*\n', text)
        current = ''
        last_overlap: List[str] = []
        for para in paragraphs:
            if not para.strip():
                continue
            if len(current) + len(para) + 2 <= max_size:
                current = current + ('\n\n' if current else '') + para
            elif len(para) > max_size:
                if current:
                    chunks.append(current)
                    if overlap:
                        last_overlap = self._get_last_sentences(current, overlap)
                    current = ''.join(last_overlap) if last_overlap else ''
                sentences = self._split_sentences(para)
                for sent in sentences:
                    if len(current) + len(sent) <= max_size:
                        current += sent
                    else:
                        if current:
                            chunks.append(current)
                            if overlap:
                                last_overlap = self._get_last_sentences(current, overlap)
                            current = ''.join(last_overlap) if last_overlap else ''
                        if len(sent) > max_size:
                            start = 0
                            while start < len(sent):
                                chunks.append(sent[start : start + max_size])
                                start += max_size
                        else:
                            current += sent
            else:
                if current:
                    chunks.append(current)
                    if overlap:
                        last_overlap = self._get_last_sentences(current, overlap)
                current = (''.join(last_overlap) if last_overlap else '') + para
        if current:
            chunks.append(current)
        return chunks


# ---------------------------------------------------------------------------

class BlockTreeBuilder:
    """Stage ② 将扁平 blocks 组织成树"""

    def __init__(self, root_block: Block):
        self.root = root_block

    def organize(self, blocks: List[Block]) -> Block:
        self.root.children = []
        stack = [self.root]
        for blk in blocks:
            while len(stack) > 1 and stack[-1].level >= blk.level:
                stack.pop()
            stack[-1].add_child(blk)
            if blk.block_type == 'heading':
                stack.append(blk)
        return self.root


# ---------------------------------------------------------------------------

class BlockMerger:
    """Stage ③ 递归合并"""

    def __init__(self, chunk_size: int, domain: Optional[str] = None):
        self.chunk_size = chunk_size
        self.domain = domain

    # 外部 API
    def merge(self, root: Block) -> List[Block]:
        merged: List[Block] = []
        self._recursive_merge(root, merged)
        return merged

    # ---------------------------------------------------------------------
    def _recursive_merge(self, block: Block, out: List[Block]):
        if not block.children:
            if block.block_type not in ('root', 'heading'):
                out.append(block)
            return

        # 把 heading 自身内容贴到首文本块
        if block.block_type == 'heading':
            text_children = [c for c in block.children if c.block_type == 'text']
            if text_children:
                first_text = text_children[0]
                first_text.content = block.content + first_text.content
                first_text.start_line = min(block.start_line, first_text.start_line)
            else:
                temp = Block(
                    content=block.content,
                    headers=block.headers,
                    level=block.level,
                    block_type='text',
                    start_line=block.start_line,
                    end_line=block.end_line,
                )
                block.children.insert(0, temp)

        # 如果是API文档，就不合并除了heading以外的块，全部append
        if self.domain == KnowledgeDomain.API_DOCUMENTATION:
            for ch in block.children:
                if ch.block_type != 'heading':
                    out.append(ch)
            return

        # 规则 1：整子树总大小 < chunk_size 全并
        if block.block_type != 'root' and block.total_size() <= self.chunk_size:
            merged_content = block.content
            merged_codes = block.codes.copy()  # 复制当前块的代码

            def collect(node: Block):
                nonlocal merged_content, merged_codes
                for ch in node.children:
                    if ch.block_type == 'heading':
                        collect(ch)
                    else:
                        merged_content += ch.content
                        # 合并代码字典
                        merged_codes.update(ch.codes)

            collect(block)
            if merged_content.strip():
                merged_block = Block(
                    content=merged_content,
                    headers=block.headers,
                    level=block.level,
                    block_type='text',
                    start_line=block.start_line,
                    end_line=max((c.end_line for c in block.children), default=block.end_line),
                )
                # 设置合并后的代码字典
                merged_block.codes = merged_codes
                out.append(merged_block)
            return

        # 处理当前层文本子块 & 递归 heading 子块
        non_heading: List[Block] = []
        for ch in block.children:
            if ch.block_type == 'heading':
                self._recursive_merge(ch, out)
            else:
                non_heading.append(ch)

        i = 0
        while i < len(non_heading):
            cur = non_heading[i]
            if cur.block_type != 'text':
                out.append(cur)
                i += 1
                continue
            merged_content = cur.content
            merged_codes = cur.codes.copy()  # 复制当前块的代码
            merged_size = cur.size()
            end_idx = i
            for j in range(i + 1, len(non_heading)):
                nxt = non_heading[j]
                if nxt.block_type == 'text' and nxt.level == cur.level and merged_size + nxt.size() <= self.chunk_size:
                    merged_content += nxt.content
                    merged_codes.update(nxt.codes)  # 合并代码字典
                    merged_size += nxt.size()
                    end_idx = j
                else:
                    break
            if end_idx > i:
                merged_block = Block(
                    content=merged_content,
                    headers=cur.headers,
                    level=cur.level,
                    block_type='text',
                    start_line=cur.start_line,
                    end_line=non_heading[end_idx].end_line,
                )
                # 设置合并后的代码字典
                merged_block.codes = merged_codes
                out.append(merged_block)
                i = end_idx + 1
            else:
                out.append(cur)
                i += 1


# ---------------------------------------------------------------------------

class BlockDeduplicator:
    """Stage ④ 删除相邻重复块"""

    @staticmethod
    def deduplicate(blocks: List[Block]) -> List[Block]:
        result: List[Block] = []
        for idx, blk in enumerate(blocks):
            if blk.block_type == 'text':
                result.append(blk)
                continue
            if idx > 0 and blk.content == blocks[idx - 1].content:
                continue
            if idx < len(blocks) - 1 and blk.content == blocks[idx + 1].content:
                continue
            result.append(blk)
        return result


# ---------------------------------------------------------------------------

class BlockFilter:
    """Stage ⑤ 过滤 min_chunk_size, 设置 prev / next"""

    def __init__(self, min_chunk_size: int):
        self.min_chunk_size = min_chunk_size

    def filter(self, blocks: List[Block]) -> List[Block]:
        filtered = [b for b in blocks if b.size() >= self.min_chunk_size and b.block_type != 'heading']
        for i, blk in enumerate(filtered):
            # prev
            p = i - 1
            while p >= 0:
                if filtered[p].block_type == 'text':
                    blk.prev_block = filtered[p]
                    break
                p -= 1
            # next
            n = i + 1
            while n < len(filtered):
                if filtered[n].block_type == 'text':
                    blk.next_block = filtered[n]
                    break
                n += 1
        return filtered


# =============================== PUBLIC FACADE ==============================

class MarkdownChunker:
    """外部接口保持不变，但内部五阶段管线化"""

    def __init__(
        self,
        chunk_size: int = 1000,
        min_chunk_size: int = 100,
        overlap_size: int = 0,
        domain: Optional[str] = None,
    ) -> None:
        self.chunk_size = chunk_size
        self.min_chunk_size = min_chunk_size
        self.overlap_size = overlap_size
        self.domain = domain
        self.max_heading_level = 6 if domain != KnowledgeDomain.API_DOCUMENTATION else 2

        # 根节点 (虚)
        self.root_block = Block( )

        # 五段式流水线组件
        self.extractor = BlockExtractor(chunk_size, overlap_size, self.max_heading_level)
        self.tree_builder = BlockTreeBuilder(self.root_block)
        self.merger = BlockMerger(chunk_size, domain)
        self.deduplicator = BlockDeduplicator()
        self.filter = BlockFilter(min_chunk_size)

    # ---------------------------------------------------------------------
    def chunk_markdown(self, text: str) -> List[Dict[str, Any]]:
        raw_blocks = self.extractor.extract(text)
        tree = self.tree_builder.organize(raw_blocks)
        merged = self.merger.merge(tree)
        deduped = self.deduplicator.deduplicate(merged)
        filtered = self.filter.filter(deduped)
        return [b.to_dict() for b in filtered]

    def validate_chunks_integrity(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证分块结果的完整性，特别是代码占位符和metadata中code字段的匹配性
        
        Args:
            chunks: 分块结果列表
            
        Returns:
            验证结果统计
        """
        validation_stats = {
            'total_chunks': len(chunks),
            'chunks_with_placeholders': 0,
            'chunks_with_code_metadata': 0,
            'mismatched_chunks': [],
            'orphaned_placeholders': [],  # 有占位符但没有对应代码的情况
            'orphaned_codes': []  # 有代码但没有对应占位符的情况
        }
        
        for i, chunk in enumerate(chunks):
            content = chunk.get('content', '')
            metadata = chunk.get('metadata', {})
            codes = metadata.get('code', {})
            
            # 查找内容中的代码占位符
            import re
            placeholders = re.findall(r'__CODE_(\d+)__', content)
            placeholder_set = set(placeholders)
            
            # 检查metadata中的代码索引
            code_indices = set(codes.keys()) if isinstance(codes, dict) else set()
            
            # 统计
            if placeholders:
                validation_stats['chunks_with_placeholders'] += 1
            if codes:
                validation_stats['chunks_with_code_metadata'] += 1
            
            # 检查不匹配情况
            orphaned_placeholders = placeholder_set - code_indices
            orphaned_codes = code_indices - placeholder_set
            
            if orphaned_placeholders or orphaned_codes:
                mismatch_info = {
                    'chunk_index': i,
                    'chunk_id': metadata.get('block_id', 'unknown'),
                    'orphaned_placeholders': list(orphaned_placeholders),
                    'orphaned_codes': list(orphaned_codes),
                    'content_preview': content[:100] + '...' if len(content) > 100 else content
                }
                validation_stats['mismatched_chunks'].append(mismatch_info)
                
                if orphaned_placeholders:
                    validation_stats['orphaned_placeholders'].extend(
                        [f'chunk_{i}_code_{idx}' for idx in orphaned_placeholders]
                    )
                if orphaned_codes:
                    validation_stats['orphaned_codes'].extend(
                        [f'chunk_{i}_code_{idx}' for idx in orphaned_codes]
                    )
        
        # 记录验证结果
        if validation_stats['mismatched_chunks']:
            logger.warning(f'发现 {len(validation_stats["mismatched_chunks"])} 个分块存在代码占位符与metadata不匹配的问题')
            for mismatch in validation_stats['mismatched_chunks']:
                logger.warning(f'  分块 {mismatch["chunk_index"]} (ID: {mismatch["chunk_id"]}): '
                             f'缺失代码 {mismatch["orphaned_placeholders"]}, '
                             f'多余代码 {mismatch["orphaned_codes"]}')
        else:
            logger.info(f'分块完整性验证通过: {validation_stats["total_chunks"]} 个分块全部正常')
        
        return validation_stats