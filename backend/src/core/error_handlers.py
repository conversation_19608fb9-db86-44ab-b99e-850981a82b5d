"""
错误处理模块 - 提供全局异常处理
"""

from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

from .logging import app_logger as logger
from .exceptions import (
    DatabaseError,
    NotFoundError,
    ProcessingError,
    AuthenticationError,
    AuthorizationError,
    LLMServiceError
)

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证异常
    
    Args:
        request: HTTP请求
        exc: 验证异常
        
    Returns:
        JSON响应
    """
    error_detail = []
    for error in exc.errors():
        error_detail.append({
            'loc': error['loc'],
            'msg': error['msg'],
            'type': error['type']
        })
    
    logger.warning(f'请求验证失败: {error_detail}')
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            'code': status.HTTP_422_UNPROCESSABLE_ENTITY,
            'message': '请求参数验证失败',
            'detail': error_detail,
            'timestamp': datetime.now().isoformat()
        }
    )

async def database_exception_handler(request: Request, exc: DatabaseError):
    """
    处理数据库异常
    
    Args:
        request: HTTP请求
        exc: 数据库异常
        
    Returns:
        JSON响应
    """
    logger.error(f'数据库操作失败: {str(exc)}', exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'message': '数据库操作失败',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def not_found_exception_handler(request: Request, exc: NotFoundError):
    """
    处理资源未找到异常
    
    Args:
        request: HTTP请求
        exc: 资源未找到异常
        
    Returns:
        JSON响应
    """
    logger.warning(f'资源未找到: {str(exc)}')
    
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            'code': status.HTTP_404_NOT_FOUND,
            'message': '资源未找到',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def processing_exception_handler(request: Request, exc: ProcessingError):
    """
    处理处理错误异常
    
    Args:
        request: HTTP请求
        exc: 处理错误异常
        
    Returns:
        JSON响应
    """
    logger.error(f'处理错误: {str(exc)}', exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'message': '处理错误',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def authentication_exception_handler(request: Request, exc: AuthenticationError):
    """
    处理认证异常
    
    Args:
        request: HTTP请求
        exc: 认证异常
        
    Returns:
        JSON响应
    """
    logger.warning(f'认证失败: {str(exc)}')
    
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content={
            'code': status.HTTP_401_UNAUTHORIZED,
            'message': '认证失败',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def authorization_exception_handler(request: Request, exc: AuthorizationError):
    """
    处理授权异常
    
    Args:
        request: HTTP请求
        exc: 授权异常
        
    Returns:
        JSON响应
    """
    logger.warning(f'授权失败: {str(exc)}')
    
    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content={
            'code': status.HTTP_403_FORBIDDEN,
            'message': '授权失败',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def llm_service_exception_handler(request: Request, exc: LLMServiceError):
    """
    处理LLM服务异常
    
    Args:
        request: HTTP请求
        exc: LLM服务异常
        
    Returns:
        JSON响应
    """
    logger.error(f'LLM服务错误: {str(exc)}', exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            'code': status.HTTP_503_SERVICE_UNAVAILABLE,
            'message': 'LLM服务暂时不可用',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    """
    处理SQLAlchemy异常
    
    Args:
        request: HTTP请求
        exc: SQLAlchemy异常
        
    Returns:
        JSON响应
    """
    logger.error(f'数据库错误: {str(exc)}', exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'message': '数据库错误',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """
    处理通用异常
    
    Args:
        request: HTTP请求
        exc: 异常
        
    Returns:
        JSON响应
    """
    logger.error(f'未处理的异常: {str(exc)}', exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            'code': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'message': '服务器内部错误',
            'detail': str(exc),
            'timestamp': datetime.now().isoformat()
        }
    )

def register_exception_handlers(app):
    """
    注册所有异常处理器
    
    Args:
        app: FastAPI应用实例
    """
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)
    app.add_exception_handler(DatabaseError, database_exception_handler)
    app.add_exception_handler(NotFoundError, not_found_exception_handler)
    app.add_exception_handler(ProcessingError, processing_exception_handler)
    app.add_exception_handler(AuthenticationError, authentication_exception_handler)
    app.add_exception_handler(AuthorizationError, authorization_exception_handler)
    app.add_exception_handler(LLMServiceError, llm_service_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
