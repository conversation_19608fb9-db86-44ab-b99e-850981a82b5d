"""
中间件模块 - 提供各种中间件功能
"""

import time
from typing import Callable, Dict, Any
import uuid

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from .logging import app_logger

# -*- coding: utf-8 -*-

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件 - 记录所有HTTP请求和响应
    """
    
    def __init__(self, app: ASGIApp, exclude_paths: list = None):
        """
        初始化中间件
        
        Args:
            app: ASGI应用
            exclude_paths: 不记录日志的路径列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志
        
        Args:
            request: HTTP请求
            call_next: 下一个处理函数
            
        Returns:
            HTTP响应
        """
        # 检查是否需要排除此路径
        path = request.url.path
        if any(path.startswith(exclude_path) for exclude_path in self.exclude_paths):
            return await call_next(request)
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求开始
        start_time = time.time()
        
        # 收集请求信息
        request_info = {
            'request_id': request_id,
            'method': request.method,
            'path': path,
            'client_ip': request.client.host if request.client else None,
            'user_agent': request.headers.get('user-agent', ''),
            'query_params': dict(request.query_params),
        }
        
        # 记录请求日志
        app_logger.info(f'开始处理请求: {request.method} {path}', **request_info)
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 收集响应信息
            response_info = {
                'request_id': request_id,
                'status_code': response.status_code,
                'process_time_ms': round(process_time * 1000, 2)
            }
            
            # 记录响应日志
            log_level = 'INFO' if response.status_code < 400 else 'ERROR'
            getattr(app_logger, log_level.lower())(
                f'完成请求: {request.method} {path} - {response.status_code} - {response_info["process_time_ms"]}ms',
                **response_info
            )
            
            # 添加处理时间到响应头
            response.headers['X-Process-Time'] = str(response_info['process_time_ms'])
            response.headers['X-Request-ID'] = request_id
            
            return response
        except Exception as e:
            # 记录异常日志
            process_time = time.time() - start_time
            error_info = {
                'request_id': request_id,
                'error': str(e),
                'process_time_ms': round(process_time * 1000, 2)
            }
            app_logger.error(f'请求处理异常: {request.method} {path} - {str(e)}', **error_info, exc_info=True)
            raise
