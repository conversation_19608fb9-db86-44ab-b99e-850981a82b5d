from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
from sqlalchemy import create_engine, event
import platform
from typing import AsyncGenerator

from .config import settings

# 根据数据库类型配置不同的引擎参数
if settings.DB_TYPE == 'sqlite':
    engine_args = {
        'echo': settings.SQL_ECHO,
        'connect_args': {
            'check_same_thread': False,
            'timeout': 60,  # 增加超时时间
            'isolation_level': 'IMMEDIATE'  # 事务隔离级别
        }
    }
    # SQLite的URI需要修改为异步格式
    db_url = f'sqlite+aiosqlite:///{settings.SQLITE_DB_FILE}'
    sync_db_url = f'sqlite:///{settings.SQLITE_DB_FILE}'
else:
    # 非SQLite数据库（如PostgreSQL）的配置
    engine_args = {
        'pool_size': 5,
        'max_overflow': 10,
        'pool_timeout': 30,
        'pool_recycle': 1800
    }
    db_url = settings.SQLALCHEMY_DATABASE_URI.replace('postgresql://', 'postgresql+asyncpg://')
    sync_db_url = settings.SQLALCHEMY_DATABASE_URI

# 创建异步引擎
engine = create_async_engine(
    db_url,
    **engine_args
)

# 创建同步引擎（用于Windows平台）
sync_engine = create_engine(
    sync_db_url,
    **{k: v for k, v in engine_args.items() if k != 'connect_args'}
)

# 创建异步会话工厂
async_session_factory = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

Base = declarative_base()

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖函数
    """
    async with async_session_factory() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            await session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f'数据库错误: {str(e)}'
            )
        finally:
            await session.close()

async def init_db():
    """
    初始化数据库，创建所有表
    """
    try:
        # 导入所有模型以确保它们被注册
        from ..models.knowledge import Knowledge, KnowledgeChunk, KnowledgeSource
        from ..models.user import User
        from ..models.user_settings import UserSettings
        from ..models.chat import ChatSession, ChatMessage, MessageReference
        from ..models.import_task import ImportTask
        # Windows平台使用同步方式初始化
        if platform.system() == 'Windows':
            Base.metadata.create_all(sync_engine)
        else:
            # 非Windows平台使用异步方式初始化
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'数据库初始化失败: {str(e)}'
        )

def init_db_sync():
    """
    同步初始化数据库，创建所有表（用于Windows平台）
    """
    try:
        # 导入所有模型以确保它们被注册
        from ..models.knowledge import Knowledge, KnowledgeChunk, KnowledgeSource
        from ..models.user import User
        from ..models.user_settings import UserSettings
        from ..models.chat import ChatSession, ChatMessage, MessageReference
        from ..models.import_task import ImportTask
        # 使用同步方式初始化
        Base.metadata.create_all(sync_engine)
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'数据库初始化失败: {str(e)}'
        ) 