# -*- coding: utf-8 -*-
from fastapi import HTTPException, status

class HarmonyQAException(HTTPException):
    def __init__(self, code: int, message: str, details: dict = None):
        super().__init__(status_code=code, detail={'message': message, 'details': details})

class ProcessingError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'处理错误: {detail}'
        )

class DatabaseError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'数据库错误: {detail}'
        )

class NotFoundError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )

class ValidationError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f'验证错误: {detail}'
        )

class AuthenticationError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f'认证错误: {detail}',
            headers={'WWW-Authenticate': 'Bearer'}
        )

class AuthorizationError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f'权限错误: {detail}'
        )

class LLMServiceError(HTTPException):
    def __init__(self, detail: str, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR):
        super().__init__(
            status_code=status_code,
            detail=f'LLM服务错误: {detail}'
        )

class LLMServiceTimeoutError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'LLM服务超时: {detail}',
            status_code=status.HTTP_504_GATEWAY_TIMEOUT
        )

class LLMServiceUnavailableError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'LLM服务不可用: {detail}',
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE
        )

class LLMServiceRequestError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'LLM服务请求错误: {detail}',
            status_code=status.HTTP_400_BAD_REQUEST
        )

class LLMServiceAuthError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'LLM服务认证错误: {detail}',
            status_code=status.HTTP_401_UNAUTHORIZED
        )

class LLMServiceQuotaExceededError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'LLM服务配额超限: {detail}',
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )

class LLMServiceContentFilterError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'内容被过滤: {detail}',
            status_code=status.HTTP_403_FORBIDDEN
        )

class LLMServiceInvalidInputError(LLMServiceError):
    def __init__(self, detail: str):
        super().__init__(
            detail=f'无效的输入: {detail}',
            status_code=status.HTTP_400_BAD_REQUEST
        ) 