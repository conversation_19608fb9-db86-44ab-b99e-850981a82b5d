# -*- coding: utf-8 -*-
import os
import json
import traceback
import numpy as np
import faiss
from datetime import datetime
from typing import List, Dict, Any, Optional
from .logging import get_module_logger
from .constants import KnowledgeDomain
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class VectorStore:
    """FAISS向量存储，支持按领域分开存储"""
    
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.indices = {}  # 每个领域一个索引
        self.metadata = {}  # 每个领域一个元数据字典
        self._ensure_dirs()
        self.load_state()
        self._executor = ThreadPoolExecutor(max_workers=4)
    
    def _ensure_dirs(self):
        """确保目录存在"""
        os.makedirs(self.base_path, exist_ok=True)
        # 为每个领域创建子目录
        for domain in KnowledgeDomain:
            os.makedirs(os.path.join(self.base_path, domain.value), exist_ok=True)
    
    async def add_vectors(
        self,
        vectors: List[List[float]],
        metadata: List[Dict[str, Any]],
        domain: str = None  # domain参数可选，如果选了就全部存到这个domain
    ) -> List[str]:
        """
        异步添加向量到存储
        :param vectors: 向量列表
        :param metadata: 元数据列表，每个元数据必须包含knowledge_id和chunk_id字段
        :param domain: 可选的默认领域，如果metadata中没有指定domain则使用此值
        :return: 向量ID列表
        """
        def _add_vectors():
            try:
                # 按domain分组向量和元数据
                domain_vectors = {}
                domain_metadata = {}
                start_ids = {}
                vector_ids = []

                if domain:
                    vec_domain = domain
                else:
                    vec_domain = KnowledgeDomain.DEFAULT

                # 转换为numpy数组并进行归一化
                vectors_array = np.array(vectors).astype('float32')
                if vectors_array.size > 0:
                    faiss.normalize_L2(vectors_array)

                # 分组处理向量和元数据
                for i, (vector, meta) in enumerate(zip(vectors, metadata)):
                    if meta.get('domain'):
                        vec_domain = meta.get('domain')
                    else:
                        vec_domain = domain

                    # 初始化领域数据结构
                    if vec_domain not in domain_vectors:
                        domain_vectors[vec_domain] = []
                        domain_metadata[vec_domain] = {}
                        start_ids[vec_domain] = len(self.metadata.get(vec_domain, {}))
                    
                    vector_id = start_ids[vec_domain]
                    start_ids[vec_domain] += 1

                    # 添加向量和元数据到对应领域
                    domain_vectors[vec_domain].append(vector)
                    domain_metadata[vec_domain][vector_id] = {
                        **meta,
                        'id': vector_id,
                        'domain': vec_domain,
                        'created_at': datetime.now().isoformat()
                    }
                    vector_ids.append(vector_id)

                # 处理每个领域的向量
                for vec_domain, domain_vecs in domain_vectors.items():
                    # 确保领域索引存在
                    if vec_domain not in self.indices or self.indices[vec_domain] is None:
                        dimension = len(domain_vecs[0])
                        self.indices[vec_domain] = faiss.IndexFlatIP(dimension)
                        self.metadata[vec_domain] = {}

                    # 添加向量到索引
                    domain_vectors_array = np.array(domain_vecs).astype('float32')
                    if domain_vectors_array.size > 0:
                        faiss.normalize_L2(domain_vectors_array)
                        self.indices[vec_domain].add(domain_vectors_array)

                    # 更新元数据
                    self.metadata[vec_domain].update(domain_metadata[vec_domain])

                # 保存状态
                self._save_state()
                
                return vector_ids
                
            except Exception as e:
                logger.error(f'添加向量失败: {str(e)}')
                raise
        
        # 在线程池中执行同步操作
        return await asyncio.get_event_loop().run_in_executor(self._executor, _add_vectors)
    
    async def search_similar(
        self,
        query_vector: List[float],
        domains: List[str] = None,
        limit: int = 5,
        min_score: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        异步搜索相似向量
        :param query_vector: 查询向量
        :param domains: 要搜索的领域列表，如果为None则搜索所有领域
        :param limit: 每个领域返回的结果数量
        :param min_score: 最小相似度分数
        :return: 相似结果列表
        """
        start_time = time.time()
        
        def _search_similar():
            try:
                # 如果没有指定领域，使用所有领域
                search_domains = domains if domains else list(self.indices.keys())
                if not search_domains:
                    logger.info(f'性能监控[VectorStore]: 没有可搜索的领域, 耗时: {(time.time() - start_time)*1000:.2f}ms')
                    return []

                # 转换查询向量
                query_array = np.array([query_vector]).astype('float32')
                
                # 归一化查询向量
                if query_array.size > 0:
                    faiss.normalize_L2(query_array)
                else:
                    logger.info(f'性能监控[VectorStore]: 查询向量为空, 耗时: {(time.time() - start_time)*1000:.2f}ms')
                    return []

                all_results = []
                
                # 在每个领域中搜索
                for domain in search_domains:
                    if domain not in self.indices or self.indices[domain] is None or self.indices[domain].ntotal == 0:
                        logger.debug(f'领域{domain}没有索引或索引为空')
                        continue
                    
                    logger.debug(f'{domain}领域的向量数: {self.indices[domain].ntotal}')
                    # 在当前领域搜索
                    scores, indices = self.indices[domain].search(query_array, limit)
                    
                    # 处理当前领域的结果
                    for i, idx in enumerate(indices[0]):
                        if idx < 0:
                            continue
                        
                        score = float(scores[0][i])
                        if score < min_score:
                            continue
                            
                        vector_id = str(idx)
                        if vector_id in self.metadata[domain]:
                            result = {
                                **self.metadata[domain][vector_id],
                                'score': score
                            }
                            all_results.append(result)
                # 按相似度排序
                all_results.sort(key=lambda x: x['score'], reverse=True)
                
                # 只返回总体最相似的limit个结果
                all_results = all_results[:limit]
                
                end_time = time.time()
                logger.info(f'性能监控[VectorStore]: search_similar 向量搜索完成, 搜索的领域：{search_domains}, 找到{len(all_results)}条结果, 总耗时: {(end_time - start_time)*1000:.2f}ms')
    
                return all_results
                
            except Exception as e:
                end_time = time.time()
                logger.error(f'搜索向量失败: {str(e)}')
                logger.debug(f'搜索向量失败: {traceback.format_exc()}')
                logger.info(f'性能监控[VectorStore]: 向量搜索失败, 耗时: {(end_time - start_time)*1000:.2f}ms')
                return []
        
        # 在线程池中执行同步操作
        return await asyncio.get_event_loop().run_in_executor(self._executor, _search_similar)
    
    def get_vector(self, vector_id: str, domain: str = KnowledgeDomain.DEFAULT) -> Optional[Dict[str, Any]]:
        """
        获取向量元数据
        :param vector_id: 向量ID
        :param domain: 领域名称
        :return: 向量元数据
        """
        if domain not in self.metadata:
            return None
        return self.metadata[domain].get(vector_id)
    
    async def delete_vectors(self, vector_ids: List[str], domain: str = KnowledgeDomain.DEFAULT) -> bool:
        """
        异步删除向量
        :param vector_ids: 向量ID列表
        :param domain: 领域名称
        :return: 是否成功
        """
        try:
            if domain not in self.metadata or not self.metadata[domain]:
                return True
                
            # 收集未删除的向量
            keep_vectors = []
            keep_metadata = []
            
            # 在线程池中执行同步操作获取所有向量
            def get_all_vectors():
                if domain not in self.indices or self.indices[domain] is None or self.indices[domain].ntotal == 0:
                    return []
                return self.indices[domain].reconstruct_n(0, self.indices[domain].ntotal)
                
            all_vectors = await asyncio.get_event_loop().run_in_executor(self._executor, get_all_vectors)
            
            def filter_vectors():
                # 筛选要保留的向量
                for i, vector_id in enumerate(self.metadata[domain].keys()):
                    if vector_id not in vector_ids:
                        keep_vectors.append(all_vectors[i])
                        keep_metadata.append(self.metadata[domain][vector_id])
                
            await asyncio.get_event_loop().run_in_executor(self._executor, filter_vectors)
            
            # 清空当前索引和元数据
            self.metadata[domain] = {}
            self.indices[domain] = None
            if keep_vectors:
                await self.add_vectors(keep_vectors, keep_metadata, domain)
            
            return True
            
        except Exception as e:
            logger.error(f'删除向量失败: {str(e)}')
            return False
    
    def _save_state(self):
        """保存索引和元数据到文件"""
        try:
            # 为每个领域保存
            for domain in self.indices.keys():
                domain_path = os.path.join(self.base_path, domain)
                os.makedirs(domain_path, exist_ok=True)
                
                # 保存索引
                if self.indices[domain] is not None:
                    index_path = os.path.join(domain_path, 'index.faiss')
                    faiss.write_index(self.indices[domain], index_path)
                
                # 保存元数据
                metadata_path = os.path.join(domain_path, 'metadata.json')
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(self.metadata[domain], f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            logger.error(f'保存状态失败: {str(e)}')
    
    def load_state(self):
        """从文件加载索引和元数据"""
        try:
            # 遍历所有领域目录
            for domain in os.listdir(self.base_path):
                domain_path = os.path.join(self.base_path, domain)
                if not os.path.isdir(domain_path):
                    continue
                
                # 加载索引
                index_path = os.path.join(domain_path, 'index.faiss')
                if os.path.exists(index_path):
                    try:
                        self.indices[domain] = faiss.read_index(index_path)
                    except Exception as e:
                        logger.error(f"加载领域 {domain} 的索引失败: {str(e)}")
                        self.indices[domain] = None
                
                # 加载元数据
                metadata_path = os.path.join(domain_path, 'metadata.json')
                if os.path.exists(metadata_path):
                    try:
                        with open(metadata_path, 'r', encoding='utf-8') as f:
                            self.metadata[domain] = json.load(f)
                    except Exception as e:
                        logger.error(f'加载领域 {domain} 的元数据失败: {str(e)}')
                        self.metadata[domain] = {}
                        
        except Exception as e:
            logger.error(f'加载状态失败: {str(e)}') 