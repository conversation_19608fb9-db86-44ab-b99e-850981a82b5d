# -*- coding: utf-8 -*-

"""
嵌入模型接口 - 为应用提供文本向量表示能力

此模块负责加载和管理文本嵌入模型，用于相似度计算和向量化操作
"""

from typing import Dict, Any, Optional, List, Union
import os
import pathlib
from .logging import get_module_logger
from sentence_transformers import SentenceTransformer
from functools import lru_cache

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

# 支持的嵌入模型列表
SUPPORTED_MODELS = {
    'all-MiniLM-L6-v2': {
        'description': '轻量级多语言向量化模型',
        'dimension': 384
    },
    'paraphrase-multilingual-MiniLM-L12-v2': {
        'description': '多语言支持更好的向量化模型',
        'dimension': 384
    },
    'text2vec-large-chinese': {
        'description': '专为中文优化的向量化模型',
        'dimension': 1024
    },
    'BAAI/bge-m3': {
        'description': '多语言多功能文本嵌入模型',
        'dimension': 1024
    }
}

# 备用模型，仅在加载主模型失败时使用
FALLBACK_MODEL = 'moka-ai/m3e-small'

# 全局模型实例
_embedding_model = None

def _is_local_model_path(model_path: str) -> bool:
    """
    判断给定的路径是否为本地模型路径
    
    Args:
        model_path: 模型路径
        
    Returns:
        是否为本地模型路径
    """
    # 检查是否为绝对路径
    if os.path.isabs(model_path):
        return os.path.exists(model_path)
    
    # 检查相对路径
    current_dir = os.getcwd()
    abs_path = os.path.join(current_dir, model_path)
    return os.path.exists(abs_path)

@lru_cache(maxsize=1)
def get_embedding_model(model_name: Optional[str] = None) -> SentenceTransformer:
    """
    获取嵌入模型实例（单例模式）
    
    模型加载优先级：
    1. 参数传入的model_name
    2. 配置文件中的EMBEDDING_MODEL设置
    
    Args:
        model_name: 模型名称或本地模型路径，如果为None则使用配置
        
    Returns:
        嵌入模型实例
        
    Raises:
        RuntimeError: 当无法加载任何模型时抛出
    """
    global _embedding_model
    
    if _embedding_model is not None:
        return _embedding_model
    
    # 从配置文件获取设置
    from ..core.config import settings
    
    # 确定使用的模型名称
    if model_name is None:
        model_name = settings.EMBEDDING_MODEL
        logger.info(f'从配置文件中获取嵌入模型: {model_name}')
    
    # 获取设备配置
    device = settings.EMBEDDING_DEVICE
    logger.info(f'使用设备: {device}')
    
    # 检查是否是本地模型路径
    is_local_path = _is_local_model_path(model_name)
    
    # 尝试加载模型
    try:
        if is_local_path:
            logger.info(f'正在加载本地嵌入模型: {model_name}')
            # 确保路径规范化
            model_path = os.path.abspath(os.path.expanduser(model_name))
            _embedding_model = SentenceTransformer(model_path, device=device)
            logger.info(f'本地嵌入模型加载完成: {model_path}')
        else:
            logger.info(f'正在加载在线嵌入模型: {model_name}')
            _embedding_model = SentenceTransformer(model_name, device=device)
            logger.info(f'在线嵌入模型加载完成: {model_name}')
        
        return _embedding_model
    except Exception as first_error:
        logger.error(f"加载嵌入模型 '{model_name}' 失败: {str(first_error)}")
        
        # 回退策略1: 尝试加载备用模型
        try:
            logger.warning(f'尝试加载备用嵌入模型: {FALLBACK_MODEL}')
            _embedding_model = SentenceTransformer(FALLBACK_MODEL, device=device)
            logger.info(f'备用嵌入模型加载完成: {FALLBACK_MODEL}')
            return _embedding_model
        except Exception as second_error:
            logger.error(f'加载备用嵌入模型也失败: {str(second_error)}')
            
            # 回退策略2: 尝试加载最轻量级的模型
            try:
                minimal_model = 'all-MiniLM-L6-v2'
                logger.warning(f'尝试加载最小嵌入模型: {minimal_model}')
                _embedding_model = SentenceTransformer(minimal_model, device=device)
                logger.info(f'最小嵌入模型加载完成: {minimal_model}')
                return _embedding_model
            except Exception as third_error:
                logger.critical('所有嵌入模型加载尝试均失败')
                error_message = (
                    f'无法加载任何嵌入模型:\n'
                    f'1. 主模型 "{model_name}": {str(first_error)}\n'
                    f'2. 备用模型 "{FALLBACK_MODEL}": {str(second_error)}\n'
                    f'3. 最小模型 "{minimal_model}": {str(third_error)}'
                )
                logger.critical(error_message)
                raise RuntimeError(error_message)

def get_supported_models() -> Dict[str, Any]:
    """
    获取所有支持的模型信息
    
    Returns:
        支持的模型信息字典
    """
    return SUPPORTED_MODELS

def clear_model_cache() -> None:
    """
    清除模型缓存
    """
    global _embedding_model
    _embedding_model = None
    # 清除lru_cache
    get_embedding_model.cache_clear()
    logger.info('已清除嵌入模型缓存')

def compute_similarity(text1: str, text2: str) -> float:
    """
    计算两段文本的余弦相似度
    
    Args:
        text1: 第一段文本
        text2: 第二段文本
        
    Returns:
        相似度分数 (0-1)
    """
    try:
        model = get_embedding_model()
        embedding1 = model.encode(text1, convert_to_tensor=True)
        embedding2 = model.encode(text2, convert_to_tensor=True)
        
        from sentence_transformers import util
        similarity = util.pytorch_cos_sim(embedding1, embedding2).item()
        return float(similarity)
    except Exception as e:
        logger.error(f'计算文本相似度失败: {str(e)}')
        return 0.0

def compute_embeddings(texts: List[str]) -> List[List[float]]:
    """
    批量计算文本嵌入向量
    
    Args:
        texts: 文本列表
        
    Returns:
        嵌入向量列表
    """
    try:
        model = get_embedding_model()
        embeddings = model.encode(texts)
        return embeddings.tolist()
    except Exception as e:
        logger.error(f'批量计算嵌入向量失败: {str(e)}')
        # 返回空向量列表
        return [[0.0] * 768] * len(texts) 