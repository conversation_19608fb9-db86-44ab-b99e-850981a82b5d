import re
import string
from typing import Dict, Any, Set, Optional
from .logging import get_module_logger
from .config import settings

logger = get_module_logger(__name__)
import nltk
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')
from nltk.corpus import stopwords

class ContentCleaner:
    """文本清洗类，负责对markdown/txt文档进行清洗处理"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化文本清洗器
        
        Args:
            config: 清洗配置，包含以下可选项:
                - keep_tables: 是否保留表格，默认True
                - keep_headers: 是否保留标题，默认True
                - keep_list_format: 是否保留列表格式，默认True
                - remove_html_tags: 是否移除HTML标签，默认True
                - remove_urls: 是否移除URL，默认True
                - remove_email: 是否移除邮箱地址，默认True
                - remove_special_chars: 是否移除特殊字符，默认True
                - language: 停用词语言，默认'chinese'
                - irrelevance_patterns: 不相关内容模式列表
        """
        self.config = {
            'keep_tables': True,
            'keep_headers': True,
            'keep_list_format': True,
            'remove_html_tags': True,
            'remove_urls': True,
            'remove_email': True,
            'remove_special_chars': True,
            'language': 'chinese',
            'irrelevance_patterns': [
                '版权所有', '著作权', '保留所有权利', 
                '未经许可', '禁止转载', '侵权必究',
                '本文由', '编辑整理', '仅供参考',
                '关注我们', '公众号', '扫描二维码',
                '作者简介', '作者介绍', '作者信息',
                "作者是", "工号为", "联系方式",
            ]
        }

        self.config.update(settings.DATA_CLEANING_CONFIGS)
        
        if config:
            self.config.update(config)
            
        # 加载停用词
        self.stopwords: Set[str] = set()
        self._load_stopwords()
            
        # 预编译正则表达式
        self.regex_html = re.compile(r'<[^>]+>')
        self.regex_url = re.compile(r'https?://\S+|www\.\S+')
        self.regex_email = re.compile(r'\S+@\S+')
        self.regex_special_chars = re.compile(f'[{re.escape(string.punctuation)}]')
        
        # Markdown特殊元素的正则表达式
        self.regex_code_block = re.compile(r'```[^\n]*\n[\s\S]*?```')
        self.regex_inline_code = re.compile(r'`[^`\n]+`')
        self.regex_header = re.compile(r'^#+\s+.*$', re.MULTILINE)
        self.regex_table = re.compile(r'\|.*\|[\s\S]*?\|.*\|')
        
        # 超链接正则表达式 - 匹配 Markdown 格式的链接 [文本](链接)
        self.regex_md_link = re.compile(r'\[([^\]]+)\]\(([^)]+)\)')
        # 匹配参考式链接 [文本][引用] 和它的定义 [引用]: URL
        self.regex_ref_link = re.compile(r'\[([^\]]+)\]\[([^\]]+)\]')
        self.regex_ref_def = re.compile(r'^\[([^\]]+)\]:\s*(.*?)$', re.MULTILINE)
        # 匹配自动链接 <http://example.com>
        self.regex_auto_link = re.compile(r'<(https?://[^>]+)>')
        
        # 图片链接正则表达式
        self.regex_image_link = re.compile(r'!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]+)")?\)')
        
        # 列表项正则表达式
        self.regex_list_item = re.compile(r'^(\s*[-*+]|\s*\d+\.)\s+(.*)$', re.MULTILINE)
        
        # 括号相关的正则表达式
        self.regex_parentheses = re.compile(r'\([^()]*\)')
        self.regex_braces = re.compile(r'\{[^{}]*\}')
        self.regex_brackets = re.compile(r'\[[^\[\]]*\]')
        
    def _load_stopwords(self) -> None:
        """加载停用词"""
        # 加载NLTK停用词
        try:
            if self.config['language'] == 'chinese':
                self.stopwords = set(stopwords.words('chinese'))
            elif self.config['language'] == 'english':
                self.stopwords = set(stopwords.words('english'))
            else:
                self.stopwords = set()
                logger.warning(f'不支持的语言: {self.config["language"]}，停用词列表为空')
        except Exception as e:
            logger.error(f'加载NLTK停用词失败: {str(e)}')
            self.stopwords = set()
            
    def _remove_empty_lines_between_list_items(self, text: str) -> str:
        """
        移除列表项之间的空行
        
        Args:
            text: 处理后的文本
            
        Returns:
            移除列表项之间空行的文本
        """
        # 使用正则表达式匹配列表项标记
        list_pattern = r'^(\s*[-*+]|\s*\d+\.)\s+(.*)$'
        
        lines = text.split('\n')
        result_lines = []
        
        i = 0
        while i < len(lines):
            # 添加当前行
            result_lines.append(lines[i])
            
            # 如果当前行是列表项，检查下一行
            if re.match(list_pattern, lines[i]):
                # 跳过空行直到找到下一个非空行
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    j += 1
                
                # 如果下一个非空行是列表项，直接跳到那一行
                if j < len(lines) and re.match(list_pattern, lines[j]):
                    i = j - 1  # 减1是因为循环结束会加1
                
            i += 1
            
        return '\n'.join(result_lines)

    def clean_text(self, text: str) -> str:
        """
        清洗文本
        
        Args:
            text: 待清洗文本
            
        Returns:
            清洗后的文本
        """
        if not text:
            return ""
            
        # 确保代码块配对完整
        text = self._ensure_complete_code_blocks(text)
            
        # 分离代码块和非代码块内容
        code_blocks = {}
        text = self._extract_code_blocks(text, code_blocks)
        
        # 分离并保存表格
        tables = {}
        if self.config['keep_tables']:
            text = self._extract_tables(text, tables)
        
        # 分离并保存标题
        headers = {}
        if self.config['keep_headers']:
            text = self._extract_headers(text, headers)
        
        # 分离并保存列表项
        list_items = {}
        if self.config['keep_list_format']:
            text = self._extract_list_items(text, list_items)
        
        # 无条件删除所有图片链接
        text = self._process_images(text)
        
        # 超链接只保留文本
        text = self._process_links(text)

        # 处理表格和列表内的链接和URL，但保留格式
        if self.config['keep_tables'] or self.config['keep_list_format']:
            # 确保列表和表格中的图片链接也被处理
            self._process_special_elements_content(tables, list_items)
            
        # 移除HTML标签
        if self.config['remove_html_tags']:
            text = self.regex_html.sub('', text)
            
        # 移除URL
        if self.config['remove_urls']:
            text = self.regex_url.sub('', text)
            
        # 移除邮箱
        if self.config['remove_email']:
            text = self.regex_email.sub('', text)
        
        # 分词以便处理停用词
        words = []
        for line in text.split('\n'):
            # 跳过特殊占位符行
            if (line.strip().startswith('__CODE_BLOCK_') or 
                line.strip().startswith('__HEADER_') or 
                line.strip().startswith('__LIST_ITEM_') or
                line.strip().startswith('__TABLE_')):
                words.append(line)
                continue
                
            # 保持段落结构
            line_words = []
            for word in line.split():
                # 跳过特殊占位符
                if (word.startswith('__CODE_BLOCK_') or 
                    word.startswith('__HEADER_') or 
                    word.startswith('__LIST_ITEM_') or
                    word.startswith('__TABLE_')):
                    line_words.append(word)
                    continue
                    
                # 移除特殊字符
                if self.config['remove_special_chars']:
                    word = self.regex_special_chars.sub('', word)
                    
                # 移除停用词
                if word.lower() in self.stopwords:
                    continue
                    
                if word:  # 确保词不为空
                    line_words.append(word)
                    
            words.append(' '.join(line_words))
            
        # 重新组合文本，保持段落格式
        text = '\n'.join(words)
        
        # 恢复列表项
        for placeholder, list_item in list_items.items():
            text = text.replace(placeholder, list_item)
            
        # 恢复标题
        for placeholder, header in headers.items():
            text = text.replace(placeholder, header)
            
        # 恢复表格
        for placeholder, table in tables.items():
            text = text.replace(placeholder, table)
            
        # 恢复代码块
        for placeholder, code in code_blocks.items():
            text = text.replace(placeholder, code)
            
        # 去除连续多个空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 移除列表项之间的空行
        if self.config['keep_list_format']:
            text = self._remove_empty_lines_between_list_items(text)
        
        return text
    
    def _ensure_complete_code_blocks(self, text: str) -> str:
        """
        确保代码块的完整性，如有不完整的代码块标记，进行修复
        
        Args:
            text: 原始文本
            
        Returns:
            修复后的文本
        """
        # 统计 ``` 的数量
        backtick_count = text.count('```')
        
        # 如果是奇数，说明有不配对的代码块标记
        if backtick_count % 2 != 0:
            logger.warning("发现不配对的代码块标记，尝试修复")
            
            # 找到最后一个 ``` 的位置
            last_pos = text.rfind('```')
            
            # 如果最后一个是起始标记，添加结束标记
            if '```' in text[last_pos+3:]:
                # 是结束标记，添加一个起始标记在开头
                text = '```\n' + text
            else:
                # 是起始标记，添加一个结束标记在末尾
                text = text + '\n```'
                
        return text
    
    def _extract_code_blocks(self, text: str, code_blocks: Dict[str, str]) -> str:
        """
        提取代码块，用占位符替换，以便在处理后恢复
        
        Args:
            text: 原始文本
            code_blocks: 保存代码块的字典
            
        Returns:
            替换后的文本
        """
        # 先找到所有代码块
        matches = list(self.regex_code_block.finditer(text))
        
        # 从后向前替换，避免位置偏移
        for i, match in enumerate(reversed(matches)):
            placeholder = f"__CODE_BLOCK_{i}__"
            code = match.group(0)
            code_blocks[placeholder] = code
            
            # 精确替换匹配内容
            start, end = match.span()
            text = text[:start] + placeholder + text[end:]
        
        # 处理内联代码
        matches = list(self.regex_inline_code.finditer(text))
        for i, match in enumerate(reversed(matches)):
            placeholder = f"__INLINE_CODE_{i}__"
            code = match.group(0)
            code_blocks[placeholder] = code
            
            start, end = match.span()
            text = text[:start] + placeholder + text[end:]
            
        return text
    
    def _extract_headers(self, text: str, headers: Dict[str, str]) -> str:
        """
        提取并保存Markdown标题
        
        Args:
            text: 原始文本
            headers: 保存标题的字典
            
        Returns:
            替换标题后的文本
        """
        # 找到所有标题
        matches = list(self.regex_header.finditer(text))
        
        # 从后向前替换，避免位置偏移
        for i, match in enumerate(reversed(matches)):
            placeholder = f"__HEADER_{i}__"
            header = match.group(0)
            headers[placeholder] = header
            
            # 精确替换匹配内容
            start, end = match.span()
            text = text[:start] + placeholder + text[end:]
            
        return text
    
    def _extract_list_items(self, text: str, list_items: Dict[str, str]) -> str:
        """
        提取并保存Markdown列表项
        
        Args:
            text: 原始文本
            list_items: 保存列表项的字典
            
        Returns:
            替换列表项后的文本
        """
        # 找到所有列表项
        matches = list(self.regex_list_item.finditer(text))
        
        # 从后向前替换，避免位置偏移
        for i, match in enumerate(reversed(matches)):
            placeholder = f"__LIST_ITEM_{i}__"
            list_item = match.group(0)
            list_items[placeholder] = list_item
            
            # 精确替换匹配内容
            start, end = match.span()
            text = text[:start] + placeholder + text[end:]
            
        return text
    
    def _process_images(self, text: str) -> str:
        """
        处理图片链接
        """
        pattern = r'!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]+)")?\)'
        text = re.sub(pattern, '', text)
        return text
    
    def _process_links(self, text: str) -> str:
        """
        处理Markdown格式的链接，只保留链接文本
        
        Args:
            text: 原始文本
            
        Returns:
            处理后的文本
        """
        # 删除图片链接 ![alt文本](链接 "标题")
        pattern = r'!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]+)")?\)'
        text = re.sub(pattern, '', text)
        
        # 处理标准Markdown链接 [文本](链接)
        text = self.regex_md_link.sub(r'\1', text)
        
        # 处理参考式链接 [文本][引用]
        text = self.regex_ref_link.sub(r'\1', text)
        
        # 删除参考式链接定义 [引用]: URL
        text = self.regex_ref_def.sub('', text)
        
        # 处理自动链接 <http://example.com>
        text = self.regex_auto_link.sub('', text)
        
        return text
    
    def _extract_tables(self, text: str, tables: Dict[str, str]) -> str:
        """
        提取并保存Markdown表格
        
        Args:
            text: 原始文本
            tables: 保存表格的字典
            
        Returns:
            替换表格后的文本
        """
        # 找到所有表格
        matches = list(self.regex_table.finditer(text))
        
        # 从后向前替换，避免位置偏移
        for i, match in enumerate(reversed(matches)):
            placeholder = f"__TABLE_{i}__"
            table = match.group(0)
            tables[placeholder] = table
            
            # 精确替换匹配内容
            start, end = match.span()
            text = text[:start] + placeholder + text[end:]
            
        return text
        
    def _process_special_elements_content(self, tables: Dict[str, str], list_items: Dict[str, str]) -> None:
        """
        处理表格和列表内的内容，如URL和链接，但保留格式
        
        Args:
            tables: 表格字典
            list_items: 列表项字典
        """
        # 图片链接正则表达式
        img_pattern = r'!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]+)")?\)'
        
        # 处理表格内的内容
        for placeholder, table in tables.items():
            # 删除所有图片链接
            table = re.sub(img_pattern, '', table)
            
            # 处理表格内的标准Markdown链接 [文本](链接)
            table = self.regex_md_link.sub(r'\1', table)
            
            # 处理表格内的参考式链接 [文本][引用]
            table = self.regex_ref_link.sub(r'\1', table)
            
            # 处理表格内的自动链接 <http://example.com>
            if self.config['remove_urls']:
                table = self.regex_auto_link.sub('', table)
                
            # 更新处理后的表格
            tables[placeholder] = table
            
        # 处理列表内的内容
        for placeholder, list_item in list_items.items():
            # 删除所有图片链接
            list_item = re.sub(img_pattern, '', list_item)
            
            # 处理列表内的标准Markdown链接 [文本](链接)
            list_item = self.regex_md_link.sub(r'\1', list_item)
            
            # 处理列表内的参考式链接 [文本][引用]
            list_item = self.regex_ref_link.sub(r'\1', list_item)
            
            # 处理列表内的自动链接 <http://example.com>
            if self.config['remove_urls']:
                list_item = self.regex_auto_link.sub('', list_item)
                
            # 更新处理后的列表项
            list_items[placeholder] = list_item

    def remove_duplicates(self, text: str) -> str:
        """
        移除重复段落
        
        Args:
            text: 待处理文本
            
        Returns:
            处理后的文本
        """
        # 提取特殊元素
        code_blocks = {}
        text = self._extract_code_blocks(text, code_blocks)
        
        tables = {}
        if self.config['keep_tables']:
            text = self._extract_tables(text, tables)
        
        headers = {}
        if self.config['keep_headers']:
            text = self._extract_headers(text, headers)
            
        list_items = {}
        if self.config['keep_list_format']:
            text = self._extract_list_items(text, list_items)
        
        # 处理图片链接
        text = self._process_images(text)
        
        # 处理链接
        text = self._process_links(text)
        
        # 分割处理常规文本段落
        paragraphs = text.split('\n\n')
        unique_paragraphs = []
        seen_paragraphs = set()
        
        for para in paragraphs:
            # 忽略特殊占位符段落
            if (para.strip().startswith('__CODE_BLOCK_') or 
                para.strip().startswith('__HEADER_') or 
                para.strip().startswith('__LIST_ITEM_') or
                para.strip().startswith('__TABLE_')):
                unique_paragraphs.append(para)
                continue
                
            # 标准化段落以便比较
            norm_para = re.sub(r'\s+', ' ', para.strip()).lower()
                
            if norm_para not in seen_paragraphs:
                seen_paragraphs.add(norm_para)
                unique_paragraphs.append(para)
        
        # 重新组合文本
        text = '\n\n'.join(unique_paragraphs)
        
        # 恢复特殊元素
        # 图片链接不需要恢复
            
        for placeholder, list_item in list_items.items():
            text = text.replace(placeholder, list_item)
            
        for placeholder, header in headers.items():
            text = text.replace(placeholder, header)
            
        for placeholder, table in tables.items():
            text = text.replace(placeholder, table)
            
        for placeholder, code in code_blocks.items():
            text = text.replace(placeholder, code)
            
        return text
    
    def remove_irrelevant_content(self, text: str) -> str:
        """
        移除不相关内容
        
        Args:
            text: 待处理文本
            irrelevance_patterns: 不相关内容的正则表达式列表，None表示不移除任何内容
            
        Returns:
            处理后的文本
        """
        if not text or not self.config['irrelevance_patterns']:
            return text
        
        # 提取特殊元素
        code_blocks = {}
        text = self._extract_code_blocks(text, code_blocks)
        
        tables = {}
        if self.config['keep_tables']:
            text = self._extract_tables(text, tables)
        
        headers = {}
        if self.config['keep_headers']:
            text = self._extract_headers(text, headers)
            
        list_items = {}
        if self.config['keep_list_format']:
            text = self._extract_list_items(text, list_items)
        
        # 确保删除图片链接
        text = self._process_images(text)
        
        # 处理常规文本，移除不相关内容
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            # 如果是特殊占位符行，直接保留
            if (line.strip().startswith('__CODE_BLOCK_') or 
                line.strip().startswith('__HEADER_') or 
                line.strip().startswith('__LIST_ITEM_') or
                line.strip().startswith('__TABLE_')):
                filtered_lines.append(line)
                continue
                
            # 检查是否包含不相关内容
            keep_line = True
            for pattern in self.config['irrelevance_patterns']:
                if re.search(pattern, line, re.IGNORECASE):
                    keep_line = False
                    break
                    
            if keep_line:
                filtered_lines.append(line)
                
        # 重新组合文本
        text = '\n'.join(filtered_lines)
        
        # 处理特殊元素中的图片链接
        if self.config['keep_tables'] or self.config['keep_list_format']:
            self._process_special_elements_content(tables, list_items)
        
        # 恢复特殊元素
        # 图片链接不需要恢复
            
        for placeholder, list_item in list_items.items():
            text = text.replace(placeholder, list_item)
            
        for placeholder, header in headers.items():
            text = text.replace(placeholder, header)
            
        for placeholder, table in tables.items():
            text = text.replace(placeholder, table)
            
        for placeholder, code in code_blocks.items():
            text = text.replace(placeholder, code)
            
        return text

    def clean_content(self, text: str) -> str:
        """
        清洗内容
        """
        text = self.clean_text(text)
        text = self.remove_duplicates(text)
        text = self.remove_irrelevant_content(text)
        return text
