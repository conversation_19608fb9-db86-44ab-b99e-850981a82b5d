# -*- coding: utf-8 -*-
import os
from typing import List, Optional, Dict, Any
from urllib.parse import quote_plus
from pathlib import Path

from loguru import logger

class Settings:
    '''
    应用配置类，包含所有系统配置项
    '''
    # 项目路径
    ROOT_PATH: str = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # API配置
    API_V1_STR: str = '/api/v1'
    PROJECT_NAME: str = 'NKRA'
    
    # LLM服务配置
    LLM_SERVICE: str = 'siliconflow'
    OLLAMA_MODEL: str = 'deepseek-r1:1.5b'
    OLLAMA_BASE_URL: str = 'http://localhost:11434'
    LLM_REQUEST_TIMEOUT: float = 60.0  # LLM请求超时时间（秒）
    LLM_STREAM_TIMEOUT: float = 120.0  # 流式请求超时时间（秒）
    LLM_MAX_TOKENS: int = 8000  # 默认LLM最大上下文窗口大小，使用相对保守的值
    
    # 不同模型的最大上下文窗口大小映射
    LLM_MODEL_MAX_TOKENS: Dict[str, int] = {
        # SiliconFlow 模型
        'Qwen/QwQ-32B': 32000,
        'Qwen/Qwen2-7B-Instruct': 4000,
        # Ollama 模型
        'deepseek-r1:1.5b': 4000,
        'qwen:7b': 8000,
        'qwen2:7b': 4000
    }
    
    # SiliconFlow配置
    SILICONFLOW_API_KEY: str = os.environ.get('SILICONFLOW_API_KEY', '')
    SILICONFLOW_MODEL: str = os.environ.get('SILICONFLOW_MODEL', 'Qwen/QwQ-32B')
    SILICONFLOW_BASE_URL: str = os.environ.get('SILICONFLOW_BASE_URL', 'https://api.siliconflow.cn')
    
    # 向量模型配置
    EMBEDDING_MODEL: str = os.environ.get('EMBEDDING_MODEL_PATH', 'BAAI/bge-m3')
    EMBEDDING_DEVICE: str = 'cpu'
    
    # 文档处理配置
    # 分块
    CHUNK_SIZE: int = 500  # 文档分块大小
    CHUNK_OVERLAP: int = 50  # 分块重叠大小
    # 内容增强
    CONTENT_ENHANCEMENT_CONFIGS = {
        'chunk_parallal_per_task': 3, # 支持分块增强的并行数量
        'generate_document_summary': True, # 是否为文档生成摘要
        'generate_chunk_queries': True, # 是否为每个分片生成查询问题
        'generate_chunk_summary': True, # 是否为每个分片生成摘要
        'generate_chunk_table_description': True, # 是否为表格分片生成描述
        'generate_chunk_code_description': True, # 是否为代码分片生成描述
        'max_queries_per_chunk': 2, # 每个分片生成的查询问题数量
        'replace_table_and_code_with_description': False # 是否将表格和代码替换为描述
    }
    # 数据清洗
    DATA_CLEANING_CONFIGS = {}

    # 检索配置
    DEFAULT_SEARCH_LIMIT: int = 5  # 默认检索数量
    MIN_RELEVANCE_SCORE: float = 0.3  # 最小相关度分数
    USE_MULTI_QUERY: bool = True  # 是否启用多查询扩展
    MULTI_QUERY_COUNT: int = 3     # 多查询变体数量
    MULTI_QUERY_CACHE_SIZE: int = 100  # 多查询缓存大小
    MULTI_QUERY_CACHE_TTL: int = 86400  # 多查询缓存过期时间(秒)

    # 混合检索配置
    USE_HYBRID_RETRIEVER: bool = True  # 是否启用混合检索
    HYBRID_VECTOR_WEIGHT: float = 0.5  # 混合检索的向量检索权重
    HYBRID_BM25_WEIGHT: float = 0.5  # 混合检索的BM25检索权重
    HYBRID_FUSION_METHOD: str = 'weighted_sum'  # 混合检索的融合方法

    # 查询变体生成配置
    VARIANT_LLM_SERVICE: Optional[str] = None  # 变体生成专用LLM服务，None表示使用主LLM服务
    VARIANT_LLM_MODEL: Optional[str] = None    # 变体生成专用模型，None表示使用主LLM模型

    # macOS 特定配置
    MACOS_FORCE_SEQUENTIAL: bool = True  # 在 macOS 上强制使用序列化执行，避免资源泄漏
    MACOS_DISABLE_STREAMING: bool = True  # 在 macOS 上禁用流式响应，避免 aiohttp session 泄漏
    MACOS_DISABLE_RELOAD: bool = True     # 在 macOS 上禁用 uvicorn 重载，避免进程资源泄漏
    
    # 数据库配置
    DB_TYPE: str = 'sqlite'
    DB_HOST: str = 'localhost'
    DB_PORT: str = '5432'
    DB_USER: str = 'postgres'
    DB_PASSWORD: str = '123456'
    DB_NAME: str = 'harmonyqa'
    DB_TEST_NAME: str = 'harmonyqa_test'
    SQL_ECHO: bool = False  # SQL查询回显
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ['*']
    
    # 日志配置
    LOG_LEVEL: str = 'DEBUG'
    LOG_DIR: str = os.path.join(ROOT_PATH, 'logs')
    
    # Elasticsearch配置
    ELASTICSEARCH: Dict[str, Any] = {
        'HOSTS': ['http://localhost:9200'],
        'INDEX_NAME': 'nkra_knowledge',
        'TIMEOUT': 60,
        'MAX_RETRIES': 3,
        'USE_SSL': False,
        'VERIFY_CERTS': False
    }
    
    def __init__(self):
        '''
        初始化配置 - 根据环境变量设置数据目录和相关路径
        '''
        # 注意：配置加载现在由config_loader.py的load_configurations()统一处理
        # 这里只做基本的路径设置，避免重复加载
        pass

    def _setup_database_uri(self):
        '''
        设置数据和数据库连接URI
        '''
        env = os.environ.get('NKRA_ENV', '')
        if 'eval' in env.lower():
            # 评估环境使用独立的存储路径和索引
            self.DATA_DIR = os.path.join(self.ROOT_PATH, 'data_eval')
            self.ELASTICSEARCH['INDEX_NAME'] = 'nkra_evaluation'
        else:
            self.DATA_DIR = os.path.join(self.ROOT_PATH, 'data')
            # 生产环境使用默认索引名称
            self.ELASTICSEARCH['INDEX_NAME'] = 'nkra_knowledge'
            
        # 设置基于DATA_DIR的路径
        self.SQLITE_DB_FILE = os.path.join(self.DATA_DIR, 'nkra.db')
        self.SQLITE_TEST_DB_FILE = os.path.join(self.DATA_DIR, 'nkra_test.db')
        self.VECTOR_DB_PATH = os.path.join(self.DATA_DIR, 'vectors')
        self.KNOWLEDGE_BASE_PATH = os.path.join(self.DATA_DIR, 'knowledge_base')
        logger.debug(f'数据目录: {self.DATA_DIR}')
        logger.debug(f'Elasticsearch索引: {self.ELASTICSEARCH["INDEX_NAME"]}')
        logger.debug(f'SQLite数据库文件: {self.SQLITE_DB_FILE}')
        logger.debug(f'SQLite测试数据库文件: {self.SQLITE_TEST_DB_FILE}')
        logger.debug(f'向量数据库路径: {self.VECTOR_DB_PATH}')
        logger.debug(f'知识库路径: {self.KNOWLEDGE_BASE_PATH}')
        
        if self.DB_TYPE == 'sqlite':
            self.SQLALCHEMY_DATABASE_URI = f'sqlite:///{self.SQLITE_DB_FILE}'
            logger.debug(f'SQLite数据库URI: {self.SQLALCHEMY_DATABASE_URI}')
        else:
            password = quote_plus(self.DB_PASSWORD)
            self.SQLALCHEMY_DATABASE_URI = (
                f'postgresql://{self.DB_USER}:{password}@'
                f'{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}'
            )
            logger.debug(f'PostgreSQL数据库URI: {self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}')
    
    def _ensure_directories(self):
        '''
        确保必要的目录存在
        '''
        os.makedirs(self.LOG_DIR, exist_ok=True)
        os.makedirs(self.DATA_DIR, exist_ok=True)
        os.makedirs(self.VECTOR_DB_PATH, exist_ok=True)
        os.makedirs(self.KNOWLEDGE_BASE_PATH, exist_ok=True)
    
    def get_config(self) -> Dict[str, Any]:
        '''
        获取所有配置项
        '''
        config = {}
        for key in dir(self):
            # 排除私有属性和方法
            if not key.startswith('_') and not callable(getattr(self, key)):
                config[key] = getattr(self, key)
        return config

# 创建全局settings实例
settings = Settings() 