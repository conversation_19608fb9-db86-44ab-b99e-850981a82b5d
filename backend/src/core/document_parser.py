from abc import ABC, abstractmethod
from datetime import datetime
import json
from pathlib import Path
import shutil
import time
from typing import Dict, Any, List, Optional, Union
import os
import uuid
from zipfile import ZipFile
from .logging import get_module_logger
import aiofiles
import asyncio
import re
from concurrent.futures import ThreadPoolExecutor

import mammoth
import pypandoc

from ..core.exceptions import ProcessingError
from .content_converter import DocConverter
from .config import settings

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class DocumentParser(ABC):
    """文档解析器基类，用于解析不同类型的文档内容"""

    def __init__(self):
        self._executor = ThreadPoolExecutor(max_workers=4)
        self.converter = DocConverter()

    @abstractmethod
    async def parse(self, file_path: str) -> Dict[str, Any]:
        """解析指定路径的文档文件
        
        Args:
            file_path: 待解析的文档文件路径
            
        Returns:
            包含文档标题、内容等信息的字典，统一JSON格式
        """
        pass

    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """获取解析器支持的文件扩展名列表
        
        Returns:
            支持的文件扩展名列表，如 ['.txt', '.md', '.pdf']
        """
        pass

    async def _get_file_size(self, file_path: str) -> int:
        """异步获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, os.path.getsize, file_path)
    
    def _convert_path_to_url(self, file_path: str) -> str:
        """将文件系统路径转换为Web URL
        
        Args:
            file_path: 文件系统路径
            
        Returns:
            Web URL
        """
        # 获取相对于DATA_DIR的路径
        base_dir = Path(settings.DATA_DIR)
        try:
            rel_path = os.path.relpath(file_path, base_dir)
            # 确保使用正斜杠（适用于Web URL）
            rel_path = rel_path.replace('\\', '/')
            # 构造URL（基于配置的静态资源URL前缀）
            url = f'/api/static/{rel_path}'
            return url
        except ValueError:
            # 如果路径不在base_dir内，返回一个安全的占位符
            logger.warning(f'无法为路径创建相对URL: {file_path}')
            return '#'
        
    def get_zip_dir(self, file_path: str) -> Path:
        """获取ZIP解压目录
        
        Args:
            file_path: 文件路径
            
        """
        date_str = time.strftime('%Y%m%d')
        # 确定保存目录
        base_dir = Path(settings.DATA_DIR)
            
        # ZIP解压目录：/temp_zip/<date_str>/<file_name>
        zip_dir = base_dir / 'temp_zip' / date_str / os.path.basename(file_path)
        zip_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f'ZIP解压目录: {zip_dir}')
        return zip_dir
    
    def get_assets_dir(self, file_path: str) -> Path:
        """获取图片资源目录
        
        Args:
            file_path: 文件路径
            
        """
        date_str = time.strftime('%Y%m%d')
        # 确定保存目录
        base_dir = Path(settings.DATA_DIR)
            
        # 图片资源目录：/temp_images/<date_str>/<file_name>
        assets_dir = base_dir / 'temp_images' / date_str
        assets_dir.mkdir(parents=True, exist_ok=True)
        return assets_dir
    
    async def _create_standardized_output(
        self, 
        title: str, 
        content: str, 
        file_path: str,
        metadata: Optional[Dict[str, Any]] = None,
        url: Optional[str] = None,
        assets: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """创建标准化的输出JSON
        
        Args:
            title: 文档标题
            content: Markdown格式的内容
            file_path: 文件路径
            url: 文档URL（可选）
            
        Returns:
            标准化的JSON格式
        """
        
        if not metadata:
            metadata = {}

        metadata.update({
            'file_name': os.path.basename(file_path),
            'file_size': await self._get_file_size(file_path)
        })

        # 没有docId的就增加docId
        if not metadata.get('docId', ''):
            metadata['docId'] = 'doc'+str(uuid.uuid4())

        # 构建输出
        output = {
            'title': title,
            'content': content,
            'metadata': metadata
        }
        
        # 添加URL（如果有）
        if url:
            output['url'] = url
            
        # 添加分类（如果元数据中有category字段）
        if 'category' in metadata:
            output['category'] = metadata['category']
            
        # 添加图片资源（如果有），将文件路径转换为URL
        if assets:
            output['assets'] = assets
            
        return output

    def _extract_title(self, file_path):
        title = os.path.splitext(os.path.basename(file_path))[0]

        # 如果文件命名方式是'%Y%m%d%H%M%S_文件名'，则从文件名中提取标题
        if re.match(r'^\d{14}_', title):
            title = title[15:]
        
        return title


class TextParser(DocumentParser):
    """文本文件解析器"""

    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()

            title = self._extract_title(file_path)
            
            # 文本内容已经是纯文本，不需要格式转换，但为了统一，我们将其视为Markdown格式
            
            # 标准化输出
            return await self._create_standardized_output(title, content, file_path)
        except Exception as e:
            logger.error(f'解析文本文件失败: {str(e)}')
            raise

    def get_supported_extensions(self) -> List[str]:
        return ['.txt', '.md', '.json']


class MarkdownParser(DocumentParser):
    """Markdown文件解析器"""

    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()

            title = self._extract_title(file_path)
            
            # 标准化输出
            return await self._create_standardized_output(title, content, file_path)
        except Exception as e:
            logger.error(f'解析Markdown文件失败: {str(e)}')
            raise

    def get_supported_extensions(self) -> List[str]:
        return ['.md', '.markdown']


class HTMLParser(DocumentParser):
    """HTML文件解析器，将HTML转换为Markdown"""
    
    def __init__(self):
        super().__init__()
        
    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                html_content = await f.read()
                
            # 使用文件名作为标题
            title = os.path.splitext(os.path.basename(file_path))[0]
            file_size = await self._get_file_size(file_path)
            
            # 尝试从HTML中提取标题
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                title_tag = soup.find('title')
                if title_tag and title_tag.string:
                    title = title_tag.string.strip()
            except Exception as e:
                logger.warning(f'从HTML提取标题失败: {str(e)}')
            
            # 将HTML转换为Markdown格式
            markdown_content = self.converter.to_markdown(html_content, 'html')
            
            # 标准化输出
            return await self._create_standardized_output(title, markdown_content, file_path)
        except Exception as e:
            logger.error(f'解析HTML文件失败: {str(e)}')
            raise
            
    def get_supported_extensions(self) -> List[str]:
        return ['.html', '.htm']

class PandocParser(DocumentParser):
    """PanDoc文件解析器，暂不启用"""
    
    def __init__(self):
        super().__init__()

    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            img_base_dir = self.get_assets_dir(file_path)

            # 使用pypandoc将文档直接转换为markdown
            markdown_content = pypandoc.convert_file(
                file_path,
                to='markdown',
                extra_args=[
                    '--markdown-headings=atx',
                    '--wrap=none',
                    '--extract-media='+str(img_base_dir)
                ]
            )
            
            # 日志输出转换后的内容前100个字符（用于调试）
            logger.debug(f'markdown_content前100个字符: {markdown_content[:100]}')
            
            # 收集图片资源列表
            image_assets = []
            if os.path.exists(img_base_dir):
                for root, _, files in os.walk(img_base_dir):
                    for file in files:
                        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                            # 保存完整路径
                            full_img_path = os.path.join(root, file)
                            web_url = self._convert_path_to_url(full_img_path)
                            image_assets.append({'path': full_img_path, 'url': web_url})

            # 使用正则表达式替换markdown中的图片路径，适应pandoc生成的格式，包括可能的宽度和高度属性
            # 匹配模式：![alt text](file_path){可选属性}
            IMG_PATTERN = re.compile(r'!\[(.*?)\]\((.*?)\)(\{.*?\})?')
            
            def replace_image_path(match):
                alt_text = match.group(1)
                img_path = match.group(2)
                attributes = match.group(3) or ''
                
                # 找到图片资源
                for asset in image_assets:
                    if os.path.basename(img_path) in asset['path'] or img_path in asset['path'] or asset['path'] in img_path:
                        web_url = asset['url']
                        return f'![{alt_text}]({web_url}){attributes}'
                
                # 如果找不到匹配的资产，保持原样
                logger.warning(f'无法为图片创建URL: {img_path}')
                return match.group(0)  # 保持原样
            
            # 应用替换
            markdown_content = IMG_PATTERN.sub(replace_image_path, markdown_content)
            
            title = self._extract_title(file_path)
            # 标准化输出
            return await self._create_standardized_output(title, markdown_content, file_path, assets=image_assets)
        except Exception as e:
            logger.error(f'解析文件{file_path}失败: {str(e)}')
            raise
        
    def get_supported_extensions(self) -> List[str]:
        return []


class ZipParser(DocumentParser):
    """Zip文件解析器，将Zip文件解压，图片保存在assets_dir"""
    def __init__(self):
        super().__init__()
    
    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            # 创建日期格式（yyyymmdd）
            date_str = time.strftime('%Y%m%d')
            
            # 确定保存目录
            base_dir = Path(settings.DATA_DIR)
                
            # ZIP解压目录：/temp_zip/<date_str>
            zip_dir = self.get_zip_dir(file_path)
            logger.debug(f'ZIP解压目录: {zip_dir}')
            
            # 安全解压缩文件
            work_dir = self._secure_extract(file_path, str(zip_dir))
            
            # 找到并处理Markdown文件
            md_files = list(Path(work_dir).rglob('*.md'))
            if not md_files:
                raise ProcessingError('在ZIP文件中未找到Markdown文件')
                
            # 使用第一个找到的md文件
            md_path = md_files[0]
            
            # 标准化处理markdown和图片
            img_dir = self.get_assets_dir(file_path)
            
            logger.debug(f'标准化处理markdown和图片, markdown文件地址(zip包内):{md_path}, 目标图片目录地址(temp_images):{img_dir}')
            markdown_content, image_assets = self._normalize_markdown(work_dir, md_path, str(img_dir))
            
            # 删掉临时文件
            shutil.rmtree(zip_dir, ignore_errors=True)

            title = self._extract_title(file_path)
            
            # 标准化输出
            return await self._create_standardized_output(title, markdown_content, file_path, assets=image_assets)
        except Exception as e:
            logger.error(f'解析ZIP文件失败: {str(e)}')
            raise
    
    def _secure_extract(self, zip_path: str, output_dir: str) -> str:
        """安全解压到指定目录，并返回工作目录路径"""
        work_dir = os.path.join(output_dir, os.path.basename(zip_path))
        os.makedirs(work_dir, exist_ok=True)

        with ZipFile(zip_path) as zf:
            # 拒绝目录穿越
            for member in zf.namelist():
                if '..' in os.path.normpath(member).split(os.sep):
                    raise ValueError(f'unsafe path in zip: {member}')
            zf.extractall(work_dir)

        return work_dir

    def _normalize_markdown(self, work_dir: str, md_path: str, assets_dir: str) -> tuple:
        """集中图片到指定目录并更新Markdown引用，返回更新后的内容和图片资源列表"""
        # 确保输出目录存在
        os.makedirs(assets_dir, exist_ok=True)
        
        # 图片链接正则表达式 - 同时捕获图片说明文字和路径
        IMG_PATTERN = re.compile(r'!\[(.*?)\]\(([^)]+)\)')
        
        # 读取markdown内容
        with open(md_path, 'r', encoding='utf-8') as f:
            text = f.read()
        
        # 收集和处理图片资源
        image_assets = []
        
        def _process_image(match):
            alt_text = match.group(1)  # 图片的替代文本
            orig_path = match.group(2)  # 原始路径
            
            # 提取文件名
            img_filename = os.path.basename(orig_path)
            
            # 查找原图片文件
            src_candidates = list(Path(work_dir).rglob(img_filename))
            if src_candidates:
                # 新图片路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                new_img_path = os.path.join(assets_dir, f'{timestamp}_{img_filename}')
                
                # 复制图片到输出目录
                shutil.copy2(src_candidates[0], new_img_path)
                
                web_url = self._convert_path_to_url(new_img_path)
                image_assets.append({'path': new_img_path, 'url': web_url})
                
                # 返回更新后的图片引用，使用Web URL
                return f'![{alt_text}]({web_url})'
            
            # 如果找不到图片，保持原引用不变
            return match.group(0)
        
        # 替换图片链接
        updated_text = IMG_PATTERN.sub(_process_image, text)
        
        return updated_text, image_assets

    def get_supported_extensions(self) -> List[str]:
        return ['.zip']
    
class JSONParser(DocumentParser):
    """JSON文件解析器，将JSON内部内容转换为Markdown"""
    
    def __init__(self):
        super().__init__()
        self.converter = DocConverter()
        
    async def parse(self, file_path: str) -> Dict[str, Any]:
        try:
            # 读取json文件，确保使用UTF-8编码
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
            # 将json内部内容转换为markdown
            markdown_content = self.converter.doc_content_to_markdown(data)

            data['content'] = markdown_content
            if 'title' not in data:
                raise ProcessingError('JSON文件中没有标题')
            if 'url' not in data and file_path not in data:
                raise ProcessingError('JSON文件中没有URL和文件路径')
            return data
        except UnicodeDecodeError as e:
            logger.error(f'JSON文件解码失败，可能是编码问题: {str(e)}')
            raise ProcessingError(f'JSON文件解码失败，请确保文件是UTF-8编码: {str(e)}')
        except json.JSONDecodeError as e:
            logger.error(f'JSON解析失败: {str(e)}')
            raise ProcessingError(f'JSON格式错误: {str(e)}')
        except Exception as e:
            logger.error(f'解析JSON文件失败: {str(e)}')
            raise
            
    def get_supported_extensions(self) -> List[str]:
        return ['.json']


class MammothParser(DocumentParser):
    """使用mammoth库的解析器，将文件转换为Markdown，并保存图片到文件系统"""
    
    def __init__(self):
        super().__init__()
    
    async def parse(self, file_path: str) -> dict:
        """解析文件，将其转换为Markdown格式
        
        Args:
            file_path: 待解析的文件路径
            
        Returns:
            包含文档标题、内容等信息的字典，统一JSON格式
        """
        try:
            # 图片处理函数
            image_assets = []
            
            def handle_image(image: mammoth.documents.Image):
                # 为图片生成唯一文件名
                image_filename = f'image_{uuid.uuid4().hex}.{image.content_type.split("/")[-1]}'
                image_path = str(self.get_assets_dir(file_path) / image_filename)
                
                # 将图片保存到文件
                with open(image_path, 'wb') as f:
                    with image.open() as image_bytes:
                        f.write(image_bytes.read())
                
                # 记录图片资产
                web_url = self._convert_path_to_url(image_path)
                image_assets.append({'path': image_path, 'url': web_url})
                
                # 返回图片HTML标签
                return {'src': web_url}
            
            # 设置mammoth转换选项
            convert_image = mammoth.images.img_element(handle_image)

            # 异步执行mammoth转换
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self._executor, 
                                              lambda: mammoth.convert_to_html(
                                                  file_path, convert_image=convert_image
                                              ))
            
            html_content = result.value
            
            # 将HTML转换为Markdown
            markdown_content = self.converter.to_markdown(html_content, 'html')
            
            # 提取标题
            title = self._extract_title(file_path)
            
            # 标准化输出
            return await self._create_standardized_output(title, markdown_content, file_path, assets=image_assets)
        except Exception as e:
            logger.error(f'MammothParser 解析文件{file_path}失败: {str(e)}')
            raise
    
    def get_supported_extensions(self) -> list:
        """获取支持的文件扩展名列表"""
        return ['.docx', '.doc']
    
# 添加新的 MarkitdownParser 解析器
class MarkitdownParser(DocumentParser):
    """使用 markitdown 库的通用文档解析器，支持多种文件格式"""
    
    def __init__(self):
        super().__init__()
        # 配置 markitdown 实例
        from markitdown import MarkItDown
        self.md = MarkItDown()
        
    async def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文档文件，使用 markitdown 转换为 Markdown 格式
        
        Args:
            file_path: 待解析的文档文件路径
            
        Returns:
            包含文档标题、内容等信息的字典，统一JSON格式
        """
        try:
            # 使用 markitdown 转换文件
            logger.debug(f'使用 markitdown 转换文件: {file_path}')
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self._executor, 
                                               lambda: self.md.convert(file_path))
            
            # 获取转换后的 Markdown 内容
            markdown_content = result.text_content
            logger.debug(f'转换后的 Markdown 内容: {markdown_content[:100]}')

            # 提取标题
            title = self._extract_title(file_path)

            # 标准化输出
            return await self._create_standardized_output(title, markdown_content, file_path)
        except Exception as e:
            logger.error(f'MarkitdownParser 解析文件失败: {str(e)}')
            raise ProcessingError(f'使用 markitdown 解析文件失败: {str(e)}')
    
    def get_supported_extensions(self) -> List[str]:
        """获取 markitdown 支持的文件扩展名列表"""
        return [
            # 文本和标记文件
            # '.md', '.markdown', '.txt', '.html', '.htm',
            
            # Office 文档
            # '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls',
            
            # PDF 文件
            '.pdf',
            
            # 图片文件
            # '.jpg', '.jpeg', '.png', '.gif', '.webp',
            
            # 数据和格式化文件
            # '.xml', '.csv',
            
            # 压缩文件
            # '.epub'
        ]

class ParserFactory:
    """解析器工厂类"""

    _parsers: Dict[str, DocumentParser] = {}

    @classmethod
    def register_parser(cls, parser: DocumentParser):
        """注册解析器"""
        for ext in parser.get_supported_extensions():
            cls._parsers[ext.lower()] = parser

    @classmethod
    def get_parser(cls, file_path: str) -> Optional[DocumentParser]:
        """获取适用的解析器"""
        ext = os.path.splitext(file_path)[1].lower()
        return cls._parsers.get(ext)

    @classmethod
    async def async_parse(cls, file_path: str) -> Dict[str, Any]:
        """
        异步解析文档，输出统一的JSON格式，内容为Markdown格式
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            标准化的JSON格式，包含文档标题、内容、目录结构等
            
        Raises:
            ProcessingError: 如果文件类型不支持或解析失败
        """
        parser = cls.get_parser(file_path)
        if not parser:
            raise ProcessingError(f'不支持的文件类型: {file_path}')
        return await parser.parse(file_path)


# 注册默认解析器
ParserFactory.register_parser(TextParser())  # 纯文本解析器
ParserFactory.register_parser(MarkdownParser())  # Markdown解析器
ParserFactory.register_parser(HTMLParser())  # 自定义HTML解析器
# ParserFactory.register_parser(PandocParser())  # 暂不启用
ParserFactory.register_parser(ZipParser())  # zip解析器，支持只包含一个markdown的zip包解析
ParserFactory.register_parser(MammothParser())  # mammoth解析器，支持docx、doc文件的解析
# ParserFactory.register_parser(MarkitdownParser())  # markitdown解析器，支持图片识别 暂不启用

ParserFactory.register_parser(JSONParser())  # json解析器（特殊Json文件）
