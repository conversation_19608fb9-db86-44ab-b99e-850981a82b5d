"""
Elasticsearch服务 - 提供基于BM25的关键词检索功能
"""

import asyncio
from typing import List, Dict, Any, Optional
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError

from sqlalchemy import select

from ..core.logging import get_module_logger
from ..core.id_generator import IDGenerator

from .constants import KnowledgeDomain
from .config import settings
from ..models.knowledge import Knowledge, KnowledgeChunk

from ..core.database import async_session_factory

logger = get_module_logger(__name__)

class ElasticSearch:
    """Elasticsearch 服务类（单例 + 上下文管理器）"""

    # ---- 单例相关 ----
    _instance: 'ElasticSearch | None' = None  # 唯一实例
    _client: Optional[AsyncElasticsearch] = None  # 全局共享客户端

    def __new__(cls, index_name: str = None):
        """如果指定了自定义索引名，创建新实例；否则返回单例"""
        if index_name and index_name != settings.ELASTICSEARCH['INDEX_NAME']:
            # 自定义索引名，创建新实例
            return super().__new__(cls)
        else:
            # 默认索引名，使用单例
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

    def __init__(self, index_name: str = None):
        """初始化（可能会被多次调用，所以要防止重复初始化）"""
        self.index_name = index_name or settings.ELASTICSEARCH['INDEX_NAME']
        
        # 对于自定义索引，总是重新初始化
        if index_name and index_name != settings.ELASTICSEARCH['INDEX_NAME']:
            self._initialize_client()
            return
            
        # 对于默认索引，检查是否已初始化
        if getattr(self, '_initialized', False):
            # 已完成初始化，直接复用
            self.client = self.__class__._client
            return

        self._initialize_client()
        # 标记已完成初始化，后续重复构造不会再初始化
        self._initialized = True
        
    def _initialize_client(self):
        """真正创建 AsyncElasticsearch 客户端（只创建一次）"""
        if self.__class__._client is not None:
            # 已经创建过，直接引用
            self.client = self.__class__._client
            return

        try:
            # Elasticsearch Python 客户端 9.x 连接 8.x 服务器兼容配置
            client = AsyncElasticsearch(
                hosts=settings.ELASTICSEARCH['HOSTS'],
                request_timeout=settings.ELASTICSEARCH['TIMEOUT'],
                retry_on_timeout=True,
                verify_certs=settings.ELASTICSEARCH['VERIFY_CERTS'],
                # 兼容 8.x Server
                headers={'Accept': 'application/vnd.elasticsearch+json; compatible-with=8'},
            )
            self.__class__._client = client
            self.client = client
            logger.info(
                f'[Elasticsearch] 客户端初始化成功，连接到: {settings.ELASTICSEARCH["HOSTS"]}'
            )
        except Exception as e:
            logger.error(f'[Elasticsearch] 客户端初始化失败: {str(e)}')
            raise
            
    async def ensure_index_exists(self):
        """确保索引存在，如果不存在则创建"""
        try:
            # 检查索引是否存在
            exists = await self.client.indices.exists(index=self.index_name)
            
            if not exists:
                # 创建索引映射 - 针对新的ID系统和字段结构优化
                mapping = {
                    'mappings': {
                        'properties': {
                            'id': {'type': 'keyword'},  # 统一的文档ID
                            'knowledge_id': {'type': 'keyword'},  # 知识条目ID（文档和分片都有）
                            'type': {'type': 'keyword'},  # 文档类型：document 或 chunk
                            'title': {
                                'type': 'text',
                                'analyzer': 'standard',
                                'search_analyzer': 'standard',
                                'fields': {
                                    'keyword': {'type': 'keyword'}  # 精确匹配
                                }
                            },
                            'content': {
                                'type': 'text',
                                'analyzer': 'standard',
                                'search_analyzer': 'standard'
                            },
                            'summary': {
                                'type': 'text',
                                'analyzer': 'standard'
                            },
                            'headers': {
                                'type': 'text',
                                'analyzer': 'standard',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'domain': {
                                'type': 'keyword',
                                'fields': {
                                    'text': {'type': 'text'}
                                }
                            },
                            'category_path': {
                                'type': 'text',
                                'analyzer': 'standard',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            }
                        }
                    },
                    'settings': {
                        'number_of_shards': 1,
                        'number_of_replicas': 0,
                        'analysis': {
                            'analyzer': {
                                'code_analyzer': {
                                    'type': 'custom',
                                    'tokenizer': 'keyword',
                                    'filter': ['lowercase']
                                }
                            }
                        },
                        # 针对API文档优化BM25参数
                        'similarity': {
                            'api_bm25': {
                                'type': 'BM25',
                                'k1': 1.5,  # 提高词频权重
                                'b': 0.75   # 减少文档长度的影响
                            }
                        }
                    }
                }
                
                await self.client.indices.create(index=self.index_name, **mapping)
                logger.info(f'[Elasticsearch]创建索引成功: {self.index_name}')
            else:
                logger.info(f'[Elasticsearch]索引已存在: {self.index_name}')
                
        except Exception as e:
            logger.error(f'[Elasticsearch]创建索引失败: {str(e)}')
            raise
            
    async def index_knowledge_and_chunks(self, knowledge: Knowledge, knowledge_chunks: List[KnowledgeChunk]) -> bool:
        """将知识片段批量追加索引到Elasticsearch"""
        # 确保Elasticsearch索引存在
        await self.ensure_index_exists()
        
        try:
            bulk_data = []
            # 整条知识条目索引数据
            doc_data = {
                'id': knowledge.id,  # 统一的文档ID
                'knowledge_id': knowledge.id,
                'type': 'main',  # 明确标记为文档类型
                'title': knowledge.title or '',
                'content': knowledge.content or '',
                'summary': knowledge.meta_info.get('summary', ''),
                'domain': knowledge.domain or '',
                'category_path': knowledge.meta_info.get('path', []),  # 保持数组格式
            }
            
            bulk_data.append({
                'index': {
                    '_index': self.index_name,
                    '_id': knowledge.id  # 直接使用knowledge_id，不添加前缀
                }
            })
            bulk_data.append(doc_data)
            
            # 准备批量分片索引数据
            for knowledge_chunk in knowledge_chunks:
                chunk_metadata = knowledge_chunk.meta_info or {}
                
                chunk_data = {
                    'id': knowledge_chunk.id,
                    'knowledge_id': knowledge.id,
                    'type': 'chunk',
                    'content': knowledge_chunk.content or '',
                    'domain':  knowledge.domain or '',
                    'summary': chunk_metadata.get('summary', ''),
                    'headers': chunk_metadata.get('headers', []),
                    'category_path': chunk_metadata.get('path', [])
                }
                
                bulk_data.append({
                    'index': {
                        '_index': self.index_name,
                        '_id': knowledge_chunk.id  # 使用chunk的独立ID
                    }
                })
                bulk_data.append(chunk_data)
            
            await self.client.bulk(
                index=self.index_name,
                operations=bulk_data
            )
            logger.debug(f'[Elasticsearch]知识片段批量索引成功')
            
            # 刷新索引
            await self.refresh_index()
            return True
        except Exception as e:
            logger.error(f'[Elasticsearch]知识片段索引失败: error={str(e)}')
            return False
        
    
    async def sync_to_elastic_search(self, batch_size: int = 200):
        """
        核心逻辑：分页读取知识条目并写入 ES。
        """
        # 确认索引已创建
        def _chunk_order(chunk: KnowledgeChunk) -> int:
            """
            返回 chunk 的排序索引，优先使用 meta_info 中的 chunk_index。
            """
            if isinstance(chunk.meta_info, dict) and 'chunk_index' in chunk.meta_info:
                try:
                    return int(chunk.meta_info['chunk_index'])
                except (ValueError, TypeError):
                    pass
            # 回退：从 chunk_id 中解析
            idx = IDGenerator.get_chunk_index_from_chunk_id(chunk.id)
            return idx if idx is not None else 0
        
        await self.ensure_index_exists()
        async with async_session_factory() as db:
            page = 1
            total_synced = 0
            while True:
                page_data = await db.execute(
                    select(Knowledge).offset((page - 1) * batch_size).limit(batch_size)
                )
                knowledge_items = page_data.scalars().all()
                if not knowledge_items:
                    break
                for item in knowledge_items:
                    # ORM 获取完整对象
                    knowledge: Knowledge = await db.get(Knowledge, item.id)
                    if not knowledge:
                        continue
                    # 获取并排序分片
                    result = await db.execute(
                        select(KnowledgeChunk).where(KnowledgeChunk.knowledge_id == knowledge.id)
                    )
                    chunk_objs = result.scalars().all()
                    chunk_objs.sort(key=_chunk_order)
                    # 写入 ES（整篇 + 分片）
                    await self.index_knowledge_and_chunks(knowledge, chunk_objs)
                    total_synced += 1
                logger.info(f'已完成第 {page} 页同步，当前累计 {total_synced} 篇文档')
                page += 1
        # 刷新索引，确保立即可查询
        await self.refresh_index()
        logger.success(f'🎉 同步完成，共写入 {total_synced} 篇文档（含分片）到 Elasticsearch')

    async def search_by_bm25(
        self,
        query: str,
        limit: int = 10,
        min_score: float = 0.0,
        domains: Optional[List[str]] = None,
        boost_title: float = 3.0, 
        boost_content: float = 1.0,
        boost_domain: float = 1.5,
        boost_headers: float = 1.5,
        boost_category_path: float = 1.5,
    ) -> List[Dict[str, Any]]:
        """使用BM25算法进行关键词检索 - 针对API文档优化"""
        try:
            # 构建查询条件 - 针对API文档优化
            must_clauses = []
            
            # 主要搜索查询
            main_query = {
                'multi_match': {
                    'query': query,
                    'fields': [
                        f'title^{boost_title}',
                        f'content^{boost_content}',
                        f'domain^{boost_domain}',
                        f'headers^{boost_headers}',
                        f'category_path^{boost_category_path}'
                    ],
                    'type': 'best_fields',
                    'operator': 'or',
                    'minimum_should_match': '30%'
                }
            }
            must_clauses.append(main_query)
            
            # 构建过滤条件
            filter_clauses = []
            
            # 领域过滤
            if domains:
                filter_clauses.append({'terms': {'domain': domains}})
            if not domains or KnowledgeDomain.API_DOCUMENTATION in domains:
                filter_clauses.append({'terms': {'type': ['chunk']}})
            
            query_body = {
                'query': {
                    'bool': {
                        'must': must_clauses,
                        'filter': filter_clauses if filter_clauses else []
                    }
                },
                'size': limit,
                'min_score': min_score,
                '_source': [
                    'id', 'knowledge_id', 'type',
                    'title', 'content', 'summary', 
                    'headers', 'domain', 'category_path',
                ],
                'highlight': {
                    'fields': {
                        'title': {
                            'fragment_size': 100,
                            'number_of_fragments': 1,
                            'pre_tags': ['<mark>'],
                            'post_tags': ['</mark>']
                        },
                        'content': {
                            'fragment_size': 200,
                            'number_of_fragments': 3,
                            'pre_tags': ['<mark>'],
                            'post_tags': ['</mark>']
                        }
                    }
                }
            }
            
            # 执行搜索
            response = await self.client.search(
                index=self.index_name,
                **query_body
            )
            
            # 处理搜索结果
            results = []
            for hit in response.get('hits', {}).get('hits', []):
                source = hit['_source']
                score = hit['_score']
                
                # 获取高亮内容
                highlight = hit.get('highlight', {})
                
                result = {
                    'id': source.get('id', hit['_id']),  # 使用ES文档ID
                    'type': source.get('type', 'main'),  # main 或 chunk
                    'domain': source.get('domain', ''),
                    'score': min(score / 10.0, 1.0),  # 将分数标准化到0-1
                }
                
                results.append(result)
            
            logger.info(f'[Elasticsearch]BM25搜索完成: query="{query}", found={len(results)}条结果')
            return results
            
        except Exception as e:
            logger.error(f'[Elasticsearch]BM25搜索失败: query="{query}", error={str(e)}')
            return []
            
    
    async def delete_knowledge(self, knowledge_id: str) -> bool:
        """删除知识条目及其所有分片
        
        新的ID系统：
        - 文档ID直接使用knowledge_id
        - 分片通过knowledge_id字段关联
        """
        try:
            deleted_count = 0
            
            # 1. 删除主文档（ID = knowledge_id）
            try:
                doc_response = await self.client.delete(
                    index=self.index_name,
                    id=knowledge_id
                )
                if doc_response.get('result') == 'deleted':
                    deleted_count += 1
                    logger.debug(f'[Elasticsearch]删除主文档成功: {knowledge_id}')
            except NotFoundError:
                logger.debug(f'[Elasticsearch]主文档不存在: {knowledge_id}')
            
            # 2. 删除所有相关分片（通过knowledge_id字段查找）
            query = {
                'query': {
                    'bool': {
                        'must': [
                            {'term': {'knowledge_id': knowledge_id}},
                            {'term': {'type': 'chunk'}}
                        ]
                    }
                }
            }
            
            chunk_response = await self.client.delete_by_query(
                index=self.index_name,
                **query
            )
            
            chunk_deleted = chunk_response.get('deleted', 0)
            deleted_count += chunk_deleted
            
            logger.info(f'[Elasticsearch]删除知识条目及分片成功: knowledge_id={knowledge_id}, 删除了{deleted_count}个文档（1个主文档 + {chunk_deleted}个分片）')
            return True
            
        except Exception as e:
            logger.error(f'[Elasticsearch]删除知识条目及分片失败: knowledge_id={knowledge_id}, error={str(e)}')
            return False
            
    async def refresh_index(self):
        """刷新索引，使最新的变更可见"""
        try:
            await self.client.indices.refresh(index=self.index_name)
            logger.debug(f'[Elasticsearch]刷新索引成功: {self.index_name}')
        except Exception as e:
            logger.error(f'[Elasticsearch]刷新索引失败: {str(e)}')
            
    async def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            stats = await self.client.indices.stats(index=self.index_name)
            total_docs = stats['indices'][self.index_name]['total']['docs']['count']
            index_size = stats['indices'][self.index_name]['total']['store']['size_in_bytes']
            
            return {
                'index_name': self.index_name,
                'total_documents': total_docs,
                'index_size_bytes': index_size,
                'index_size_mb': round(index_size / 1024 / 1024, 2)
            }
        except Exception as e:
            logger.error(f'[Elasticsearch]获取索引统计信息失败: {str(e)}')
            return {}
            
    async def close(self):
        """关闭全局 Elasticsearch 客户端（只关闭一次）"""
        if self.__class__._client is not None:
            try:
                await self.__class__._client.close()
                logger.info('[Elasticsearch] 客户端已关闭')
            finally:
                # 置空，方便下次重新初始化
                self.__class__._client = None

    # ---- 异步上下文管理器 ----

    async def __aenter__(self):
        """`async with ElasticSearch()` 时进入"""
        return self

    async def __aexit__(self, exc_type, exc, tb):
        """退出 async with 时自动关闭连接"""
        await self.close()
