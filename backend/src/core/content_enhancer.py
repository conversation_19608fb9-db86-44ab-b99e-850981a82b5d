import asyncio
from typing import Dict, Any, List, Optional

from .config import settings
from .logging import get_module_logger

from ..services.llm_service_factory import LLMServiceFactory
from ..llm.base import BaseLLMService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class ContentEnhancer:
    """内容增强器，用于对知识文档和分片进行增强
    
    主要功能：
    1. 为整篇文档生成摘要，存储到metadata中
    2. 为每个分片生成可能的问题，存储到每个分片的metadata中
    3. 为每个分片生成摘要，存储到每个分片的metadata中
    4. 为表格生成描述，便于文本检索
    5. 为代码生成描述，便于文本检索
    """
    
    def __init__(
        self,
        llm_service: Optional[BaseLLMService] = None
    ):
        """
        初始化内容增强器
        
        Args:
            llm_service: LLM服务实例，如果不提供，则自动创建
        """
        self.llm_service = llm_service or LLMServiceFactory.create_service(settings.VARIANT_LLM_SERVICE, settings.VARIANT_LLM_MODEL)
        self.config = settings.CONTENT_ENHANCEMENT_CONFIGS
        
    async def generate_document_summary(
        self, 
        title: str, 
        content: str
    ) -> str:
        """
        为整篇文档生成摘要，添加到metadata中
        
        Args:
            title: 文档标题
            content: 文档内容
            metadata: 原始元数据
            
        Returns:
            生成的摘要
        """
        if not self.config['generate_document_summary']:
            return
        
        try:
            # 构建摘要生成提示词
            prompt = self._build_document_summary_prompt(title, content)
            
            # 调用LLM生成摘要
            summary = await self.llm_service.generate(prompt)
            
            # 如果包含</think>标签,删除它之前的所有内容
            if '</think>' in summary:
                summary = summary.split('</think>')[-1]
            
            # 确保生成的内容是一个有效的摘要
            summary = summary.strip()
            if not summary:
                logger.warning(f'文档 "{title}" 摘要生成失败，生成结果为空')
                return ''
        
            logger.info(f'成功为文档 "{title}" 生成摘要')
            return summary    
        except Exception as e:
            logger.error(f'为文档 "{title}" 生成摘要时发生错误: {str(e)}')
            # 发生错误时返回原始元数据，确保不影响主流程
            return ''
    
    async def enhance_chunks(
        self, 
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        对所有分片进行内容增强
        """
        # 一次处理N个分片增强任务，并发处理
        for i in range(0, len(chunks), self.config['chunk_parallal_per_task']):
            tasks = []
            for j in range(i, min(i + self.config['chunk_parallal_per_task'], len(chunks))):
                chunk_content = chunks[j].get('content', '')
                chunk_metadata = chunks[j].get('metadata', {})
                title = chunks[j].get('title', '')
                
                chunk_context = ''
                if j > 0:
                    chunk_previous_content = chunks[j-1].get('content', '')
                    chunk_context += f'上文：{chunk_previous_content}\n\n'
                if j < len(chunks) - 1:
                    chunk_next_content = chunks[j+1].get('content', '')
                    chunk_context += f'下文：{chunk_next_content}\n\n'
                tasks.append(self.enhance_chunk(
                    chunk_content=chunk_content,
                    chunk_metadata=chunk_metadata,
                    chunk_context=chunk_context,
                    title=title
                ))
            # 使用asyncio.gather并发处理    
            await asyncio.gather(*tasks)
            
        return chunks

    async def enhance_chunk(
        self, 
        chunk_content: str, 
        chunk_metadata: Dict[str, Any],
        chunk_context: str,
        title: str
    ) -> None:
        """
        为分片增强内容，包括生成可能的问题和摘要，添加到metadata中
        
        Args:
            chunk_content: 分片内容
            chunk_metadata: 分片元数据
            chunk_context: 分片上下文
            title: 分片标题
        """
        try:
            # 生成并添加问题到元数据
            if self.config['generate_chunk_queries']:
                chunk_metadata['possible_questions'] = await self.generate_chunk_questions(chunk_content, chunk_metadata)

            chunk_type = chunk_metadata.get('chunk_type', 'text')
            para_heading = '-'.join(chunk_metadata.get('headers', []))
            logger.debug(f'para_heading: {para_heading}, chunk_type: {chunk_type}')
            # 生成表格描述
            if self.config['generate_chunk_table_description'] and chunk_type == 'table':
                chunk_metadata['chunk_summary'] = await self._generate_table_description(chunk_content, title, para_heading, chunk_context)
            
            # 生成并添加摘要到元数据
            if self.config['generate_chunk_summary'] and chunk_type not in ['table', 'code']:
                chunk_metadata['chunk_summary'] = await self.generate_chunk_summary(chunk_content, chunk_metadata)

            logger.debug(f'增强后的chunk_metadata: {chunk_metadata}')
        except Exception as e:
            logger.error(f'为分片进行内容增强时发生错误: {str(e)}')
            # 发生错误时返回原始元数据，确保不影响主流程

        return chunk_metadata

    async def generate_chunk_questions(
        self, 
        chunk_content: str, 
        chunk_metadata: Dict[str, Any]
    ) -> List[str]:
        """生成分片的问题"""
        document_title = chunk_metadata.get('title', '未知文档')

        # 如果已经有问题列表，则不重新生成
        if 'possible_questions' in chunk_metadata and chunk_metadata['possible_questions']:
            return chunk_metadata['possible_questions']
        
        # 构建问题生成提示词
        prompt = self._build_chunk_questions_prompt(document_title, chunk_content, chunk_metadata)
        
        # 调用LLM生成问题
        response = await self.llm_service.generate(prompt)
        
        # 处理生成的问题
        questions = self._parse_questions(response, self.config['max_queries_per_chunk'])
        
        if not questions:
            logger.warning(f'分片问题生成失败，生成结果无法解析: {response[:100]}...')
            return []
        
        total_chunk_index = chunk_metadata.get('chunk_index', '未知')
        logger.debug(f'成功为分片 #{total_chunk_index} 生成 {len(questions)} 个可能的问题, 问题列表: {questions}')
            
        logger.debug(f'chunk_metadata: {chunk_metadata}')
    
        # 返回问题列表
        return questions
    
    async def generate_chunk_summary(
        self,
        chunk_content: str,
        chunk_metadata: Dict[str, Any]
    ) -> str:
        """
        为分片生成摘要
        
        Args:
            chunk_content: 分片内容
            chunk_metadata: 分片元数据
            
        Returns:
            生成的摘要
        """
        # 如果已经有摘要，则不重新生成
        if 'summary' in chunk_metadata and chunk_metadata['summary']:
            return chunk_metadata['summary']
        
        document_title = chunk_metadata.get('title', '未知文档')
        
        try:
            # 构建摘要生成提示词
            prompt = self._build_chunk_summary_prompt(document_title, chunk_content, chunk_metadata)
            
            # 调用LLM生成摘要
            summary = await self.llm_service.generate(prompt)
            
            # 如果包含</think>标签，删除它之前的所有内容
            if '</think>' in summary:
                summary = summary.split('</think>')[-1]
            
            # 确保生成的内容是一个有效的摘要
            summary = summary.strip()
            if not summary:
                total_chunk_index = chunk_metadata.get('chunk_index', '未知')
                logger.warning(f'分片 #{total_chunk_index} 摘要生成失败，生成结果为空')
                return ''
            
            total_chunk_index = chunk_metadata.get('chunk_index', '未知')
            logger.debug(f'成功为分片 #{total_chunk_index} 生成摘要')
            return summary
        except Exception as e:
            total_chunk_index = chunk_metadata.get('chunk_index', '未知')
            logger.error(f'为分片 #{total_chunk_index} 生成摘要时发生错误: {str(e)}')
            return ''

    def _build_document_summary_prompt(
        self, 
        title: str, 
        content: str
    ) -> str:
        """构建文档摘要生成的提示词"""
        
        # 限制内容长度，避免超出模型上下文窗口
        max_content_length = 8000  # 根据模型上下文窗口大小调整
        truncated_content = content[:max_content_length] if len(content) > max_content_length else content
        
        prompt = f'''这是一个简单的任务，不要进行复杂的思考。请为以下文档生成一个简洁、全面的摘要，捕捉文档的主要内容和关键点。摘要应该是一段连贯的文字，长度控制在100字以内。

文档标题: {title}

文档内容:
{truncated_content}

请直接输出摘要，不要生成任何多余内容，不要包含"摘要："等前缀，也不要使用引号包裹。'''

        return prompt
    
    def _build_chunk_summary_prompt(
        self,
        chunk_title: str,
        chunk_content: str,
        metadata: Optional[Dict[str, Any]]
    ) -> str:
        """构建分片摘要生成的提示词"""
        
        para_heading = ''
        headers = metadata.get('headers', [])
        if headers and isinstance(headers, list):
            para_heading += f'{metadata["headers"][0]} '
            for header in metadata['headers'][1:]:
                para_heading += f'- {header} '
                
        prompt = f'''这是一个简单的任务，不要进行复杂的思考。请为以下内容片段生成一个简洁、全面的摘要，捕捉片段的主要内容和关键点。摘要应该是一段连贯的文字，长度控制在50字以内。

文档标题: {chunk_title}
所属段落：{para_heading}
分片内容:
{chunk_content}

请直接输出摘要，不要生成任何多余内容，不要包含"摘要："等前缀，也不要使用引号包裹。'''

        return prompt
    
    def _build_chunk_questions_prompt(
        self, 
        chunk_title: str,
        chunk_content: str,
        metadata: Optional[Dict[str, Any]]
    ) -> str:
        """构建分片问题生成的提示词"""
       
        para_heading = ''
        headers = metadata.get('headers', [])
        if headers and isinstance(headers, list):
            para_heading += f'{metadata["headers"][0]} '
            for header in metadata['headers'][1:]:
                para_heading += f'- {header} '
        prompt = f'''这是一个简单的任务，不要进行复杂的思考。请为以下内容片段生成不超过{self.config["max_queries_per_chunk"]}个可能的问题，这些问题应该是可以借助这段内容回答的。
问题应该准确反映内容中的信息，应该从不同角度和知识点出发，控制在30字以内，如果内容中没有提到/没有相关信息，则不要生成问题。

文档标题: {chunk_title}
所属段落：{para_heading}
分片内容:
{chunk_content}

请生成不超过{self.config["max_queries_per_chunk"]}个不同的问题，每个问题一行，格式为:
1. [第一个问题]
2. [第二个问题]
...

只需列出问题本身，不需要提供答案或解释，不要生成任何多余内容。'''
        return prompt
        
    def _parse_questions(self, response: str, expected_count: int) -> List[str]:
        """解析生成的问题文本，返回问题列表"""
        # 移除可能的前导和尾随文本
        response = response.strip()
        
        # 尝试按编号分割
        questions = []
        
        # 寻找类似"1. "，"1）"，"1."等模式的行
        import re
        question_lines = re.findall(r'\d+[\.\)、]\s*(.*?)(?=\n\d+[\.\)、]|$)', response, re.DOTALL)
        
        if question_lines:
            questions = [q.strip() for q in question_lines if q.strip()]
        else:
            # 如果没有找到编号问题，按行分割
            lines = [line.strip() for line in response.split('\n') if line.strip()]
            questions = [re.sub(r'^\d+[\.\)、]\s*', '', line) for line in lines]
        
        # 过滤可能的非问题行，通常问题以问号结束或有特定前缀
        questions = [q for q in questions if '?' in q or '？' in q or re.match(r'^(what|how|why|when|where|who|which|是否|如何|为什么|哪些|什么|谁|何时|何地|怎样)', q, re.IGNORECASE)]
        
        # 限制问题数量
        return questions[:expected_count] 
    
    async def _generate_table_description(
        self,
        table_content: str,
        title: str,
        para_heading: str,
        chunk_context: str
    ) -> str:
        """
        为表格生成描述
        
        Args:
            table_content: 表格内容
            context: 表格所在的上下文内容（可选）
            
        Returns:
            生成的表格描述
        """
        try:
            # 构建表格描述生成提示词
            prompt = self._build_table_description_prompt(table_content, title, para_heading, chunk_context)
            
            # 调用LLM生成描述
            description = await self.llm_service.generate(prompt)
            
            # 如果包含</think>标签，删除它之前的所有内容
            if '</think>' in description:
                description = description.split('</think>')[-1]
            
            # 确保生成的内容是有效的描述
            description = description.strip()
            if not description:
                logger.warning('表格描述生成失败，生成结果为空')
                return ''
            
            logger.debug(f'prompt: {prompt}')
            logger.debug(f'成功生成表格描述: {description}')
            return description
        except Exception as e:
            logger.error(f'为表格生成描述时发生错误: {str(e)}')
            return ''
    
    async def _generate_code_description(
        self,
        code_content: str,
        language: Optional[str] = None,
        title: str = '',
        para_heading: str = '',
        context: str = ''
    ) -> str:
        """
        为代码生成描述
        
        Args:
            code_content: 代码内容
            language: 代码语言（可选）
            title: 代码所在位置的标题（可选）
            para_heading: 代码所在位置的段落标题（可选）
            context: 代码所在位置的上下文（可选）
        Returns:
            生成的代码描述
        """
        try:
            # 构建代码描述生成提示词
            prompt = self._build_code_description_prompt(code_content, language, title, para_heading, context)
            
            # 调用LLM生成描述
            description = await self.llm_service.generate(prompt)
            
            # 如果包含</think>标签，删除它之前的所有内容
            if '</think>' in description:
                description = description.split('</think>')[-1]
            
            # 确保生成的内容是有效的描述
            description = description.strip()
            if not description:
                logger.warning('代码描述生成失败，生成结果为空')
                return ''
            
            logger.debug(f'prompt: {prompt}')
            logger.debug(f'成功生成代码描述: {description}')
            return description
        except Exception as e:
            logger.error(f'为代码生成描述时发生错误: {str(e)}')
            return ''
    
    def _build_table_description_prompt(
        self,
        table_content: str,
        title: str,
        para_heading: str,
        chunk_context: str
    ) -> str:
        """构建表格描述生成的提示词"""
        
        prompt = f'''这是一个简单的任务，不要进行复杂的思考。请将以下表格进行线性化，并添加必要的文字描述，以下是输出的例子：
<example>
xx.xxx方法是一个可以用于XXXX...，位于xxx.xxxx包，其入/出参包含：
param1 [int]: 参数1，是用于...
param2 [char]: 参数2，描述了...，范围是
</example>
<example>
xxx.h文件中提供用于xx功能的函数，包含：
void fun1(params): 函数fun1的功能是...
int fun2(params): 函数fun2的功能是...
</example>
<example>
xxx包中包含以下文件：
- xxx.h 用于...
- xxx.c 用于...
- xxxx.h 用于...
- xxxx.c 用于...
</example>

文档标题：{title}
所在的段落标题：{para_heading}

<chunk_context>
{chunk_context}
</chunk_context>

<table_content>
{table_content}
</table_content>

请直接输出描述文本，不要包含"描述："等前缀，也不要使用引号或方括号包裹。'''

        return prompt
    
    def _build_code_description_prompt(
        self,
        code_content: str,
        language: Optional[str] = None,
        title: str = '',
        para_heading: str = '',
        chunk_context: str = ''
    ) -> str:
        """构建代码描述生成的提示词"""
        prompt = f'''这是一个简单的任务，不要进行复杂的思考。请为以下代码生成一个结构化的描述，用于帮助文本检索。描述应包含代码的功能、关键API，主要逻辑和目的，长度控制在500字以内。
输出例子：
<example>
[功能]
此代码演示...。
[关键API]
API_X: 用于...
API_Y: 用于...
[主要逻辑]
1. 导入requests库；
2. 设置API端点URL和headers；
3. 发送GET请求并检查状态码；
4. 解析返回的JSON数据。
[适用场景] 
需要批量拉取用户信息的Python环境。
</example>

代码所在位置的上下文为：
<context>
{chunk_context}
</context>

文档标题: {title}
所属段落：{para_heading}
代码语言: {language}
代码内容:
<code_content>
{code_content}
</code_content>

请直接输出结构化的描述文本，不要包含"描述："等前缀，也不要使用引号或方括号包裹。'''

        return prompt

class ContentFormater:
    @staticmethod
    def get_enhanced_document_content(title: str, content: str, metadata: Optional[dict] = None) -> str:
        """获取增强后的文档内容"""
        if title:
            content += f'\n\n标题：{title}'
        if metadata and metadata.get('summary'):
            content += f'\n摘要: {metadata["summary"]} \n\n'
            
        return content

    @staticmethod
    def get_embedding_content(chunk: Dict[str, Any]) -> str:
        '''
        获取增强后的知识块内容
        如果是代码块或者表格块，就用描述替换内容
        '''
        ret = ''
        content = chunk.get('content', '')
        metadata = chunk.get('metadata', {})
        if not metadata:
            return content
        
        if settings.CONTENT_ENHANCEMENT_CONFIGS['replace_table_and_code_with_description']:
            chunk_type = metadata.get('chunk_type', 'text')
            if chunk_type in ['table', 'code'] and metadata.get('chunk_summary'):
                logger.debug(f'分片为{chunk_type}类型，添加描述: {metadata["chunk_summary"]}.')
                ret = metadata['chunk_summary']
            else:
                ret = content
        else:
            ret = content

        context = ''
        title = ''
        if 'title' in chunk:
            title = chunk['title']
        elif 'title' in metadata:
            title = metadata['title']
        context += f'\n所属文章：{title}'

        headers = metadata.get('headers', [])
        if headers and isinstance(headers, list):
            context += f'\n段落：{metadata["headers"][0]} '
            for header in metadata['headers'][1:]:
                context += f'- {header} '

        possible_questions = metadata.get('possible_questions', [])
        if possible_questions and isinstance(possible_questions, list):
            context += '\n相关问题:'
            for i, query in enumerate(possible_questions):
                context += f'\n{i+1}. {query}'
        ret += '\n' + context
        return ret
