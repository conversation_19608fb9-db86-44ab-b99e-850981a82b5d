# -*- coding: utf-8 -*-
import uuid
import time
from typing import Optional

class IDGenerator:
    """
    统一ID生成器
    
    新的ID策略：
    1. Knowledge.id: 使用短ID格式 'k_' + 时间戳 + 随机数，例如：k_1703123456_abc123
    2. KnowledgeChunk.id: 使用父Knowledge.id + chunk序号，例如：k_1703123456_abc123_c001
    3. ES文档ID: 直接使用Knowledge.id或KnowledgeChunk.id
    4. 向量存储embedding_id: 保持原有的数字自增机制不变
    
    这样可以：
    - 通过ID前缀快速识别资源类型
    - 通过chunk ID可以快速定位父Knowledge
    - 保持ID的可读性和唯一性
    - 不破坏现有的向量存储机制
    """
    
    @staticmethod
    def generate_knowledge_id() -> str:
        """
        生成Knowledge ID
        
        格式: k_{timestamp}_{random}
        例如: k_1703123456_a1b2c3
        """
        timestamp = int(time.time())
        random_part = uuid.uuid4().hex[:6]
        return f'k_{timestamp}_{random_part}'
    
    @staticmethod
    def generate_chunk_id(knowledge_id: str, chunk_index: int) -> str:
        """
        生成KnowledgeChunk ID
        
        格式: {knowledge_id}_c{chunk_index:03d}
        例如: k_1703123456_a1b2c3_c001
        
        Args:
            knowledge_id: 父Knowledge的ID
            chunk_index: chunk在文档中的索引（从0开始）
        """
        return f'{knowledge_id}_c{chunk_index:03d}'
    
    @staticmethod
    def extract_knowledge_id_from_chunk_id(chunk_id: str) -> Optional[str]:
        """
        从chunk ID中提取Knowledge ID
        
        Args:
            chunk_id: chunk的ID，例如 k_1703123456_a1b2c3_c001
            
        Returns:
            knowledge_id: 例如 k_1703123456_a1b2c3
        """
        if '_c' in chunk_id:
            return chunk_id.rsplit('_c', 1)[0]
        return None
    
    @staticmethod
    def get_chunk_index_from_chunk_id(chunk_id: str) -> Optional[int]:
        """
        从chunk ID中提取chunk索引
        
        Args:
            chunk_id: chunk的ID，例如 k_1703123456_a1b2c3_c001
            
        Returns:
            chunk_index: 例如 1
        """
        if '_c' in chunk_id:
            try:
                chunk_part = chunk_id.rsplit('_c', 1)[1]
                return int(chunk_part)
            except (ValueError, IndexError):
                return None
        return None
    
    @staticmethod
    def is_knowledge_id(id_str: str) -> bool:
        """
        判断是否是Knowledge ID
        """
        if not id_str or not isinstance(id_str, str):
            return False
        
        if not id_str.startswith('k_') or '_c' in id_str:
            return False
            
        parts = id_str.split('_')
        if len(parts) != 3:
            return False
            
        # 验证时间戳部分是数字
        if not parts[1].isdigit():
            return False
            
        # 验证随机部分长度和格式（应该是6位十六进制字符）
        if len(parts[2]) != 6:
            return False
            
        # 验证随机部分是有效的十六进制字符
        try:
            int(parts[2], 16)
        except ValueError:
            return False
            
        return True
    
    @staticmethod
    def is_chunk_id(id_str: str) -> bool:
        """
        判断是否是Chunk ID
        """
        if not id_str or not isinstance(id_str, str):
            return False
            
        if not id_str.startswith('k_') or '_c' not in id_str:
            return False
            
        # 分离knowledge部分和chunk部分
        if '_c' not in id_str:
            return False
            
        knowledge_part, chunk_part = id_str.rsplit('_c', 1)
        
        # 验证knowledge部分
        if not IDGenerator.is_knowledge_id(knowledge_part):
            return False
            
        # 验证chunk部分是3位数字
        if not chunk_part.isdigit() or len(chunk_part) != 3:
            return False
            
        return True 