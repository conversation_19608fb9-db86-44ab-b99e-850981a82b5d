import sys
import os
import platform
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.core.database import init_db, init_db_sync
from src.core.config import settings
from src.core.logging import get_cli_logger

# 获取CLI专用logger
logger = get_cli_logger('init_db')

def ensure_directories():
    """
    确保必要的目录存在
    """
    dirs = [
        os.path.dirname(settings.SQLITE_DB_FILE),
        settings.VECTOR_DB_PATH,
        settings.KNOWLEDGE_BASE_PATH,
        'logs'
    ]
    for dir_path in dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            logger.info(f'创建目录: {dir_path}')

def main():
    """
    初始化数据库
    """
    try:
        # 确保目录存在
        ensure_directories()
        # 根据平台选择初始化方式
        logger.info(f'开始初始化数据库: {settings.SQLALCHEMY_DATABASE_URI}')
        if platform.system() == 'Windows':
            init_db_sync()
        else:
            import asyncio
            asyncio.run(init_db())
        logger.info('数据库初始化完成')
    except Exception as e:
        logger.error(f'数据库初始化失败: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main() 