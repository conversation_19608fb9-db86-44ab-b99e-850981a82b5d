# -*- coding: utf-8 -*-
import os
import sys
import json
import shutil
from pathlib import Path

import click

# 将项目根目录添加到 Python 路径，确保可以导入 src.* 包
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.cli.utils import setup_cli_logging

# 初始化日志
logger = setup_cli_logging('organize_json_files')

# Windows 不允许的文件名字符（同时也兼容 *nix 系统的保留字符）
INVALID_CHARS = r'<>:"/\\|?*'


def sanitize_name(name: str) -> str:
    """
    将非法文件/目录字符替换为下划线
    """
    translation = str.maketrans({ch: '_' for ch in INVALID_CHARS})
    return name.translate(translation).strip()


def organize_file(json_path: Path, dest_root: Path, move: bool = False) -> None:
    """
    处理单个 JSON 文件，将其放置到以 metadata.path 构成的目录中
    """
    try:
        with json_path.open('r', encoding='utf-8') as fp:
            data = json.load(fp)
    except Exception as e:
        logger.warning(f'解析失败，跳过文件 {json_path}: {e}')
        return

    path_list = (
        data.get('metadata', {}).get('path')
        if isinstance(data, dict)
        else None
    )
    if not path_list or not isinstance(path_list, list):
        logger.warning(f'文件缺少 metadata.path，跳过: {json_path}')
        return

    # 构造目标目录
    safe_parts = [sanitize_name(str(part)) for part in path_list]
    target_dir = dest_root.joinpath(*safe_parts)
    target_dir.mkdir(parents=True, exist_ok=True)

    target_path = target_dir / json_path.name
    try:
        if move:
            # 如果目标已存在则覆盖
            if target_path.exists():
                target_path.unlink()
            shutil.move(str(json_path), str(target_path))
            logger.info(f'已移动: {json_path} -> {target_path}')
        else:
            shutil.copy2(str(json_path), str(target_path))
            logger.info(f'已复制: {json_path} -> {target_path}')
    except Exception as e:
        logger.error(f'写入目标文件失败 {target_path}: {e}')


def scan_and_organize(src_dir: Path, dest_dir: Path, move: bool = False) -> None:
    """
    扫描目录并组织所有 JSON 文件
    """
    json_files = list(src_dir.rglob('*.json'))
    logger.info(f'共发现 {len(json_files)} 个 JSON 文件，开始处理…')

    for idx, json_file in enumerate(json_files, 1):
        logger.debug(f'[{idx}/{len(json_files)}] 处理 {json_file}')
        organize_file(json_file, dest_dir, move=move)

    logger.info('处理完成！')


@click.command()
@click.option('--src-dir', default='D:\\Downloads\\鸿蒙知识库源数据测试\\harmony_api_references', help='源目录，包含原始 JSON 文件')
@click.option('--dest-dir', default='D:/Downloads/鸿蒙知识库源数据测试/organized_api_docs', help='目标目录，用于存放结构化结果')
@click.option('--move', is_flag=True, help='是否移动文件（默认为复制）')

def main(src_dir: str, dest_dir: str, move: bool):
    """
    根据 metadata.path 将 JSON 文件组织成多级目录结构。
    """
    src_path = Path(src_dir).expanduser().resolve()
    dest_path = Path(dest_dir).expanduser().resolve()

    if not src_path.exists():
        logger.error(f'源目录不存在: {src_path}')
        sys.exit(1)

    dest_path.mkdir(parents=True, exist_ok=True)

    logger.info(
        f'开始组织文件:\n  源目录: {src_path}\n  目标目录: {dest_path}\n  操作: {'移动' if move else '复制'}'
    )

    scan_and_organize(src_path, dest_path, move=move)


if __name__ == '__main__':
    main() 