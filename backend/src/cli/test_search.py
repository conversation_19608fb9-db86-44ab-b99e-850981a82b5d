import asyncio
import sys
import os
import argparse

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.rag.retrievers import VectorRetriever, KeywordRetriever, BM25Retriever
from src.core.logging import get_cli_logger
from src.core.config import settings

# 获取CLI专用logger
logger = get_cli_logger('test_search')

async def test_search(query: str, search_type: str = 'vector', limit: int = settings.DEFAULT_SEARCH_LIMIT, min_score: float = settings.MIN_RELEVANCE_SCORE):
    """
    测试知识库搜索
    
    Args:
        query: 搜索查询
        search_type: 搜索类型 (vector, keyword, 或 bm25)
        limit: 返回结果数量
        min_score: 最小相关度分数（仅用于向量检索和BM25检索）
    """
    try:
        # 初始化数据库会话
        async with async_session_factory() as db_session:
            # 初始化知识库仓库
            knowledge_repo = KnowledgeRepository(db_session)
            
            # 根据搜索类型选择检索器
            if search_type == 'vector':
                logger.info('使用向量检索...')
                retriever = VectorRetriever(knowledge_repo)
                # 执行搜索
                logger.info(f'执行查询: {query} (最小相关度: {min_score})')
                results = await retriever.retrieve(query, limit=limit, min_score=min_score)
            elif search_type == 'bm25':
                logger.info('使用Elasticsearch BM25检索...')
                retriever = BM25Retriever(knowledge_repo)
                # 执行搜索
                logger.info(f'执行查询: {query} (最小分数: {min_score})')
                results = await retriever.retrieve(query, limit=limit, min_score=min_score)
            else:
                logger.info('使用关键词检索...')
                retriever = KeywordRetriever(knowledge_repo)
                # 执行搜索
                logger.info(f'执行查询: {query}')
                results = await retriever.retrieve(query, limit=limit)
            
            # 打印结果
            logger.info(f'找到 {len(results)} 条结果:')
            for i, result in enumerate(results, 1):
                logger.info(f'\n--- 结果 {i} ---')
                logger.info(f'标题: {result.get("title", "N/A")}')
                logger.info(f'相关度: {result.get("relevance", 0):.4f}')
                logger.info(f'内容: {result.get("content", "N/A")[:200]}...')  # 只显示前200个字符
                logger.info('-------------------')
            
            return results
        
    except Exception as e:
        logger.error(f'搜索过程中出错: {str(e)}')
        raise

def main():
    """
    命令行入口
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='测试知识库搜索功能')
    parser.add_argument('query', help='搜索查询')
    parser.add_argument('--type', choices=['vector', 'keyword', 'bm25'], default='vector',
                      help='搜索类型: vector (向量检索), keyword (关键词检索) 或 bm25 (Elasticsearch BM25检索)')
    parser.add_argument('--limit', type=int, default=settings.DEFAULT_SEARCH_LIMIT,
                      help=f'返回结果数量限制 (默认: {settings.DEFAULT_SEARCH_LIMIT})')
    parser.add_argument('--min-score', type=float, default=settings.MIN_RELEVANCE_SCORE,
                      help=f'最小相关度分数，仅用于向量检索 (默认: {settings.MIN_RELEVANCE_SCORE})')
    
    args = parser.parse_args()
    
    # 运行搜索测试
    asyncio.run(test_search(args.query, args.type, args.limit, args.min_score))

if __name__ == '__main__':
    main() 