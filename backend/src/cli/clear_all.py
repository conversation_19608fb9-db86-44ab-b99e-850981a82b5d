#!/usr/bin/env python3
"""
清空知识缓存和ES内容的完整脚本

支持两种模式：
1. 清空所有知识条目: python clear_all.py
2. 按领域清空: python clear_all.py --domains api_documentation best_practices

使用示例：
- 清空API文档领域: python clear_all.py --domains api_documentation
- 清空多个领域: python clear_all.py --domains api_documentation best_practices
- 清空所有知识条目: python clear_all.py
"""

import asyncio
from pathlib import Path
import sys
import os
import shutil
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.database import get_db
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.knowledge_service import KnowledgeService
from src.core.elasticsearch import ElasticSearch
from src.core.embeddings import clear_model_cache
from src.core.logging import get_cli_logger
from src.core.config import settings
from src.core.constants import KnowledgeDomain

es_instance = ElasticSearch(settings.ELASTICSEARCH['INDEX_NAME'])
logger = get_cli_logger('clear_all')

async def clear_all_knowledge_and_cache():
    """
    清空所有知识缓存和ES内容
    """
    try:
        logger.info('🚀 开始清空所有知识缓存和ES内容...')
        # 1. 重新创建ES索引（清空所有ES数据）
        logger.info('1️⃣ 清空Elasticsearch索引...')
        try:
            await es_instance.client.indices.delete(index=settings.ELASTICSEARCH['INDEX_NAME'])
            logger.info(f'✅ 删除旧ES索引{settings.ELASTICSEARCH["INDEX_NAME"]}成功')
        except Exception as e:
            logger.info(f'旧ES索引不存在或删除失败: {e}')
        await es_instance.ensure_index_exists()
        logger.info(f'✅ 创建新ES索引{settings.ELASTICSEARCH["INDEX_NAME"]}成功')
        # 2. 清空数据库中的所有知识条目
        logger.info('2️⃣ 清空数据库中的知识条目...')
        async for db in get_db():
            repository = KnowledgeRepository(db)
            service = KnowledgeService(repository)
            count = await service.delete_all_knowledge()
            logger.info(f'✅ 成功清空 {count} 条知识条目')
            break  # 只执行一次
        # 3. 验证清理结果
        logger.info('3️⃣ 验证清理结果...')
        mapping = await es_instance.client.indices.get_mapping(index=settings.ELASTICSEARCH['INDEX_NAME'])
        properties = mapping[settings.ELASTICSEARCH['INDEX_NAME']]['mappings']['properties']
        logger.info(f'✅ ES索引验证成功，字段数量: {len(properties)}')
        await es_instance.close()
        logger.info('🎉 所有知识缓存和ES内容清空完成！')
        logger.info('\n' + '='*60)
        logger.info('           清理完成 - 摘要报告')
        logger.info('='*60)
        logger.info('✅ Elasticsearch索引已重新创建')
        logger.info(f'✅ 数据库知识条目已清空 ({count} 条)')
        logger.info('='*60)
        return True
    except Exception as e:
        logger.error(f'❌ 清空失败: {e}')
        return False

async def clear_knowledge_by_domains(domains: list):
    """
    按领域清空知识缓存和ES内容
    """
    try:
        logger.info(f'🚀 开始清空指定领域的知识缓存和ES内容: {domains}...')
        # 1. 清空数据库中指定领域的知识条目
        logger.info('2️⃣ 清空数据库中指定领域的知识条目...')
        async for db in get_db():
            repository = KnowledgeRepository(db)
            count = await repository.delete_all_knowledge(domains)
            logger.info(f'✅ 成功清空指定领域的知识条目:{count} 条')
            break  # 只执行一次
        return True
    except Exception as e:
        logger.error(f'❌ 按领域清空失败: {e}')
        return False

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='清空知识缓存和ES内容')
    parser.add_argument(
        '--domains', 
        nargs='+', 
        help='指定要清空的领域，多个领域用空格分隔。如果不指定则清空所有知识条目'
    )
    parser.add_argument(
        '--list-domains', 
        action='store_true', 
        help='列出所有可用的领域'
    )
    args = parser.parse_args()
    if args.domains:
        # 验证领域是否有效
        valid_domains = [d.value for d in KnowledgeDomain]
        invalid_domains = [d for d in args.domains if d not in valid_domains]
        if invalid_domains:
            logger.error(f'❌ 无效的领域: {invalid_domains}')
            logger.info('使用 --list-domains 查看所有可用的领域')
            sys.exit(1)
        logger.info(f'开始按领域清空: {args.domains}')
        result = asyncio.run(clear_knowledge_by_domains(args.domains))
    else:
        logger.info('开始清空所有知识条目')
        result = asyncio.run(clear_all_knowledge_and_cache())
    sys.exit(0 if result else 1) 