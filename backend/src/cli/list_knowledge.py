import asyncio
import sys
import os
from pathlib import Path
import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

# 导入CLI工具辅助函数
from src.cli.utils import setup_cli_logging
# 设置日志
logger = setup_cli_logging('list_knowledge')

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.core.config import settings

@click.command()
@click.option('--page', default=1, help='页码')
@click.option('--page-size', default=20, help='每页数量')
@click.option('--source-type', default=None, help='来源类型过滤')
@click.option('--domain', default=None, help='领域过滤')
def main(page: int, page_size: int, source_type: str, domain: str):
    """
    列出知识库中的内容
    """
    try:
        async def run():
            # 创建数据库会话
            db = async_session_factory()
            try:
                # 初始化仓库
                repository = KnowledgeRepository(db)
                # 获取知识列表
                result = await repository.get_all_knowledge(
                    page=page,
                    page_size=page_size,
                    source_type=source_type,
                    domain=domain
                )
                # 记录结果
                logger.info(f'\n总数: {result["total"]}')
                logger.info(f'当前页: {result["page"]}/{result["total_pages"]}')
                logger.info('\n知识条目列表:')
                logger.info('-' * 80)
                for item in result['items']:
                    logger.info(f'ID: {item["id"]}')
                    logger.info(f'标题: {item["title"]}')
                    logger.info(f'来源: {item["source_type"]}')
                    logger.info(f'URL: {item["url"] or "无"}')
                    logger.info(f'片段数: {item["chunk_count"]}')
                    logger.info(f'创建时间: {item["created_at"]}')
                    logger.info(f'更新时间: {item["updated_at"]}')
                    logger.info('-' * 80)
                return result
            finally:
                await db.close()
        # 运行异步任务并忽略返回值
        asyncio.run(run())
    except Exception as e:
        logger.error(f'获取知识列表失败: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main()