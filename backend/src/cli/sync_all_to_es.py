#!/usr/bin/env python3
"""
sync_all_to_es.py

一次性把数据库中的 Knowledge 及其所有分片 (KnowledgeChunk)
同步到 Elasticsearch，并确保分片顺序与 chunk_index 对齐。

用法：
    python backend/src/cli/sync_all_to_es.py --batch-size 200
"""

import asyncio
import sys
from pathlib import Path
import click

# 将项目根目录加入 PYTHONPATH
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

from sqlalchemy import select

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.knowledge_service import KnowledgeService
from src.models.knowledge import Knowledge, KnowledgeChunk
from src.core.id_generator import IDGenerator
from src.core.elasticsearch import ElasticSearch
from src.core.logging import get_cli_logger

logger = get_cli_logger('sync_all_to_es')
es_instance = ElasticSearch()

async def sync_all_to_es(batch_size: int = 200):
    await es_instance.sync_to_elastic_search(batch_size)
    await es_instance.close()

@click.command()
@click.option('--batch-size', default=200, show_default=True, help='分页大小')
def main(batch_size: int):
    """
    CLI 入口
    """
    asyncio.run(sync_all_to_es(batch_size))

if __name__ == '__main__':
    main() 