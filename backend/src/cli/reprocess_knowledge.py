# -*- coding: utf-8 -*-
import asyncio
import sys
import os
from pathlib import Path
import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.knowledge_service import KnowledgeService
from src.core.config import settings
from src.core.logging import get_cli_logger

# 获取CLI专用logger
logger = get_cli_logger('reprocess_knowledge')

@click.command()
@click.argument('knowledge_id')
@click.option('--chunk-size', default=500, help='分块大小')
@click.option('--chunk-overlap', default=50, help='分块重叠大小')
def main(knowledge_id: str, chunk_size: int, chunk_overlap: int):
    """
    重新处理知识条目，生成分块
    """
    try:
        async def run():
            # 创建数据库会话
            async with async_session_factory() as db:
                try:
                    # 初始化仓库和服务
                    repository = KnowledgeRepository(db)
                    service = KnowledgeService(repository)
                    # 获取知识详情
                    knowledge = await repository.get_knowledge_by_id(knowledge_id)
                    if not knowledge:
                        logger.warning(f'\n未找到ID为 {knowledge_id} 的知识条目')
                        return
                    logger.info(f'开始重新处理知识条目: {knowledge["title"]}')
                    # 确保metadata是字典类型
                    metadata = {}
                    if 'metadata' in knowledge and isinstance(knowledge['metadata'], dict):
                        metadata = knowledge['metadata']
                    # 重新处理文档内容，生成分块
                    result = await service.update_knowledge(
                        knowledge_id=knowledge_id,
                        title=knowledge['title'],
                        content=knowledge['content'],
                        url=knowledge.get('url'),
                        metadata=metadata,
                        reprocess_chunks=True,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )
                    if result:
                        # 获取更新后的知识详情
                        updated_knowledge = await repository.get_knowledge_by_id(knowledge_id)
                        logger.info(f'处理完成，生成了 {len(updated_knowledge["chunks"])} 个知识片段')
                        logger.info('\n知识片段列表:')
                        for i, chunk in enumerate(updated_knowledge['chunks'], 1):
                            logger.info(f'\n--- 片段 {i} ---')
                            logger.info(f'ID: {chunk["id"]}')
                            logger.info(f'内容长度: {len(chunk["content"])} 字符')
                            logger.info(f'内容预览: {chunk["content"][:100]}...')
                    else:
                        logger.error('处理失败')
                    return result
                except Exception as e:
                    logger.error(f'处理过程中出错: {str(e)}')
                    raise
        # 运行异步任务
        asyncio.run(run())
    except Exception as e:
        logger.error(f'重新处理知识条目失败: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main()