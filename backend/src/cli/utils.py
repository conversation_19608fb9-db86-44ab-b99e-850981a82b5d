"""
CLI工具辅助函数
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.core.config import settings
from src.core.logging import get_cli_logger

def setup_cli_logging(cli_name: str, log_level: str = 'INFO') -> None:
    """
    为CLI工具设置日志配置
    
    Args:
        cli_name: CLI工具名称，用于日志文件命名
        log_level: 日志级别
        
    Returns:
        配置好的logger实例
    """
    # 使用新的统一日志接口
    return get_cli_logger(cli_name, log_level)
