import asyncio
import sys
import os
from pathlib import Path
import traceback
from typing import List

import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.logging import setup_logging, app_logger as logger
from src.core.database import async_session_factory, init_db
from src.repositories.knowledge_repository import KnowledgeRepository
from src.repositories.import_task_repository import ImportTaskRepository
from src.services.local_docs_service import LocalDocsService
from src.core.config import settings


# 初始化日志配置
setup_logging(log_level=settings.LOG_LEVEL, log_file_name='import_local_docs_by_path.log', log_to_file=True)


def _filter_docs_by_path(docs: List[dict], filters: List[str], docs_dir: str) -> List[dict]:
    """根据相对路径中的文件夹是否包含任意关键字进行过滤"""
    if not filters:
        return docs
    normalized_filters = [f.lower() for f in filters]
    logger.debug(f'过滤关键字: {normalized_filters}')
    filtered: List[dict] = []
    for doc in docs:
        # 获取不包含文件名的相对目录路径
        # 需要都转换为Path对象，然后算相对路径
        path = Path(doc['file_path'])
        rel_dir = path.relative_to(docs_dir).parts
        logger.debug(f'文件路径: {rel_dir}')
        # 将目录名也转换为小写进行比较
        if any(filter_keyword in dir_name.lower() for filter_keyword in normalized_filters for dir_name in rel_dir):
            filtered.append(doc)
            logger.debug(f'文件路径: {rel_dir} 匹配到关键字: {normalized_filters}')
    return filtered


@click.command()
@click.option('--docs-dir', default=settings.KNOWLEDGE_BASE_PATH, help='本地文档目录路径')
@click.option('--filter', 'filters', multiple=True, help='路径筛选关键字，可指定多次。如 --filter 应用框架 --filter JS组件')
@click.option('--scan-only', is_flag=True, help='仅扫描文档，不导入')
@click.option('--domain', default=None, help='指定导入的文档领域')

def main(docs_dir: str, filters: List[str], scan_only: bool, domain: str):
    """根据文件夹路径关键字筛选后导入本地文档到知识库"""
    try:
        # 确保目录存在
        os.makedirs(docs_dir, exist_ok=True)

        async def run():
            logger.info('初始化数据库…')
            await init_db()
            logger.info('数据库初始化完成')

            async with async_session_factory() as db:
                knowledge_repo = KnowledgeRepository(db)
                import_task_repo = ImportTaskRepository(db)

                service = LocalDocsService(
                    knowledge_repository=knowledge_repo,
                    import_task_repository=import_task_repo,
                    docs_dir=docs_dir,
                    domain=domain,
                )

                logger.info(f'开始扫描目录: {docs_dir}')
                docs = await service.scan_docs()
                logger.info(f'扫描完成，发现 {len(docs)} 个文件 (支持 {sum(1 for d in docs if d["supported"]) } 个)')

                # 根据关键字进行过滤
                filtered_docs = _filter_docs_by_path(docs, list(filters), docs_dir)
                supported_filtered = [d for d in filtered_docs if d['supported']]

                logger.info(f'过滤关键字: {filters if filters else "无"} -> 匹配到 {len(filtered_docs)} 个文件，其中支持 {len(supported_filtered)} 个')

                # 如果只是扫描
                if scan_only:
                    return {
                        'total': len(docs),
                        'matched_total': len(filtered_docs),
                        'matched_supported': len(supported_filtered),
                    }

                if not supported_filtered:
                    logger.warning('没有符合条件且受支持的文件可供导入')
                    return {
                        'success': [],
                        'failed': [],
                        'total': 0,
                    }

                # 开始导入
                logger.info('开始导入符合条件的文档…')
                file_paths = [d['file_path'] for d in supported_filtered]
                logger.info(f'开始导入文件，一共有{len(file_paths)}个文件，文件路径如下：')
                for i, file_path in enumerate(file_paths):
                    logger.info(f'{i+1}. {file_path}')
                results = await service.import_docs(file_paths=file_paths)

                logger.info(f'导入完成: 成功 {len(results["success"])} 个, 失败 {len(results["failed"])} 个')

                if results['failed']:
                    for item in results['failed']:
                        logger.error(f'导入失败: {item["file_path"]} -> {item["error"]}')

                return results

        return asyncio.run(run())

    except Exception as e:
        logger.error(f'导入过程出错: {str(e)}')
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main() 