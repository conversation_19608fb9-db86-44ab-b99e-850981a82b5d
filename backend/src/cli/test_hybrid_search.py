import asyncio
import sys
import os
import argparse

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) )


# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.rag.retrievers import HybridRetriever, VectorRetriever, BM25Retriever
from src.core.logging import get_cli_logger
from src.core.config import settings

# 获取CLI专用logger
logger = get_cli_logger('test_hybrid_search')

async def test_hybrid_search(
    query: str, 
    vector_weight: float = 0.5,
    bm25_weight: float = 0.5,
    fusion_method: str = 'rrf',
    limit: int = settings.DEFAULT_SEARCH_LIMIT,
    min_score: float = settings.MIN_RELEVANCE_SCORE,
    compare_methods: bool = False
):
    """
    测试混合检索器
    
    Args:
        query: 搜索查询
        vector_weight: 向量检索权重
        bm25_weight: BM25检索权重
        fusion_method: 融合方法 ('weighted_sum' 或 'rrf')
        limit: 返回结果数量
        min_score: 最小相关度分数
        compare_methods: 是否比较不同的检索方法
    """
    try:
        # 初始化数据库会话
        async with async_session_factory() as db_session:
            # 初始化知识库仓库
            knowledge_repo = KnowledgeRepository(db_session)
            
            logger.info('开始混合检索测试')
            logger.info(f'查询: {query}')
            logger.info(f'参数: vector_weight={vector_weight}, bm25_weight={bm25_weight}, fusion_method={fusion_method}')
            
            # 创建混合检索器
            hybrid_retriever = HybridRetriever(
                knowledge_repository=knowledge_repo,
                vector_weight=vector_weight,
                bm25_weight=bm25_weight,
                fusion_method=fusion_method
            )
            
            # 执行混合检索
            logger.info('\n=== 混合检索结果 ===')
            hybrid_results = await hybrid_retriever.retrieve(
                query=query, 
                limit=limit, 
                min_score=min_score
            )
            
            # 显示结果
            logger.info(f'混合检索找到 {len(hybrid_results)} 条结果:')
            for i, result in enumerate(hybrid_results, 1):
                logger.info(f'\n--- 混合结果 {i} ---')
                logger.info(f'标题: {result.get("title", "N/A")}')
                logger.info(f'混合相关度: {result.get("relevance", 0):.4f}')
                
                # 显示融合信息
                fusion_info = result.get('fusion_info', {})
                if fusion_info:
                    logger.info(f'融合信息: {fusion_info}')
                
                logger.info(f'内容: {result.get("content", "N/A")[:200]}...')
            
            # 获取统计信息
            logger.info('\n=== 检索统计信息 ===')
            stats = await hybrid_retriever.get_retrieval_stats(query, limit)
            for key, value in stats.items():
                logger.info(f'{key}: {value}')
            
            # 如果启用比较模式，分别测试单独的检索方法
            if compare_methods:
                await compare_retrieval_methods(knowledge_repo, query, limit, min_score)
            
            return hybrid_results
        
    except Exception as e:
        logger.error(f'混合检索测试失败: {str(e)}')
        raise

async def compare_retrieval_methods(knowledge_repo, query: str, limit: int, min_score: float):
    """比较不同的检索方法"""
    logger.info('\n=== 检索方法比较 ===')
    
    # 1. 向量检索
    logger.info('\n--- 纯向量检索 ---')
    vector_retriever = VectorRetriever(knowledge_repo)
    vector_results = await vector_retriever.retrieve(query, limit=limit, min_score=min_score)
    logger.info(f'向量检索找到 {len(vector_results)} 条结果')
    for i, result in enumerate(vector_results[:3], 1):  # 只显示前3个
        logger.info(f'{i}. {result.get("title", "N/A")} (相关度: {result.get("relevance", 0):.4f})')
    
    # 2. BM25检索
    logger.info('\n--- 纯BM25检索 ---')
    bm25_retriever = BM25Retriever(knowledge_repo)
    bm25_results = await bm25_retriever.retrieve(query, limit=limit, min_score=min_score)
    logger.info(f'BM25检索找到 {len(bm25_results)} 条结果')
    for i, result in enumerate(bm25_results[:3], 1):  # 只显示前3个
        logger.info(f'{i}. {result.get("title", "N/A")} (相关度: {result.get("relevance", 0):.4f})')
    
    # 3. 不同权重的混合检索
    logger.info('\n--- 不同权重的混合检索比较 ---')
    weight_configs = [
        (0.3, 0.7, '更偏向BM25'),
        (0.5, 0.5, '均衡权重'),
        (0.7, 0.3, '更偏向向量')
    ]
    
    for vector_w, bm25_w, desc in weight_configs:
        logger.info(f'\n{desc} (向量:{vector_w}, BM25:{bm25_w}):')
        hybrid_retriever = HybridRetriever(
            knowledge_repository=knowledge_repo,
            vector_weight=vector_w,
            bm25_weight=bm25_w,
            fusion_method='rrf'
        )
        hybrid_results = await hybrid_retriever.retrieve(query, limit=3, min_score=min_score)
        for i, result in enumerate(hybrid_results, 1):
            logger.info(f'  {i}. {result.get("title", "N/A")} (混合分数: {result.get("relevance", 0):.4f})')

async def test_weight_adjustment():
    """测试权重动态调整功能"""
    logger.info('\n=== 权重动态调整测试 ===')
    
    query = '如何使用API'
    
    async with async_session_factory() as db_session:
        knowledge_repo = KnowledgeRepository(db_session)
        
        # 创建混合检索器
        hybrid_retriever = HybridRetriever(
            knowledge_repository=knowledge_repo,
            vector_weight=0.5,
            bm25_weight=0.5
        )
        
        # 测试不同的权重配置
        weight_tests = [
            (0.2, 0.8, '偏向关键词匹配'),
            (0.8, 0.2, '偏向语义理解'),
            (0.5, 0.5, '平衡权重')
        ]
        
        for vector_w, bm25_w, desc in weight_tests:
            logger.info(f'\n测试配置: {desc}')
            
            # 动态调整权重
            hybrid_retriever.set_weights(vector_w, bm25_w)
            
            # 执行检索
            results = await hybrid_retriever.retrieve(query, limit=3)
            logger.info(f'找到 {len(results)} 条结果:')
            for i, result in enumerate(results, 1):
                fusion_info = result.get('fusion_info', {})
                logger.info(f'  {i}. {result.get("title", "N/A")[:50]}... (分数: {result.get("relevance", 0):.4f})')

async def test_fusion_methods():
    """测试不同的融合方法"""
    logger.info('\n=== 融合方法测试 ===')
    
    query = '鸿蒙开发示例'
    
    async with async_session_factory() as db_session:
        knowledge_repo = KnowledgeRepository(db_session)
        
        # 测试不同的融合方法
        fusion_methods = ['weighted_sum', 'rrf']
        
        for method in fusion_methods:
            logger.info(f'\n--- 使用 {method} 融合方法 ---')
            
            hybrid_retriever = HybridRetriever(
                knowledge_repository=knowledge_repo,
                vector_weight=0.6,
                bm25_weight=0.4,
                fusion_method=method
            )
            
            results = await hybrid_retriever.retrieve(query, limit=5)
            logger.info(f'{method} 方法找到 {len(results)} 条结果:')
            
            for i, result in enumerate(results, 1):
                fusion_info = result.get('fusion_info', {})
                logger.info(f'  {i}. {result.get("title", "N/A")[:40]}... (分数: {result.get("relevance", 0):.4f})')
                if fusion_info:
                    logger.info(f'     融合详情: {fusion_info}')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试混合检索器')
    parser.add_argument('query', help='搜索查询')
    parser.add_argument('--vector-weight', type=float, default=0.5, help='向量检索权重 (0.0-1.0)')
    parser.add_argument('--bm25-weight', type=float, default=0.5, help='BM25检索权重 (0.0-1.0)')
    parser.add_argument('--fusion-method', choices=['weighted_sum', 'rrf'], default='rrf', help='融合方法')
    parser.add_argument('--limit', type=int, default=settings.DEFAULT_SEARCH_LIMIT, help='返回结果数量')
    parser.add_argument('--min-score', type=float, default=settings.MIN_RELEVANCE_SCORE, help='最小相关度分数')
    parser.add_argument('--compare', action='store_true', help='比较不同的检索方法')
    parser.add_argument('--test-weights', action='store_true', help='测试权重调整功能')
    parser.add_argument('--test-fusion', action='store_true', help='测试不同融合方法')
    
    args = parser.parse_args()
    
    async def run_tests():
        if args.test_weights:
            await test_weight_adjustment()
        elif args.test_fusion:
            await test_fusion_methods()
        else:
            await test_hybrid_search(
                query=args.query,
                vector_weight=args.vector_weight,
                bm25_weight=args.bm25_weight,
                fusion_method=args.fusion_method,
                limit=args.limit,
                min_score=args.min_score,
                compare_methods=args.compare
            )
    
    # 运行测试
    asyncio.run(run_tests())

if __name__ == '__main__':
    main() 