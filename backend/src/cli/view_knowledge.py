import asyncio
import sys
import os
from pathlib import Path
import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.database import async_session_factory
from src.repositories.knowledge_repository import KnowledgeRepository
from src.core.config import settings
from src.core.logging import get_cli_logger

# 获取CLI专用logger
logger = get_cli_logger('view_knowledge')

@click.command()
@click.argument('knowledge_id')
def main(knowledge_id: str):
    """
    查看知识条目详情
    """
    try:
        async def run():
            # 创建数据库会话
            db = async_session_factory()
            try:
                # 初始化仓库
                repository = KnowledgeRepository(db)
                # 获取知识详情
                result = await repository.get_knowledge_by_id(knowledge_id)
                if not result:
                    logger.warning(f'\n未找到ID为 {knowledge_id} 的知识条目')
                    return
                # 记录结果
                logger.info('\n知识条目详情:')
                logger.info('-' * 80)
                logger.info(f'ID: {result["id"]}')
                logger.info(f'标题: {result["title"]}')
                logger.info(f'来源: {result["source_type"]}')
                logger.info(f'URL: {result["url"] or "无"}')
                logger.info(f'创建时间: {result["created_at"]}')
                logger.info(f'更新时间: {result["updated_at"]}')
                logger.info('\n内容:')
                logger.info('-' * 80)
                logger.info(result['content'])
                logger.info('-' * 80)
                if result['chunks']:
                    logger.info('\n知识片段:')
                    for chunk in result['chunks']:
                        logger.info('-' * 40)
                        logger.info(f'片段ID: {chunk["id"]}')
                        logger.info(f'内容: {chunk["content"]}')
                return result
            finally:
                await db.close()
        # 运行异步任务并忽略返回值
        asyncio.run(run())
    except Exception as e:
        logger.error(f'获取知识详情失败: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main() 