# -*- coding: utf-8 -*-
import asyncio
import sys
import os
from pathlib import Path
import traceback
import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入配置加载器
from src.core.config_loader import load_configurations
# 重新加载配置
load_configurations()

# 导入日志配置
from src.core.logging import setup_logging, app_logger as logger

# 显示环境变量信息
if os.environ.get('NKRA_ENV'):
    logger.info(f'当前环境: {os.environ.get("NKRA_ENV")}')
else:
    logger.warning('未设置 NKRA_ENV 环境变量')

from src.core.database import async_session_factory, init_db
from src.repositories.knowledge_repository import KnowledgeRepository
from src.repositories.import_task_repository import ImportTaskRepository
from src.services.local_docs_service import LocalDocsService
from src.core.config import settings

# 初始化日志配置
setup_logging(log_level=settings.LOG_LEVEL, log_file_name='import_local_docs.log',
              log_to_file=True)

@click.command()
@click.option('--docs-dir', default=settings.KNOWLEDGE_BASE_PATH, help='本地文档目录路径')
@click.option('--scan-only', is_flag=True, help='仅扫描文档，不导入')
@click.option('--domain', default=None, help='指定导入的文档领域')
def main(docs_dir: str, scan_only: bool, domain: str):
    """
    导入本地文档到知识库
    """
    try:
        # 确保目录存在
        os.makedirs(docs_dir, exist_ok=True)
        async def run():
            # 初始化数据库，确保所有表都已创建
            logger.info('初始化数据库...')
            await init_db()
            logger.info('数据库初始化完成')
            # 创建异步数据库会话
            async with async_session_factory() as db:
                try:
                    # 初始化仓库
                    knowledge_repo = KnowledgeRepository(db)
                    import_task_repo = ImportTaskRepository(db)
                    # 创建服务
                    service = LocalDocsService(
                        knowledge_repository=knowledge_repo,
                        import_task_repository=import_task_repo,
                        docs_dir=docs_dir,
                        domain=domain
                    )
                    # 扫描文档
                    logger.info(f'开始扫描目录: {docs_dir}')
                    docs = await service.scan_docs()
                    # 打印扫描结果
                    supported_docs = [doc for doc in docs if doc['supported']]
                    unsupported_docs = [doc for doc in docs if not doc['supported']]
                    logger.info(f'扫描完成，共发现 {len(docs)} 个文件：')
                    logger.info(f'- 支持的文件：{len(supported_docs)} 个')
                    logger.info(f'- 不支持的文件：{len(unsupported_docs)} 个')
                    if unsupported_docs:
                        logger.warning('不支持的文件列表：')
                        for doc in unsupported_docs:
                            logger.warning(f'- {doc["relative_path"]} ({doc["file_type"]})')
                    # 如果只是扫描，返回扫描结果
                    if scan_only:
                        return {
                            'total': len(docs), 
                            'supported': len(supported_docs), 
                            'unsupported': len(unsupported_docs)
                        }
                    # 开始导入
                    if supported_docs:
                        logger.info('开始导入文档...')
                        results = await service.import_docs()
                        # 打印导入结果
                        logger.info('导入完成：')
                        logger.info(f'- 成功：{len(results["success"])} 个')
                        logger.info(f'- 失败：{len(results["failed"])} 个')
                        if results['failed']:
                            logger.warning('导入失败的文件：')
                            for failed in results['failed']:
                                logger.warning(f'- {failed["file_path"]}: {failed["error"]}')
                        return results
                    else:
                        logger.warning('没有发现可以导入的文档')
                        return {'success': [], 'failed': [], 'total': 0}
                except Exception as e:
                    logger.error(f'处理过程出错: {str(e)}')
                    logger.error(traceback.format_exc())
                    raise
        # 运行异步任务并返回结果
        return asyncio.run(run())
    except Exception as e:
        logger.error(f'导入过程出错: {str(e)}')
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == '__main__':
    main() 