#!/usr/bin/env python3
"""
重新创建优化后的Elasticsearch索引
"""

import asyncio
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


# 导入配置加载器
from src.core.config_loader import load_configurations

# 重新加载配置
load_configurations()

from src.core.config import settings
from src.core.elasticsearch import ElasticSearch
from src.core.logging import get_cli_logger

logger = get_cli_logger('recreate_index')
es_instance = ElasticSearch(settings.ELASTICSEARCH['INDEX_NAME'])

async def recreate_index():
    """
    重新创建优化后的索引
    """
    try:
        logger.info('开始重新创建Elasticsearch索引...')
        
        # 1. 删除旧索引
        try:
            await es_instance.client.indices.delete(index=settings.ELASTICSEARCH['INDEX_NAME'])
            logger.info(f'✅ 删除旧索引{settings.ELASTICSEARCH["INDEX_NAME"]}成功')
        except Exception as e:
            logger.info(f'旧索引{settings.ELASTICSEARCH["INDEX_NAME"]}不存在或删除失败: {e}')
        
        # 2. 创建新索引
        await es_instance.ensure_index_exists()
        logger.info(f'✅ 创建新索引{settings.ELASTICSEARCH["INDEX_NAME"]}成功')
        
        # 3. 获取索引信息验证
        mapping = await es_instance.client.indices.get_mapping(index=settings.ELASTICSEARCH['INDEX_NAME'])
        properties = mapping[settings.ELASTICSEARCH['INDEX_NAME']]['mappings']['properties']
        
        logger.info(f'✅ 索引映射验证成功，索引字段数量: {len(properties)}')
        
        logger.info(f'🎉 索引{settings.ELASTICSEARCH["INDEX_NAME"]}重新创建完成！')
        return True
        
    except Exception as e:
        logger.error(f'❌ 索引重新创建失败: {e}')
        return False
    finally:
        await es_instance.close()

if __name__ == '__main__':
    result = asyncio.run(recreate_index())
    sys.exit(0 if result else 1) 