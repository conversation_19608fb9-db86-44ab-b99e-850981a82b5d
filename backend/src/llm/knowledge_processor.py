"""
知识处理器 - 负责知识条目的智能过滤、合并和格式化

此模块提供了知识条目的高级处理功能，包括：
1. 基于相关性的智能过滤
2. 语义完整性保护
3. 相似知识条目合并
4. 针对LLM的优化格式化
"""

import re
import hashlib
from typing import List, Dict, Any, Tuple, Optional
from ..core.logging import get_module_logger

# 初始化模块级logger
logger = get_module_logger(__name__)

# 导入嵌入模型接口
from ..core.embeddings import compute_similarity, compute_embeddings

class KnowledgeProcessor:
    """知识处理器 - 负责知识条目的智能过滤、合并和格式化"""
    
    def __init__(self, 
                 min_relevance_score: float = 0.6,
                 max_items: int = 20,
                 similarity_threshold: float = 0.85):
        """
        初始化知识处理器
        
        Args:
            min_relevance_score: 最低相关性分数阈值
            max_items: 最大知识条目数量
            similarity_threshold: 相似度阈值，用于去重和合并
        """
        self.min_relevance_score = min_relevance_score
        self.max_items = max_items
        self.similarity_threshold = similarity_threshold
        logger.info(f"知识处理器初始化，参数: min_score={min_relevance_score}, max_items={max_items}, sim_threshold={similarity_threshold}")
    
    def filter_knowledge(self, 
                      knowledge_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        基于相关性分数过滤知识条目
        
        Args:
            knowledge_items: 知识条目列表，每个条目需包含'content'和'relevance_score'字段
            
        Returns:
            过滤后的知识条目列表
        """
        if not knowledge_items:
            return []
            
        # 基于相关性分数过滤
        filtered_items = [item for item in knowledge_items 
                        if item.get('relevance_score', 0) >= self.min_relevance_score]
        
        # 按相关性分数排序
        sorted_items = sorted(filtered_items, 
                            key=lambda x: x.get('relevance_score', 0), 
                            reverse=True)
        
        # 限制数量
        result = sorted_items[:self.max_items]
        
        logger.info(f"知识过滤: {len(knowledge_items)} -> {len(result)} 条目")
        return result
    
    def _compute_content_fingerprint(self, content: str) -> str:
        """
        计算内容的指纹，用于去重
        
        Args:
            content: 文本内容
            
        Returns:
            内容指纹（哈希值）
        """
        # 预处理：移除多余空格、标点符号，转小写
        normalized = re.sub(r'\s+', ' ', content.lower())
        normalized = re.sub(r'[^\w\s]', '', normalized).strip()
        # 创建SHA256哈希
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()
    
    def deduplicate_knowledge(self, 
                            knowledge_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复或高度相似的知识条目
        
        Args:
            knowledge_items: 知识条目列表
            
        Returns:
            去重后的知识条目列表
        """
        if not knowledge_items:
            return []
            
        # 通过内容指纹进行初步去重
        fingerprints = set()
        unique_items = []
        
        for item in knowledge_items:
            content = item.get('content', '')
            if not content:
                continue
                
            fingerprint = self._compute_content_fingerprint(content)
            if fingerprint not in fingerprints:
                fingerprints.add(fingerprint)
                unique_items.append(item)
        
        # 使用嵌入模型检测语义相似性去重
        result = []
        for i, item in enumerate(unique_items):
            is_duplicate = False
            # 只与已添加的结果进行比较，减少计算量
            for existing_item in result:
                similarity = compute_similarity(
                    item.get('content', ''), 
                    existing_item.get('content', '')
                )
                
                if similarity >= self.similarity_threshold:
                    is_duplicate = True
                    # 如果当前条目相关性更高，替换已有条目
                    if item.get('relevance_score', 0) > existing_item.get('relevance_score', 0):
                        result.remove(existing_item)
                        result.append(item)
                    break
                    
            if not is_duplicate:
                result.append(item)
                
        logger.info(f"知识去重: {len(knowledge_items)} -> {len(result)} 条目")
        return result
    
    def truncate_with_semantic_boundary(self, 
                                      text: str, 
                                      max_length: int = 1000) -> str:
        """
        在语义边界处截断文本，保持完整性
        
        Args:
            text: 要截断的文本
            max_length: 最大长度
            
        Returns:
            截断后的文本
        """
        if len(text) <= max_length:
            return text
            
        # 尝试在句子边界截断
        truncated = text[:max_length]
        
        # 寻找最后一个完整句子
        sentence_boundaries = ['. ', '! ', '? ', '。', '！', '？', '\n\n']
        best_position = 0
        
        for boundary in sentence_boundaries:
            pos = truncated.rfind(boundary)
            if pos > best_position:
                best_position = pos + len(boundary)
                
        # 如果找不到句子边界，尝试段落或词语边界
        if best_position == 0:
            # 尝试段落
            pos = truncated.rfind('\n')
            if pos > 0:
                best_position = pos + 1
            else:
                # 退化到词语边界
                word_boundaries = [' ', '，', ',', '、', ';', '；']
                for boundary in word_boundaries:
                    pos = truncated.rfind(boundary)
                    if pos > best_position:
                        best_position = pos + len(boundary)
        
        # 如果仍然找不到合适的边界，退化到截断点
        if best_position == 0:
            best_position = max_length
            
        # 添加省略号表示已截断
        return truncated[:best_position] + "..."
    
    def merge_similar_knowledge(self, 
                             knowledge_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        合并相似的知识条目
        
        Args:
            knowledge_items: 知识条目列表
            
        Returns:
            合并后的知识条目列表
        """
        if not knowledge_items or len(knowledge_items) <= 1:
            return knowledge_items
            
        # 提取所有文本内容用于计算嵌入向量
        texts = [item.get('content', '') for item in knowledge_items]
        
        # 计算所有文本的嵌入向量（批量处理提高效率）
        try:
            # 计算嵌入向量
            embeddings = compute_embeddings(texts)
            
            # 构建嵌入向量与条目的映射
            items_with_embeddings = [
                (knowledge_items[i], embeddings[i]) 
                for i in range(len(knowledge_items))
            ]
            
            # 聚类合并相似条目
            merged_items = []
            processed_indices = set()
            
            for i in range(len(items_with_embeddings)):
                if i in processed_indices:
                    continue
                    
                current_item, current_embedding = items_with_embeddings[i]
                similar_items = [current_item]
                processed_indices.add(i)
                
                # 寻找与当前条目相似的条目
                from sentence_transformers import util
                import torch
                current_embedding_tensor = torch.tensor([current_embedding])
                
                for j in range(i+1, len(items_with_embeddings)):
                    if j in processed_indices:
                        continue
                        
                    other_item, other_embedding = items_with_embeddings[j]
                    other_embedding_tensor = torch.tensor([other_embedding])
                    
                    # 计算余弦相似度
                    similarity = util.pytorch_cos_sim(
                        current_embedding_tensor, 
                        other_embedding_tensor
                    ).item()
                    
                    if similarity >= self.similarity_threshold:
                        similar_items.append(other_item)
                        processed_indices.add(j)
                
                if len(similar_items) == 1:
                    # 没有相似条目，直接添加
                    merged_items.append(current_item)
                else:
                    # 合并相似条目
                    merged_item = self._merge_items(similar_items)
                    merged_items.append(merged_item)
            
            logger.info(f"知识合并: {len(knowledge_items)} -> {len(merged_items)} 条目")
            return merged_items
            
        except Exception as e:
            logger.error(f"合并知识条目失败: {str(e)}")
            return knowledge_items
    
    def _merge_items(self, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合并多个相似的知识条目
        
        Args:
            items: 要合并的条目列表
            
        Returns:
            合并后的条目
        """
        if not items:
            return {}
            
        if len(items) == 1:
            return items[0]
            
        # 按相关性分数排序
        sorted_items = sorted(items, key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        # 使用相关性最高的条目作为基础
        base_item = sorted_items[0].copy()
        
        # 合并内容，避免重复
        contents = [item.get('content', '') for item in sorted_items]
        merged_content = self._combine_text_content(contents)
        
        # 更新合并后的条目
        base_item['content'] = merged_content
        base_item['is_merged'] = True
        base_item['merged_count'] = len(items)
        
        return base_item
    
    def _combine_text_content(self, texts: List[str]) -> str:
        """
        智能合并多个文本内容，避免重复
        
        Args:
            texts: 文本列表
            
        Returns:
            合并后的文本
        """
        if not texts:
            return ""
            
        if len(texts) == 1:
            return texts[0]
            
        # 使用最长的文本作为基础
        base_text = max(texts, key=len)
        
        # 尝试从其他文本中提取补充信息
        for text in texts:
            if text == base_text:
                continue
                
            # 分析当前文本是否包含基础文本中没有的信息
            sentences = re.split(r'(?<=[.!?。！？])\s+', text)
            for sentence in sentences:
                # 如果句子足够长且不在基础文本中，添加到基础文本
                if len(sentence) > 10 and sentence not in base_text:
                    base_text += "\n" + sentence
        
        return base_text
    
    def process_knowledge(self, 
                        knowledge_items: List[Dict[str, Any]],
                        query: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        对知识条目进行完整的处理流程
        
        Args:
            knowledge_items: 知识条目列表
            query: 用户查询，用于额外的相关性排序
            
        Returns:
            处理后的知识条目列表
        """
        if not knowledge_items:
            return []
            
        logger.info(f"开始处理知识条目，共 {len(knowledge_items)} 条")
        
        # 1. 基于相关性过滤
        filtered_items = self.filter_knowledge(knowledge_items)
        
        # 2. 去除重复条目
        unique_items = self.deduplicate_knowledge(filtered_items)
        
        # 3. 合并相似条目
        merged_items = self.merge_similar_knowledge(unique_items)
        
        # 4. 如果提供了查询，根据查询重新排序
        if query:
            # 计算每个条目与查询的相似度
            try:
                for item in merged_items:
                    content = item.get('content', '')
                    item['query_similarity'] = compute_similarity(query, content)
                
                # 按查询相似度排序
                merged_items = sorted(merged_items, key=lambda x: x.get('query_similarity', 0), reverse=True)
            except Exception as e:
                logger.error(f"根据查询排序知识条目失败: {str(e)}")
        
        logger.info(f"知识处理完成，最终条目数量: {len(merged_items)}")
        return merged_items
    
    def format_knowledge_for_prompt(self, 
                                 knowledge_items: List[Dict[str, Any]], 
                                 max_tokens: int = 4000) -> str:
        """
        将处理后的知识条目格式化为适合LLM的提示
        
        Args:
            knowledge_items: 处理后的知识条目列表
            max_tokens: 最大令牌数
            
        Returns:
            格式化后的知识文本
        """
        if not knowledge_items:
            return "没有找到相关的知识条目。"
            
        formatted_text = "以下是与您的问题相关的知识：\n\n"
        
        # 估算每个标记的平均字符数（中文约1.5个字符/标记）
        avg_chars_per_token = 1.5
        max_chars = int(max_tokens * avg_chars_per_token * 0.9)  # 留10%余量
        
        current_chars = len(formatted_text)
        
        for i, item in enumerate(knowledge_items):
            content = item.get('content', '')
            source = item.get('source', '未知来源')
            
            # 格式化当前条目
            item_text = f"[知识 {i+1}] {content}\n"
            
            # 如果来源存在，添加来源信息
            if source and source != '未知来源':
                item_text += f"来源: {source}\n"
                
            item_text += "\n"
            
            # 检查是否超出最大字符限制
            if current_chars + len(item_text) > max_chars:
                # 如果添加整个条目会超出限制，尝试截断
                remaining_chars = max_chars - current_chars - len("\n[知识 {i+1}] ") - len("\n来源: ") - len(source) - 10
                
                if remaining_chars > 100:  # 确保至少有足够的内容
                    truncated_content = self.truncate_with_semantic_boundary(content, remaining_chars)
                    item_text = f"[知识 {i+1}] {truncated_content}\n"
                    if source and source != '未知来源':
                        item_text += f"来源: {source}\n"
                    item_text += "\n"
                    formatted_text += item_text
                
                # 添加注释说明已达到上限
                formatted_text += "\n[由于上下文长度限制，部分相关知识已省略]"
                break
                
            formatted_text += item_text
            current_chars += len(item_text)
        
        return formatted_text 