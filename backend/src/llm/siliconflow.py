import json
import aiohttp
import asyncio
import time
from typing import AsyncIterator, List, Optional, Union, Dict
import numpy as np

from ..core.logging import get_module_logger
from ..core.config import settings
from .base import BaseLLMService
from .prompts import PromptTemplates
from ..core.exceptions import (
    LLMServiceError, 
    LLMServiceTimeoutError, 
    LLMServiceUnavailableError,
    LLMServiceRequestError
)

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class AsyncStreamResponse:
    """异步流式响应包装器"""
    def __init__(self, response, process_func, timeout=None, heartbeat_interval=5):
        self.response = response
        self.process_func = process_func
        self._session = None
        # 如果未指定超时时间，则使用配置中的流式超时时间或默认值
        if timeout is None:
            self.timeout = getattr(settings, "LLM_STREAM_TIMEOUT", 120.0)
        else:
            self.timeout = timeout
        self._closed = False
        self._closing = False
        self._last_activity = asyncio.get_event_loop().time()
        self._heartbeat_interval = heartbeat_interval
        self._heartbeat_task = None
        self._stream_active = True
        self._received_data_count = 0
        
        # 性能监控相关
        self._stream_start_time = time.time()
        self._first_token_time = None
        
        # 启动心跳检测任务
        if heartbeat_interval > 0:
            self._heartbeat_task = asyncio.create_task(self._heartbeat_check())
            logger.debug(f"启动了心跳检测任务，间隔为{heartbeat_interval}秒，超时为{self.timeout}秒")
        
    async def _heartbeat_check(self):
        """心跳检测，检查流是否长时间没有活动"""
        try:
            warnings_count = 0
            max_warnings = 2
            
            while not self._closed and self._stream_active:
                await asyncio.sleep(self._heartbeat_interval)
                
                # 如果已经开始关闭，直接退出
                if self._closing:
                    logger.debug("心跳检测发现流正在关闭中，退出检测")
                    break
                    
                current_time = asyncio.get_event_loop().time()
                elapsed_time = current_time - self._last_activity
                
                # 检测无活动时间是否接近超时
                if elapsed_time > (self.timeout * 0.7) and warnings_count < max_warnings:
                    warnings_count += 1
                    logger.warning(f"流式响应 {elapsed_time:.1f} 秒无活动，接近超时值 {self.timeout}秒 ({warnings_count}/{max_warnings})")
                
                # 检测超时
                if elapsed_time > self.timeout:
                    logger.warning(f"流式响应心跳超时（{elapsed_time:.1f}秒无活动，超出限制{self.timeout}秒），已处理{self._received_data_count}条数据")
                    self._stream_active = False
                    await self.close()
                    break
        except asyncio.CancelledError:
            logger.debug("心跳检测任务被取消")
        except Exception as e:
            logger.error(f"心跳检测任务出错: {str(e)}", exc_info=True)
        
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        if self._closed:
            logger.debug("流已关闭，停止迭代")
            raise StopAsyncIteration
            
        if not self._stream_active:
            logger.debug("流不再活跃，停止迭代")
            await self.close()
            raise StopAsyncIteration
            
        try:
            # 添加超时控制
            async with asyncio.timeout(self.timeout):
                async for line in self.response.content:
                    # 更新最后活动时间
                    self._last_activity = asyncio.get_event_loop().time()
                    self._received_data_count += 1
                    
                    # 跳过空行
                    if not line or len(line.strip()) == 0:
                        continue
                        
                    try:
                        # 记录原始响应数据以便调试
                        decoded_line = line.decode('utf-8').strip()
                        # logger.debug(f"Raw response line: {decoded_line}")
                        
                        # 检查是否为结束标记
                        if '[DONE]' in decoded_line or decoded_line == 'data: [DONE]':
                            logger.debug(f"检测到流结束标记，正常关闭流，已处理{self._received_data_count}条数据")
                            # 主动关闭流并停止迭代
                            self._stream_active = False
                            await self.close()
                            raise StopAsyncIteration
                        
                        result = await self.process_func(line)
                        
                        # 记录首个token时间
                        if result and self._first_token_time is None:
                            self._first_token_time = time.time()
                            first_token_latency = (self._first_token_time - self._stream_start_time) * 1000
                            logger.info(f"LLM首个token响应时间: {first_token_latency:.2f}ms")
                        
                        if result:
                            return result
                    except json.JSONDecodeError as je:
                        logger.warning(f"无法解析JSON响应: {line.decode('utf-8', errors='replace')} - 错误: {str(je)}")
                        continue
                    except Exception as e:
                        # 特殊处理[DONE]标记，避免将正常结束视为错误
                        decoded_line = line.decode('utf-8', errors='replace').strip()
                        if '[DONE]' in decoded_line or decoded_line == 'data: [DONE]':
                            logger.debug(f"在异常处理中检测到流结束标记[DONE]，正常关闭流，已处理{self._received_data_count}条数据")
                            self._stream_active = False
                            await self.close()
                            raise StopAsyncIteration
                        
                        logger.error(f"处理响应行时出错: {decoded_line} - 错误: {str(e)}")
                        continue
                        
                # 正常结束流
                logger.debug("流式响应内容已全部处理完毕")
                await self.close()
                raise StopAsyncIteration
        except asyncio.TimeoutError:
            logger.error(f"流式响应超时（{self.timeout}秒）")
            await self.close()
            raise LLMServiceTimeoutError(f"流式响应超时（{self.timeout}秒）")
        except aiohttp.ClientPayloadError as e:
            logger.error(f"读取响应内容错误: {str(e)}")
            await self.close()
            raise LLMServiceRequestError(f"读取响应内容错误: {str(e)}")
        except aiohttp.ClientConnectionError as e:
            logger.error(f"连接中断: {str(e)}")
            await self.close()
            raise LLMServiceUnavailableError(f"连接中断: {str(e)}")
        except StopAsyncIteration:
            # 直接向上传递StopAsyncIteration异常
            raise
        except Exception as e:
            logger.error(f"读取响应流时出错: {str(e)}", exc_info=True)
            await self.close()
            raise LLMServiceError(f"读取响应流时出错: {str(e)}")
        
    async def close(self):
        """关闭响应和会话"""
        if self._closed or self._closing:
            return
            
        self._closing = True
        logger.debug(f"正在关闭流式响应资源，已处理{self._received_data_count}条数据")
        
        # 取消心跳检测任务
        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.warning(f"取消心跳任务时出错: {str(e)}")
            
        try:
            # 关闭响应对象
            if hasattr(self, 'response') and self.response and not self.response.closed:
                self.response.close()
                logger.debug("响应对象已关闭")
                
            # 关闭会话对象
            if hasattr(self, '_session') and self._session and not self._session.closed:
                await self._session.close()
                logger.debug("会话对象已关闭")
                
            self._closed = True
            self._closing = False
        except Exception as e:
            self._closed = True
            self._closing = False
            logger.warning(f"关闭流式响应资源时出错: {str(e)}")
            
    def __del__(self):
        """析构函数，确保资源被释放"""
        if not self._closed:
            try:
                # 在 macOS 上避免在析构函数中创建新任务，防止资源泄漏
                import platform
                if platform.system() == 'Darwin':
                    # macOS 上只进行同步清理
                    self._closed = True
                    if hasattr(self, '_heartbeat_task') and self._heartbeat_task:
                        self._heartbeat_task.cancel()
                    logger.debug("在析构函数中进行同步清理（macOS 优化）")
                else:
                    # 其他系统保持原有逻辑
                    if asyncio.get_event_loop().is_running():
                        asyncio.create_task(self.close())
                        logger.debug("在析构函数中创建关闭任务")
            except Exception as e:
                logger.warning(f"在析构函数中调用close()时出错: {str(e)}")

class SiliconFlowService(BaseLLMService):
    """硅基流动API服务实现类"""
    
    def __init__(
        self,
        api_key: str,
        model: str = "qwen1.5-7b-chat",
        embedding_model: str = "bge-large-zh",
        base_url: str = "https://api.siliconflow.cn",
        temperature: float = 0.3,
        top_p: float = 0.8,
        request_timeout: float = 60.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """初始化硅基流动API服务
        
        Args:
            api_key: API密钥
            model: 对话模型名称
            embedding_model: 向量模型名称
            base_url: API基础地址
            temperature: 温度参数
            top_p: 核采样参数
            request_timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试间隔时间（秒）
        """
        self.api_key = api_key
        self.model = model
        self.embedding_model = embedding_model
        self.base_url = base_url.rstrip('/')
        self.temperature = temperature
        self.top_p = top_p
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
    def get_model_max_tokens(self) -> int:
        """获取当前模型的最大上下文窗口大小
        
        Returns:
            最大token数量
        """
        # 尝试从模型映射中获取，如果不存在则使用默认值
        return settings.LLM_MODEL_MAX_TOKENS.get(self.model, settings.LLM_MAX_TOKENS)
        
    async def _process_stream_line(self, line: bytes) -> Optional[str]:
        """处理流式响应的单行数据"""
        try:
            # 移除 'data: ' 前缀
            line_str = line.decode('utf-8', errors='replace').strip()
            
            # 记录原始数据（调试用）
            # logger.debug(f"Processing stream line: {line_str}")
            
            # 处理可能的前缀
            if line_str.startswith('data: '):
                line_str = line_str[6:]
            
            # 处理特殊标记
            if line_str == '[DONE]' or line_str == '' or line_str == 'data: [DONE]':
                logger.debug('收到流结束标记')
                return None
                
            # 解析JSON
            try:
                # 首先检查是否为[DONE]标记，避免尝试解析非JSON内容
                if '[DONE]' in line_str:
                    logger.debug('在JSON解析前检测到[DONE]标记')
                    return None
                
                chunk = json.loads(line_str)
                # logger.debug(f"解析后的JSON: {json.dumps(chunk, ensure_ascii=False)}")
                
                # 提取内容 - 处理不同的响应格式
                content = None
                
                # 标准OpenAI格式处理
                if 'choices' in chunk and len(chunk['choices']) > 0:
                    choice = chunk['choices'][0]
                    if 'delta' in choice and 'content' in choice['delta'] and choice['delta']['content']:
                        content = choice['delta']['content']
                    elif 'text' in choice and choice['text']:
                        content = choice['text']
                    elif 'message' in choice and 'content' in choice['message'] and choice['message']['content']:
                        content = choice['message']['content']
                    
                    # 检查是否为流结束的标志
                    if 'finish_reason' in choice and choice['finish_reason'] is not None:
                        logger.debug(f'检测到完成标志: finish_reason={choice["finish_reason"]}')
                        if not content:  # 如果没有内容，可能是最后一个消息
                            return None
                
                # 其他可能的格式
                elif 'content' in chunk:
                    content = chunk['content']
                elif 'text' in chunk:
                    content = chunk['text']
                
                # 如果找到内容，返回处理后的内容
                if content:
                    processed_content = PromptTemplates.create_stream_prompt(content)
                    # logger.debug(f"流式内容: {processed_content}")
                    return processed_content
                    
                # 没有找到内容，继续处理下一行
                # logger.debug("未找到内容，跳过")
                return None
                
            except json.JSONDecodeError as je:
                # 非JSON行，可能是服务器发送的其他信息
                if '[DONE]' in line_str:
                    logger.debug('在JSON解析错误中检测到结束标记[DONE]')
                    return None
                logger.warning(f'无法解析JSON (行内容: {line_str}): {str(je)}')
                return None
        except Exception as e:
            # 捕获所有异常，确保流处理不会因为单行解析错误而中断
            logger.warning(f"处理流式响应行时出现未预期的错误: {str(e)}")
            if isinstance(line, bytes):
                decoded_line = line.decode('utf-8', errors='replace').strip()
                if '[DONE]' in decoded_line:
                    logger.debug("在全局异常处理中检测到[DONE]标记")
                    return None
            return None

    async def _make_request_stream(self, prompt: str) -> AsyncStreamResponse:
        """发送流式请求（带重试）"""
        last_exception = None
        backoff_factor = 1.0
        
        # 性能监控
        request_start_time = time.time()
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/v1/chat/completions"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }
                
                # 准备请求数据
                data = {
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "stream": True,
                    "temperature": self.temperature,
                    "top_p": self.top_p,
                    "max_tokens": self.get_model_max_tokens()  # 使用配置的最大token数
                }
                
                # 记录请求信息（不包含敏感数据）
                logger.debug(f"Stream request to model: {self.model}, url: {url}")
                logger.debug(f"Request parameters: temperature={self.temperature}, top_p={self.top_p}")
                
                # 计算当前请求的超时时间（考虑重试次数）
                # 流式请求使用更长的超时时间
                stream_timeout = getattr(settings, "LLM_STREAM_TIMEOUT", self.request_timeout * 2)
                current_timeout = min(stream_timeout * (1 + attempt * 0.5), stream_timeout * 2)
                logger.debug(f"流式请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                timeout = aiohttp.ClientTimeout(total=current_timeout)
                session = aiohttp.ClientSession(timeout=timeout)
                try:
                    logger.debug(f"发送流式请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                    response = await session.post(url, headers=headers, json=data)
                    
                    # 记录响应时间
                    response_time = time.time()
                    connection_latency = (response_time - request_start_time) * 1000
                    logger.info(f"LLM服务连接耗时: {connection_latency:.2f}ms (尝试 {attempt+1}/{self.max_retries})")
                    
                    if response.status != 200:
                        error_text = await response.text()
                        error_msg = f"LLM服务返回错误: 状态码={response.status}, 内容={error_text}"
                        logger.error(error_msg)
                        
                        # 根据状态码处理不同类型的错误
                        if response.status == 401:
                            raise LLMServiceError(f"API密钥无效或已过期: {error_text}", status_code=401)
                        elif response.status == 404:
                            raise LLMServiceRequestError(f"模型 '{self.model}' 不存在: {error_text}")
                        elif response.status == 400:
                            raise LLMServiceRequestError(f"请求参数错误: {error_text}")
                        elif response.status == 429:
                            raise LLMServiceError(f"请求频率超限: {error_text}", status_code=429)
                        elif response.status == 500:
                            raise LLMServiceError(f"SiliconFlow服务内部错误: {error_text}")
                        elif response.status == 503:
                            raise LLMServiceUnavailableError(f"SiliconFlow服务不可用: {error_text}")
                        else:
                            raise LLMServiceRequestError(f"API请求失败: {response.status} - {error_text}")
                        
                    # 检查响应头，确认是否为流式响应
                    content_type = response.headers.get('Content-Type', '')
                    if 'text/event-stream' not in content_type and 'application/x-ndjson' not in content_type:
                        logger.warning(f"预期流式响应，但收到了内容类型: {content_type}")
                    
                    logger.debug(f"成功建立流式连接（尝试 {attempt+1}/{self.max_retries}）")
                    stream_response = AsyncStreamResponse(response, self._process_stream_line, timeout=current_timeout)
                    stream_response._session = session
                    return stream_response
                except aiohttp.ClientConnectorError as e:
                    await session.close()
                    raise LLMServiceUnavailableError(f"无法连接到SiliconFlow服务: {str(e)}")
                except aiohttp.ClientOSError as e:
                    await session.close()
                    raise LLMServiceUnavailableError(f"连接到SiliconFlow服务时出现操作系统错误: {str(e)}")
                except aiohttp.ServerDisconnectedError as e:
                    await session.close()
                    raise LLMServiceUnavailableError(f"SiliconFlow服务断开连接: {str(e)}")
                except asyncio.TimeoutError as e:
                    await session.close()
                    raise LLMServiceTimeoutError(f"连接SiliconFlow服务超时（{current_timeout}秒）")
                except Exception as e:
                    await session.close()
                    raise LLMServiceError(f"创建流式请求时出错: {str(e)}")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"SiliconFlow服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"SiliconFlow服务不可用，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceTimeoutError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"连接SiliconFlow服务超时（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"连接SiliconFlow服务超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                # 对于某些错误，不进行重试
                if getattr(e, "status_code", 500) in [401, 403, 429]:
                    logger.error(f"SiliconFlow服务错误（不再重试）: {str(e)}")
                    raise
                    
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"流式请求失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"流式请求失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有重试尝试均失败，但未捕获到具体异常")

    async def _make_request_normal(self, prompt: str) -> str:
        """发送普通请求（非流式，带重试）"""
        last_exception = None
        backoff_factor = 1.0
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/v1/chat/completions"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }
                data = {
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "stream": False,
                    "temperature": self.temperature,
                    "top_p": self.top_p,
                    "max_tokens": self.get_model_max_tokens()  # 使用配置的最大token数
                }
                
                # 记录请求信息（不包含敏感数据）
                logger.debug(f"Normal request to model: {self.model}, url: {url}")
                logger.debug(f"Request parameters: temperature={self.temperature}, top_p={self.top_p}")
                
                # 计算当前请求的超时时间（考虑重试次数）
                current_timeout = min(self.request_timeout * (1 + attempt * 0.5), self.request_timeout * 2)
                logger.debug(f"普通请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                timeout = aiohttp.ClientTimeout(total=current_timeout)
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    try:
                        logger.debug(f"发送请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                        async with session.post(url, headers=headers, json=data) as response:
                            if response.status != 200:
                                error_text = await response.text()
                                
                                # 记录完整响应以便调试
                                logger.error(f"API请求失败: 状态码 {response.status}, 响应: {error_text}")
                                
                                # 根据状态码处理不同类型的错误
                                if response.status == 401:
                                    raise LLMServiceError(f"API密钥无效或已过期: {error_text}", status_code=401)
                                elif response.status == 404:
                                    raise LLMServiceRequestError(f"模型 '{self.model}' 不存在: {error_text}")
                                elif response.status == 400:
                                    raise LLMServiceRequestError(f"请求参数错误: {error_text}")
                                elif response.status == 429:
                                    raise LLMServiceError(f"请求频率超限: {error_text}", status_code=429)
                                elif response.status == 500:
                                    raise LLMServiceError(f"SiliconFlow服务内部错误: {error_text}")
                                elif response.status == 503:
                                    raise LLMServiceUnavailableError(f"SiliconFlow服务不可用: {error_text}")
                                else:
                                    raise LLMServiceRequestError(f"API请求失败: {response.status} - {error_text}")
                                
                            result = await response.json()
                            
                            # 记录响应信息以便调试
                            logger.debug(f"成功接收响应（尝试 {attempt+1}/{self.max_retries}）")
                            
                            # 验证响应格式
                            if not result.get("choices") or not result["choices"][0].get("message", {}).get("content"):
                                logger.error(f"API返回的响应格式无效: {json.dumps(result, ensure_ascii=False)}")
                                raise LLMServiceError("API返回的响应格式无效")
                                
                            response_text = result["choices"][0]["message"]["content"]
                            logger.debug(f"响应长度: {len(response_text)} 字符")
                            return response_text
                    except aiohttp.ClientConnectorError as e:
                        raise LLMServiceUnavailableError(f"无法连接到SiliconFlow服务: {str(e)}")
                    except aiohttp.ClientOSError as e:
                        raise LLMServiceUnavailableError(f"连接到SiliconFlow服务时出现操作系统错误: {str(e)}")
                    except aiohttp.ServerDisconnectedError as e:
                        raise LLMServiceUnavailableError(f"SiliconFlow服务断开连接: {str(e)}")
                    except asyncio.TimeoutError as e:
                        raise LLMServiceTimeoutError(f"请求SiliconFlow服务超时（{current_timeout}秒）")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"SiliconFlow服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"SiliconFlow服务不可用，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceTimeoutError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"请求SiliconFlow服务超时（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"请求SiliconFlow服务超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                # 对于某些错误，不进行重试
                if getattr(e, "status_code", 500) in [401, 403, 429]:
                    logger.error(f"SiliconFlow服务错误（不再重试）: {str(e)}")
                    raise
                    
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"请求失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"请求失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except Exception as e:
                last_exception = LLMServiceError(f"请求出错: {str(e)}")
                if attempt == self.max_retries - 1:
                    logger.error(f"请求出错（已重试{attempt+1}次）: {str(e)}")
                    raise last_exception
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"请求出错，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有重试尝试均失败，但未捕获到具体异常")

    async def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示（带重试）
        
        Args:
            text: 输入文本
            
        Returns:
            文本的向量表示
        """
        last_exception = None
        backoff_factor = 1.0
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/v1/embeddings"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }
                data = {
                    "model": self.embedding_model,
                    "input": text
                }
                
                # 计算当前请求的超时时间（考虑重试次数）
                current_timeout = min(self.request_timeout * (1 + attempt * 0.5), self.request_timeout * 2)
                logger.debug(f"获取向量请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                timeout = aiohttp.ClientTimeout(total=current_timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    try:
                        logger.debug(f"发送向量请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                        async with session.post(url, headers=headers, json=data) as response:
                            if response.status != 200:
                                error_text = await response.text()
                                
                                # 根据状态码处理不同类型的错误
                                if response.status == 401:
                                    raise LLMServiceError(f"API密钥无效或已过期: {error_text}", status_code=401)
                                elif response.status == 404:
                                    raise LLMServiceRequestError(f"向量模型 '{self.embedding_model}' 不存在: {error_text}")
                                elif response.status == 400:
                                    raise LLMServiceRequestError(f"向量请求参数错误: {error_text}")
                                elif response.status == 429:
                                    raise LLMServiceError(f"请求频率超限: {error_text}", status_code=429)
                                elif response.status == 500:
                                    raise LLMServiceError(f"SiliconFlow服务内部错误: {error_text}")
                                elif response.status == 503:
                                    raise LLMServiceUnavailableError(f"SiliconFlow服务不可用: {error_text}")
                                else:
                                    raise LLMServiceRequestError(f"获取向量失败: {response.status} - {error_text}")
                                
                            result = await response.json()
                            if not result.get("data") or not result["data"][0].get("embedding"):
                                raise LLMServiceError("API返回的向量格式无效")
                                
                            logger.debug(f"成功获取向量（尝试 {attempt+1}/{self.max_retries}）")
                            return result["data"][0]["embedding"]
                    except aiohttp.ClientConnectorError as e:
                        raise LLMServiceUnavailableError(f"无法连接到SiliconFlow服务: {str(e)}")
                    except aiohttp.ClientOSError as e:
                        raise LLMServiceUnavailableError(f"连接到SiliconFlow服务时出现操作系统错误: {str(e)}")
                    except aiohttp.ServerDisconnectedError as e:
                        raise LLMServiceUnavailableError(f"SiliconFlow服务断开连接: {str(e)}")
                    except asyncio.TimeoutError as e:
                        raise LLMServiceTimeoutError(f"获取向量超时（{current_timeout}秒）")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"SiliconFlow服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"SiliconFlow服务不可用，将在 {wait_time:.2f} 秒后重试获取向量（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceTimeoutError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量超时（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"获取向量超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                # 对于某些错误，不进行重试
                if getattr(e, "status_code", 500) in [401, 403, 429]:
                    logger.error(f"SiliconFlow服务错误（不再重试）: {str(e)}")
                    raise
                    
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"获取向量失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except Exception as e:
                last_exception = LLMServiceError(f"获取向量时出错: {str(e)}")
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量时出错（已重试{attempt+1}次）: {str(e)}")
                    raise last_exception
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"获取向量时出错，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有获取向量的尝试均失败，但未捕获到具体异常")

    async def generate(
        self,
        prompt: str,
        stream: bool = False
    ) -> Union[str, AsyncIterator[str]]:
        """生成回答
        
        Args:
            prompt: 提示词
            stream: 是否使用流式输出
            
        Returns:
            响应文本或文本生成器
        """
        try:
            if stream:
                stream_response = await self._make_request_stream(prompt)
                return stream_response
            else:
                return await self._make_request_normal(prompt)
        except LLMServiceTimeoutError:
            # 超时错误直接向上传递，不做特殊处理
            raise
        except LLMServiceUnavailableError:
            # 服务不可用错误直接向上传递，不做特殊处理
            raise
        except LLMServiceRequestError:
            # 请求错误直接向上传递，不做特殊处理
            raise
        except Exception as e:
            logger.error(f"生成回答时出错: {str(e)}")
            if stream:
                # 流式响应必须抛出异常，由上层处理
                raise LLMServiceError(f"生成流式回答失败: {str(e)}")
            else:
                # 非流式响应可以返回错误信息
                return "抱歉，生成回答时出现技术故障，请稍后再试。"
    
    async def generate_with_context(
        self,
        messages: List[Dict[str, str]],
        knowledge: Optional[List[Dict[str, str]]] = None,
        stream: bool = False
    ) -> Union[str, AsyncIterator[str]]:
        """带上下文的生成
        
        Args:
            messages: 历史消息列表
            knowledge: 知识片段列表
            stream: 是否使用流式输出
            
        Returns:
            响应文本或文本生成器
        """
        try:
            prompt = PromptTemplates.create_chat_prompt(
                messages, 
                knowledge,
                model_name=self.model  # 传入当前模型名称
            )
            if stream:
                stream_response = await self._make_request_stream(prompt)
                return stream_response
            else:
                return await self._make_request_normal(prompt)
        except LLMServiceTimeoutError:
            # 超时错误直接向上传递，不做特殊处理
            raise
        except LLMServiceUnavailableError:
            # 服务不可用错误直接向上传递，不做特殊处理
            raise
        except LLMServiceRequestError:
            # 请求错误直接向上传递，不做特殊处理
            raise
        except Exception as e:
            logger.error(f"带上下文生成时出错: {str(e)}")
            if stream:
                # 流式响应必须抛出异常，由上层处理
                raise LLMServiceError(f"生成流式上下文回答失败: {str(e)}")
            else:
                # 非流式响应可以返回错误信息
                return "抱歉，生成回答时出现技术故障，请稍后再试。"
    
    async def check_service_status(self) -> Dict[str, Union[bool, str, List[str]]]:
        """检查SiliconFlow服务状态
        
        Returns:
            服务状态信息，包含以下字段：
            - available: 服务是否可用
            - model_available: 当前配置的模型是否可用
            - models: 可用模型列表（如果服务支持）
            - message: 状态描述信息
        """
        try:
            # 检查模型列表API
            url = f"{self.base_url}/v1/models"
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }
            
            timeout = aiohttp.ClientTimeout(total=5)  # 短超时用于状态检查
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        return {
                            "available": False,
                            "message": f"服务返回错误状态码: {response.status}"
                        }
                    
                    result = await response.json()
                    models = [m.get("id") for m in result.get("data", [])]
                    
                    if not models:
                        return {
                            "available": True,
                            "message": "服务正常，但没有可用模型"
                        }
                    
                    model_available = self.model in models
                    current_model = self.model if model_available else ""
                    
                    return {
                        "available": True,
                        "model_available": model_available,
                        "models": models,
                        "current_model": current_model,
                        "message": "服务正常" if model_available else f"服务正常，但模型 {self.model} 不可用"
                    }
        except aiohttp.ClientConnectorError:
            return {
                "available": False,
                "message": "无法连接到SiliconFlow服务"
            }
        except Exception as e:
            return {
                "available": False,
                "message": f"服务检查失败: {str(e)}"
            } 