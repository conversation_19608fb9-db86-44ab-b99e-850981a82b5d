from abc import ABC, abstractmethod
from typing import AsyncIterator, List, Optional, Union, Dict

class BaseLLMService(ABC):
    """LLM服务的基础接口类，定义了与语言模型交互的标准方法"""
    
    # 默认超时和重试配置
    request_timeout: float = 60.0  # 请求超时时间（秒）
    max_retries: int = 3           # 最大重试次数
    retry_delay: float = 1.0       # 重试间隔时间（秒）
    
    @abstractmethod
    async def generate(self, prompt: str, stream: bool = False) -> Union[str, AsyncIterator[str]]:
        """根据提示词生成回答
        
        Args:
            prompt: 输入提示词
            stream: 是否使用流式输出
            
        Returns:
            完整回答文本或回答生成器
        """
        pass
    
    @abstractmethod
    async def generate_with_context(
        self,
        messages: List[Dict[str, str]],
        knowledge: Optional[List[Dict[str, str]]] = None,
        stream: bool = False
    ) -> Union[str, AsyncIterator[str]]:
        """基于历史消息和知识生成回答
        
        Args:
            messages: 历史对话消息列表
            knowledge: 相关的知识片段列表
            stream: 是否使用流式输出
            
        Returns:
            完整回答文本或回答生成器
        """
        pass

    @abstractmethod
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示
        
        Args:
            text: 输入文本
            
        Returns:
            文本的向量表示（浮点数列表）
        """
        pass
        
    @abstractmethod
    async def check_service_status(self) -> Dict[str, Union[bool, str, List[str]]]:
        """检查LLM服务状态
        
        Returns:
            服务状态信息，包含以下字段：
            - available: 服务是否可用
            - model_available: 当前配置的模型是否可用
            - models: 可用模型列表（如果服务支持）
            - message: 状态描述信息
        """
        pass 