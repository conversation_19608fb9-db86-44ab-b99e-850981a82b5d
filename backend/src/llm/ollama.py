import json
import aiohttp
import asyncio
import time
import logging
import numpy as np
from typing import AsyncIterator, Dict, List, Optional, Tuple, Union, Any
from ..core.logging import get_module_logger
from ..core.config import settings
from .base import BaseLLMService
from .prompts import PromptTemplates
from ..core.exceptions import (
    LLMServiceError, 
    LLMServiceTimeoutError, 
    LLMServiceUnavailableError,
    LLMServiceRequestError
)

# 初始化模块级logger
logger = get_module_logger(__name__)

class AsyncStreamIterator:
    """异步流迭代器"""
    def __init__(self, response, session, timeout=None, heartbeat_interval=5):
        self.response = response
        self.session = session
        self._buffer = []
        self._done = False
        # 如果未指定超时时间，则使用配置中的流式超时时间或默认值
        if timeout is None:
            self.timeout = getattr(settings, "LLM_STREAM_TIMEOUT", 120.0)
        else:
            self.timeout = timeout
        self._closed = False
        self._last_activity = asyncio.get_event_loop().time()
        self._heartbeat_interval = heartbeat_interval
        self._heartbeat_task = None
        
        # 启动心跳检测任务
        if heartbeat_interval > 0:
            self._heartbeat_task = asyncio.create_task(self._heartbeat_check())
        
    async def _heartbeat_check(self):
        """心跳检测，检查流是否长时间没有活动"""
        try:
            while not self._closed and not self._done:
                await asyncio.sleep(self._heartbeat_interval)
                current_time = asyncio.get_event_loop().time()
                if current_time - self._last_activity > self.timeout:
                    logger.warning(f"流式响应心跳超时（{self.timeout}秒无活动）")
                    self._done = True
                    await self.close()
                    break
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"心跳检测任务出错: {str(e)}")
            
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        if self._closed:
            raise StopAsyncIteration
            
        if self._done and not self._buffer:
            await self.close()
            raise StopAsyncIteration
            
        if self._buffer:
            return self._buffer.pop(0)
        
        try:
            # 添加超时控制
            async with asyncio.timeout(self.timeout):
                async for line in self.response.content:
                    # 更新最后活动时间
                    self._last_activity = asyncio.get_event_loop().time()
                    
                    if line:
                        try:
                            chunk = json.loads(line)
                            if chunk.get("done", False):
                                self._done = True
                                if not self._buffer:
                                    await self.close()
                                    raise StopAsyncIteration
                                return self._buffer.pop(0)
                                
                            if "response" in chunk and chunk["response"]:
                                # 添加调试日志，打印原始响应
                                # logger.debug(f"原始响应: {repr(chunk['response'])}")
                                # 直接返回原始响应，保留所有换行符
                                return chunk["response"]
                        except json.JSONDecodeError:
                            logger.warning("无法解析JSON响应")
                            continue
        except StopAsyncIteration:
            # 正确处理StopAsyncIteration异常，表示迭代结束
            self._done = True
            await self.close()
            raise  # 重新抛出异常，不作为错误处理
        except asyncio.TimeoutError:
            logger.error(f"流式响应超时（{self.timeout}秒）")
            self._done = True
            await self.close()
            raise LLMServiceTimeoutError(f"流式响应超时（{self.timeout}秒）")
        except aiohttp.ClientPayloadError as e:
            logger.error(f"读取响应内容错误: {str(e)}")
            self._done = True
            await self.close()
            raise LLMServiceRequestError(f"读取响应内容错误: {str(e)}")
        except aiohttp.ClientConnectionError as e:
            logger.error(f"连接中断: {str(e)}")
            self._done = True
            await self.close()
            raise LLMServiceUnavailableError(f"连接中断: {str(e)}")
        except Exception as e:
            # 增强异常日志记录，包括异常类型和详细信息
            error_type = type(e).__name__
            error_detail = str(e) or '无详细错误信息'
            error_repr = repr(e)
            logger.error(f'流式响应处理错误: 类型={error_type}, 详情={error_detail}, 对象={error_repr}')
            self._done = True
            await self.close()
            raise LLMServiceError(f'流式响应处理错误: 类型={error_type}, 详情={error_detail}')
                    
        self._done = True
        await self.close()
        raise StopAsyncIteration
        
    async def close(self):
        """关闭响应和会话"""
        if self._closed:
            return
            
        self._closed = True
        
        # 取消心跳检测任务
        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass
            
        try:
            if not self.response.closed:
                self.response.close()
            if self.session and not self.session.closed:
                await self.session.close()
        except Exception as e:
            logger.warning(f"关闭流式响应时出错: {str(e)}")
            
    def __del__(self):
        """析构函数，确保资源被释放"""
        if not self._closed:
            try:
                # 在 macOS 上避免在析构函数中创建新任务，防止资源泄漏
                import platform
                if platform.system() == 'Darwin':
                    # macOS 上只进行同步清理
                    self._closed = True
                    if hasattr(self, '_heartbeat_task') and self._heartbeat_task:
                        self._heartbeat_task.cancel()
                    logger.debug("在析构函数中进行同步清理（macOS 优化）")
                else:
                    # 其他系统保持原有逻辑
                    if asyncio.get_event_loop().is_running():
                        asyncio.create_task(self.close())
            except Exception as e:
                logger.warning(f"在析构函数中清理资源时出错: {str(e)}")

class ConsistencyStreamWrapper:
    """流式响应一致性包装器
    
    用于处理流式输出的文本一致性，包括：
    - 标点符号规范化
    - 句子完整性检查
    - 中英文标点统一
    """
    
    def __init__(self, stream_iterator: AsyncStreamIterator, consistency_mode: bool = False):
        """初始化流式响应包装器
        
        Args:
            stream_iterator: 原始流式迭代器
            consistency_mode: 是否启用一致性模式
        """
        self.stream_iterator = stream_iterator
        self.consistency_mode = consistency_mode
        self._buffer = ""
        self._is_first_chunk = True
        self._is_last_chunk = False
        
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        try:
            chunk = await self.stream_iterator.__anext__()
            
            if not self.consistency_mode:
                return chunk
                
            # 处理第一个块
            if self._is_first_chunk:
                self._is_first_chunk = False
                # 移除可能的前导空白
                chunk = chunk.lstrip()
                
            # 将块添加到缓冲区
            self._buffer += chunk
            
            # 返回当前块
            return chunk
        except StopAsyncIteration:
            # 处理最后一个文本块
            if self.consistency_mode and self._buffer and not self._is_last_chunk:
                self._is_last_chunk = True
                
                # 确保回答以完整的句子结束
                if not self._buffer.endswith(("。", "！", "？", ".", "!", "?")):
                    # 根据上下文选择合适的结束标点
                    if any(char in self._buffer[-20:] for char in "，。？！；：""''（）【】《》"):
                        return "。"
                    else:
                        return "."
                        
            raise StopAsyncIteration
            
    async def close(self):
        """关闭流式迭代器，释放资源"""
        await self.stream_iterator.close()

class OllamaService(BaseLLMService):
    """Ollama服务实现类
    
    提供与Ollama模型交互的具体实现，包括：
    - 模型参数配置
    - 流式输出处理
    - 响应一致性处理
    - 错误重试机制
    """
    
    def __init__(
        self,
        model: str = "qwen:7b",
        base_url: str = "http://localhost:11434",
        temperature: float = 0.3,  # 降低默认温度，减少随机性
        top_p: float = 0.8,  # 略微降低top_p值
        request_timeout: float = 60.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        consistency_mode: bool = True,  # 添加一致性模式开关
    ):
        """初始化Ollama服务
        
        Args:
            model: 模型名称
            base_url: Ollama服务地址
            temperature: 温度参数，控制随机性，较低的值会使输出更加确定性
            top_p: 核采样参数
            request_timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            consistency_mode: 是否启用一致性模式，启用后会添加额外的一致性提示
        """
        self.model = model
        self.base_url = base_url.rstrip('/')
        self.temperature = temperature
        self.top_p = top_p
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.consistency_mode = consistency_mode
        
    def get_model_max_tokens(self) -> int:
        """获取当前模型的最大上下文窗口大小
        
        Returns:
            最大token数量
        """
        # 尝试从模型映射中获取，如果不存在则使用默认值
        return settings.LLM_MODEL_MAX_TOKENS.get(self.model, settings.LLM_MAX_TOKENS)
        
    async def _make_request_stream(
        self,
        prompt: str
    ) -> AsyncStreamIterator:
        """发送流式请求到Ollama服务（带重试）"""
        last_exception = None
        backoff_factor = 1.0
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/api/generate"
                headers = {"Content-Type": "application/json"}
                data = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": True,
                    "options": {
                        "temperature": self.temperature,
                        "top_p": self.top_p,
                        "num_predict": self.get_model_max_tokens(),  # 使用配置的最大token数
                        "stop": ["<|im_end|>", "<|endoftext|>"]  # 添加通用停止标记
                    }
                }
                
                # 计算当前请求的超时时间（考虑重试次数）
                # 流式请求使用更长的超时时间
                stream_timeout = getattr(settings, "LLM_STREAM_TIMEOUT", self.request_timeout * 2)
                current_timeout = min(stream_timeout * (1 + attempt * 0.5), stream_timeout * 2)
                logger.debug(f"流式请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=current_timeout))
                try:
                    logger.debug(f"发送流式请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                    response = await session.post(url, headers=headers, json=data)
                    
                    if response.status != 200:
                        error_text = await response.text()
                        await session.close()
                        
                        # 根据状态码处理不同类型的错误
                        if response.status == 404:
                            raise LLMServiceRequestError(f"模型 '{self.model}' 不存在")
                        elif response.status == 400:
                            raise LLMServiceRequestError(f"请求参数错误: {error_text}")
                        elif response.status == 500:
                            raise LLMServiceError(f"Ollama服务内部错误: {error_text}")
                        elif response.status == 503:
                            raise LLMServiceUnavailableError(f"Ollama服务不可用: {error_text}")
                        else:
                            raise LLMServiceRequestError(f"API请求失败: {response.status} - {error_text}")
                        
                    logger.debug(f"成功建立流式连接（尝试 {attempt+1}/{self.max_retries}）")
                    return AsyncStreamIterator(response, session, timeout=current_timeout)
                except aiohttp.ClientConnectorError as e:
                    await session.close()
                    error_detail = str(e) or "无详细错误信息"
                    logger.error(f"无法连接到Ollama服务: {error_detail}")
                    raise LLMServiceUnavailableError(f"无法连接到Ollama服务: {error_detail}")
                except aiohttp.ClientOSError as e:
                    await session.close()
                    error_detail = str(e) or "无详细错误信息"
                    logger.error(f"连接到Ollama服务时出现操作系统错误: {error_detail}")
                    raise LLMServiceUnavailableError(f"连接到Ollama服务时出现操作系统错误: {error_detail}")
                except aiohttp.ServerDisconnectedError as e:
                    await session.close()
                    error_detail = str(e) or "无详细错误信息"
                    logger.error(f"Ollama服务断开连接: {error_detail}")
                    raise LLMServiceUnavailableError(f"Ollama服务断开连接: {error_detail}")
                except asyncio.TimeoutError as e:
                    await session.close()
                    raise LLMServiceTimeoutError(f"连接Ollama服务超时（{current_timeout}秒）")
                except Exception as e:
                    await session.close()
                    error_type = type(e).__name__
                    error_detail = str(e) or "无详细错误信息"
                    error_repr = repr(e)
                    logger.error(f"创建流式请求时出错: 类型={error_type}, 详情={error_detail}, 对象={error_repr}")
                    raise LLMServiceError(f"创建流式请求时出错: 类型={error_type}, 详情={error_detail}")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"Ollama服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"Ollama服务不可用，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceTimeoutError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"连接Ollama服务超时（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"连接Ollama服务超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"流式请求失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"流式请求失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有重试尝试均失败，但未捕获到具体异常")

    async def _make_request_normal(
        self,
        prompt: str
    ) -> str:
        """发送普通请求到Ollama服务（带重试）"""
        last_exception = None
        backoff_factor = 1.0
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/api/generate"
                headers = {"Content-Type": "application/json"}
                data = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": self.temperature,
                        "top_p": self.top_p,
                        "num_predict": self.get_model_max_tokens(),  # 使用配置的最大token数
                        "stop": ["<|im_end|>", "<|endoftext|>"]  # 添加通用停止标记
                    }
                }
                
                # 计算当前请求的超时时间（考虑重试次数）
                current_timeout = min(self.request_timeout * (1 + attempt * 0.5), self.request_timeout * 2)
                logger.debug(f"请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                timeout = aiohttp.ClientTimeout(total=current_timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    try:
                        logger.debug(f"发送请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                        async with session.post(url, headers=headers, json=data) as response:
                            if response.status != 200:
                                error_text = await response.text()
                                
                                # 根据状态码处理不同类型的错误
                                if response.status == 404:
                                    raise LLMServiceRequestError(f"模型 '{self.model}' 不存在")
                                elif response.status == 400:
                                    raise LLMServiceRequestError(f"请求参数错误: {error_text}")
                                elif response.status == 500:
                                    raise LLMServiceError(f"Ollama服务内部错误: {error_text}")
                                elif response.status == 503:
                                    raise LLMServiceUnavailableError(f"Ollama服务不可用: {error_text}")
                                else:
                                    raise LLMServiceRequestError(f"API请求失败: {response.status} - {error_text}")
                                
                            result = await response.json()
                            if "response" not in result or not result["response"]:
                                raise LLMServiceError("API返回的响应格式无效")
                                
                            logger.debug(f"成功接收响应（尝试 {attempt+1}/{self.max_retries}）")
                            return result["response"]
                    except aiohttp.ClientConnectorError as e:
                        raise LLMServiceUnavailableError(f"无法连接到Ollama服务: {str(e)}")
                    except aiohttp.ClientOSError as e:
                        raise LLMServiceUnavailableError(f"连接到Ollama服务时出现操作系统错误: {str(e)}")
                    except aiohttp.ServerDisconnectedError as e:
                        raise LLMServiceUnavailableError(f"Ollama服务断开连接: {str(e)}")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"Ollama服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"Ollama服务不可用，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"请求失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"请求失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except asyncio.TimeoutError:
                last_exception = LLMServiceTimeoutError(f"请求超时（{current_timeout}秒）")
                if attempt == self.max_retries - 1:
                    logger.error(f"请求超时（已重试{attempt+1}次）")
                    raise last_exception
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"请求超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）")
                await asyncio.sleep(wait_time)
            except Exception as e:
                last_exception = LLMServiceError(f"请求出错: {str(e)}")
                if attempt == self.max_retries - 1:
                    logger.error(f"请求出错（已重试{attempt+1}次）: {str(e)}")
                    raise last_exception
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"请求出错，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有重试尝试均失败，但未捕获到具体异常")

    async def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示（带重试）
        
        Args:
            text: 输入文本
            
        Returns:
            文本的向量表示
        """
        last_exception = None
        backoff_factor = 1.0
        
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/api/embeddings"
                headers = {"Content-Type": "application/json"}
                data = {
                    "model": self.model,
                    "prompt": text
                }
                
                # 计算当前请求的超时时间（考虑重试次数）
                current_timeout = min(self.request_timeout * (1 + attempt * 0.5), self.request_timeout * 2)
                logger.debug(f"获取向量请求超时设置为 {current_timeout}秒（尝试 {attempt+1}/{self.max_retries}）")
                
                timeout = aiohttp.ClientTimeout(total=current_timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    try:
                        logger.debug(f"发送向量请求到 {url}（尝试 {attempt+1}/{self.max_retries}）")
                        async with session.post(url, headers=headers, json=data) as response:
                            if response.status != 200:
                                error_text = await response.text()
                                
                                # 根据状态码处理不同类型的错误
                                if response.status == 404:
                                    raise LLMServiceRequestError(f"模型 '{self.model}' 不支持生成向量")
                                elif response.status == 400:
                                    raise LLMServiceRequestError(f"向量请求参数错误: {error_text}")
                                elif response.status == 500:
                                    raise LLMServiceError(f"Ollama服务内部错误: {error_text}")
                                elif response.status == 503:
                                    raise LLMServiceUnavailableError(f"Ollama服务不可用: {error_text}")
                                else:
                                    raise LLMServiceRequestError(f"获取向量失败: {response.status} - {error_text}")
                                
                            result = await response.json()
                            if not result.get("embedding"):
                                raise LLMServiceError("API返回的向量格式无效")
                                
                            logger.debug(f"成功获取向量（尝试 {attempt+1}/{self.max_retries}）")
                            return result["embedding"]
                    except aiohttp.ClientConnectorError as e:
                        raise LLMServiceUnavailableError(f"无法连接到Ollama服务: {str(e)}")
                    except aiohttp.ClientOSError as e:
                        raise LLMServiceUnavailableError(f"连接到Ollama服务时出现操作系统错误: {str(e)}")
                    except aiohttp.ServerDisconnectedError as e:
                        raise LLMServiceUnavailableError(f"Ollama服务断开连接: {str(e)}")
                    except asyncio.TimeoutError as e:
                        raise LLMServiceTimeoutError(f"获取向量超时（{current_timeout}秒）")
            except LLMServiceUnavailableError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"Ollama服务不可用（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 使用指数退避策略
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"Ollama服务不可用，将在 {wait_time:.2f} 秒后重试获取向量（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceTimeoutError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量超时（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                # 超时错误使用更长的等待时间
                wait_time = self.retry_delay * (backoff_factor ** attempt) * 1.5
                logger.warning(f"获取向量超时，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except LLMServiceError as e:
                last_exception = e
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量失败（已重试{attempt+1}次）: {str(e)}")
                    raise
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"获取向量失败，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
            except Exception as e:
                last_exception = LLMServiceError(f"获取向量时出错: {str(e)}")
                if attempt == self.max_retries - 1:
                    logger.error(f"获取向量时出错（已重试{attempt+1}次）: {str(e)}")
                    raise last_exception
                
                wait_time = self.retry_delay * (backoff_factor ** attempt)
                logger.warning(f"获取向量时出错，将在 {wait_time:.2f} 秒后重试（{attempt+1}/{self.max_retries}）: {str(e)}")
                await asyncio.sleep(wait_time)
        
        # 如果所有重试都失败，但没有异常记录（不太可能发生）
        if last_exception:
            raise last_exception
        else:
            raise LLMServiceError("所有获取向量的尝试均失败，但未捕获到具体异常")

    async def _post_process_response(self, response: str) -> str:
        """对模型回答进行后处理，增强文本一致性
        
        Args:
            response: 原始回答文本
            
        Returns:
            处理后的回答文本，包含：
            - 规范化标点符号
            - 移除多余空行
            - 确保句子完整性
        """
        if not self.consistency_mode or not response:
            return response
            
        # 移除多余空行
        response = "\n".join([line for line in response.split("\n") if line.strip()])
        
        # 规范化标点符号
        if any(char in response for char in "，。？！；：""''（）【】《》"):
            response = response.replace(",", "，")
            response = response.replace(".", "。")
            response = response.replace("?", "？")
            response = response.replace("!", "！")
            response = response.replace(";", "；")
            response = response.replace(":", "：")
        
        # 确保回答以完整的句子结束
        if response and not response.endswith(("。", "！", "？", ".", "!", "?")):
            if any(char in response[-20:] for char in "，。？！；：""''（）【】《》"):
                response += "。"
            else:
                response += "."
        
        return response

    async def generate(
        self,
        prompt: str,
        stream: bool = False
    ) -> Union[str, AsyncStreamIterator]:
        """生成回答
        
        Args:
            prompt: 提示词
            stream: 是否使用流式输出
            
        Returns:
            响应文本或文本生成器
        """
        try:
            # 如果启用了一致性模式，添加一致性相关的提示
            if self.consistency_mode:
                from .prompts import PromptTemplates
                consistency_prompt = '''请确保你的回答风格保持一致，使用专业、简洁的语气，并遵循统一的结构模式。'''
                prompt = f'{prompt}\n\n{consistency_prompt}'
                
            if stream:
                stream_iterator = await self._make_request_stream(prompt)
                # 对流式响应进行包装，实现一致性处理
                return ConsistencyStreamWrapper(stream_iterator, self.consistency_mode)
            else:
                response = await self._make_request_normal(prompt)
                # 对非流式回答进行后处理
                return await self._post_process_response(response)
        except LLMServiceTimeoutError:
            # 超时错误直接向上传递，不做特殊处理
            raise
        except LLMServiceUnavailableError:
            # 服务不可用错误直接向上传递，不做特殊处理
            raise
        except LLMServiceRequestError as e:
            # 请求错误直接向上传递，不做特殊处理
            raise
        except Exception as e:
            logger.error(f"生成回答失败: {str(e)}")
            if stream:
                # 流式响应必须抛出异常，由上层处理
                raise LLMServiceError(f"生成流式回答失败: {str(e)}")
            else:
                # 非流式响应可以返回错误信息
                return "抱歉，生成回答时出现技术故障，请稍后再试。"
    
    async def generate_with_context(
        self,
        messages: List[Dict[str, str]],
        knowledge: Optional[List[Dict[str, str]]] = None,
        stream: bool = False
    ) -> Union[str, AsyncStreamIterator]:
        """带上下文的生成
        
        Args:
            messages: 历史消息列表
            knowledge: 知识片段列表
            stream: 是否使用流式输出
            
        Returns:
            响应文本或文本生成器
        """
        try:
            from .prompts import PromptTemplates
            # 使用一致性模式创建提示词
            prompt = PromptTemplates.create_chat_prompt(
                messages, 
                knowledge, 
                consistency_mode=self.consistency_mode,
                model_name=self.model  # 传入当前模型名称
            )
            
            if stream:
                stream_iterator = await self._make_request_stream(prompt)
                # 对流式响应进行包装，实现一致性处理
                return ConsistencyStreamWrapper(stream_iterator, self.consistency_mode)
            else:
                response = await self._make_request_normal(prompt)
                # 对非流式回答进行后处理
                return await self._post_process_response(response)
        except LLMServiceTimeoutError:
            # 超时错误直接向上传递，不做特殊处理
            raise
        except LLMServiceUnavailableError:
            # 服务不可用错误直接向上传递，不做特殊处理
            raise
        except LLMServiceRequestError:
            # 请求错误直接向上传递，不做特殊处理
            raise
        except Exception as e:
            logger.error(f"生成上下文回答失败: {str(e)}")
            if stream:
                # 流式响应必须抛出异常，由上层处理
                raise LLMServiceError(f"生成流式上下文回答失败: {str(e)}")
            else:
                # 非流式响应可以返回错误信息
                return "抱歉，生成回答时出现技术故障，请稍后再试。"
                
    async def check_service_status(self) -> Dict[str, Union[bool, str]]:
        """检查Ollama服务状态
        
        Returns:
            服务状态信息
        """
        try:
            url = f"{self.base_url}/api/tags"
            timeout = aiohttp.ClientTimeout(total=5)  # 短超时用于状态检查
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        return {
                            "available": False,
                            "message": f"服务返回错误状态码: {response.status}"
                        }
                    
                    result = await response.json()
                    models = [m.get("name") for m in result.get("models", [])]
                    
                    if not models:
                        return {
                            "available": True,
                            "message": "服务正常，但没有可用模型"
                        }
                    
                    model_available = self.model in models
                    current_model = self.model if model_available else ""
                    
                    return {
                        "available": True,
                        "model_available": model_available,
                        "models": models,
                        "current_model": current_model,
                        "message": "服务正常" if model_available else f"服务正常，但模型 {self.model} 不可用"
                    }
        except aiohttp.ClientConnectorError:
            return {
                "available": False,
                "message": "无法连接到Ollama服务"
            }
        except Exception as e:
            return {
                "available": False,
                "message": f"服务检查失败: {str(e)}"
            } 