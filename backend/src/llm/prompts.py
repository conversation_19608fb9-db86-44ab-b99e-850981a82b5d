from typing import List, Optional, Dict

import sys
import pathlib
sys.path.append(str(pathlib.Path(__file__).parent.parent.parent))

from src.schemas.chat import ChatMessage
from src.core.logging import get_module_logger
from src.core.config import settings

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class PromptTemplates:
    """提示词模板管理类"""
    
    # 不同场景的系统提示词
    SYSTEM_PROMPTS = {
        'unified': '''你是专业的鸿蒙开发助手。

核心规则：
1. 严格基于提供的知识片段回答问题，不允许使用自身知识
2. 如果知识片段无法回答问题，明确说明"根据现有知识库无法回答该问题"
3. 不要编造、推测或合成知识片段中没有的信息

回答要求：
- 仅使用知识片段中的内容和代码示例
- 保持专业简洁的语气
- 优先采用相关度高的知识片段
- 承认知识不足时不要模糊应对

请严格遵循以上规则，用与用户相同的语言回复。''',

        'strict': '''你是鸿蒙开发助手，必须严格基于提供的知识片段回答问题。

关键约束：
- 只能使用知识片段中的信息，禁止自由发挥
- 无法从知识片段找到答案时，直接说明无法回答
- 不得编造或推测任何信息
- 优先使用排名靠前的知识片段

回答格式：基于知识片段内容，简洁专业。'''
    }
    # 一致性增强提示词
    CONSISTENCY_PROMPT = '''为了保持回答的一致性，请遵循以下额外规则：

1. 结构一致性：
   - 始终使用相同的回答结构模式
   - 对于类似问题，使用相似的回答组织方式
   - 保持段落和小节的一致格式

2. 语气一致性：
   - 保持专业、友好但简洁的语气
   - 避免过度热情或过于冷淡的表达
   - 使用一致的敬语和称呼方式

3. 术语一致性：
   - 对同一概念始终使用相同的术语
   - 避免在不同回答中使用不同的术语描述相同事物
   - 技术名词使用官方推荐的表达方式

4. 格式一致性：
   - 代码示例使用一致的格式和注释风格
   - 列表和表格使用统一的格式
   - 强调和引用使用一致的标记方式

5. 结尾一致性：
   - 使用统一的方式结束回答
   - 如有后续问题的可能，使用一致的引导方式
   - 避免在某些回答中过于简短而在其他回答中过于冗长
'''

    @staticmethod
    def _get_system_prompt(messages: List[Dict[str, str]], knowledge: Optional[List[Dict[str, str]]] = None, consistency_mode: bool = False) -> str:
        """根据上下文动态选择系统提示词"""
        # 使用统一的提示词模板
        base_prompt = PromptTemplates.SYSTEM_PROMPTS['unified']
        
        # 如果启用一致性模式，添加一致性提示词
        if consistency_mode:
            return base_prompt + '\n\n' + PromptTemplates.CONSISTENCY_PROMPT
        
        return base_prompt

    @staticmethod
    def _format_knowledge(knowledge: List[Dict[str, str]], max_tokens: int = 2000) -> str:
        """格式化知识片段，并控制长度"""
        if not knowledge:
            return ""
        
        # 记录原始检索到的知识片段数量
        logger.info(f'检索到的原始知识片段数量: {len(knowledge)}')
        if knowledge:
            logger.info(f'原始知识片段标题: {[k.get("title", "无标题") for k in knowledge]}')
        
        # 设置相关度阈值，只保留相关度较高的知识
        relevance_threshold = 0.3
        filtered_knowledge = [k for k in knowledge if k.get('relevance', 0) >= relevance_threshold]
        
        # 如果筛选后没有结果，保留最相关的一条
        if not filtered_knowledge and knowledge:
            filtered_knowledge = [max(knowledge, key=lambda x: x.get('relevance', 0))]
        
        logger.info(f'相关度过滤后的知识片段数量: {len(filtered_knowledge)}')
        
        # 去重：根据内容的前100个字符作为指纹去重
        seen_content = set()
        unique_knowledge = []
        
        for k in filtered_knowledge:
            content_digest = k.get('content', '')[:100].lower()
            if content_digest not in seen_content:
                seen_content.add(content_digest)
                unique_knowledge.append(k)
        
        logger.info(f'去重后的知识片段数量: {len(unique_knowledge)}')
        
        # 按相关度排序
        sorted_knowledge = sorted(unique_knowledge, key=lambda x: x.get('relevance', 0), reverse=True)
        
        knowledge_prompt = '以下是检索到的相关知识：\n<knowledge>\n'
        current_length = len(knowledge_prompt)
        
        # 记录最终添加到prompt的知识片段
        applied_knowledge_count = 0
        applied_knowledge_titles = []
        total_knowledge_count = len(sorted_knowledge)
        
        for idx, k in enumerate(sorted_knowledge):
            # 跳过相关度过低的知识
            relevance = k.get('relevance', 0.0)
            if relevance < relevance_threshold:
                continue
                
            # 格式化单个知识片段
            piece = ''
            # 获取标题，如果没有则使用默认值
            title = k.get('title', '无标题')
            if k.get('type') == 'document':
                piece += f'《{title}》- [知识{idx+1}]\n{k["content"]}\n'
                if k.get('url'):
                    piece += f'来源：{k["url"]}\n'
            else:
                piece += f'《{title}》- [知识{idx+1}]\n{k["content"]}\n'
            
            # 添加相关度评分
            piece += f'相关度：{relevance:.2f}\n\n'
            
            # 检查是否超出长度限制
            if current_length + len(piece) > max_tokens:
                # 如果是第一条知识，确保至少包含部分内容
                if idx == 0:
                    # 截取一部分内容
                    max_first_piece = max_tokens - current_length - 50  # 预留50个字符给结尾
                    # 获取标题并使用统一格式
                    title = k.get('title', '无标题')
                    # 确保标题行是完整的
                    title_line = f'《{title}》- [知识{idx+1}]\n'
                    # 计算内容可用的空间
                    content_space = max_first_piece - len(title_line)
                    # 构建截断的片段
                    truncated_piece = title_line + k.get('content', '')[:content_space] + '... (内容已截断)\n\n'
                    knowledge_prompt += truncated_piece
                    applied_knowledge_count += 1
                    applied_knowledge_titles.append(title + ' (截断)')
                break
                
            knowledge_prompt += piece
            current_length += len(piece)
            applied_knowledge_count += 1
            applied_knowledge_titles.append(k.get('title', '无标题'))
        
        # 添加知识片段截断提示
        if applied_knowledge_count < total_knowledge_count:
            remaining_space = max_tokens - current_length - 50  # 预留一些空间
            if remaining_space > 0:
                truncation_notice = f'\n注意：由于长度限制，仅显示了{applied_knowledge_count}个知识片段，还有{total_knowledge_count - applied_knowledge_count}个相关知识片段未显示。\n'
                if len(truncation_notice) <= remaining_space:
                    knowledge_prompt += truncation_notice
            
        knowledge_prompt += '</knowledge>\n'
        
        logger.info(f'最终应用到prompt的知识片段数量: {applied_knowledge_count}')
        logger.info(f'应用到prompt的知识片段标题: {applied_knowledge_titles}')
        
        return knowledge_prompt.strip()

    @staticmethod
    def _format_chat_history(messages: List[Dict[str, str]], max_messages: int = 5, max_tokens: int = 1000) -> str:
        """格式化对话历史，并控制长度和数量"""
        if not messages:
            return ""
            
        chat_history = "以下是对话历史:\n <history>\n"
        current_length = len(chat_history)
        
        # 只使用最近的N条消息，删除最后一次信息(当前用户提问)
        recent_messages = messages[-max_messages:-1]
        
        for msg in recent_messages:
            # 格式化单条消息
            if isinstance(msg, dict):
                role = "用户" if msg["role"] == "user" else "助手"
                piece = f"{role}：{msg['content'].strip()}\n"
            elif isinstance(msg, ChatMessage):
                piece = f"{msg.role}：{msg.content.strip()}\n"
            else:
                piece = f"消息：{msg}\n"
                logger.debug(f"消息类型错误，消息类型为{type(msg)}")

            # 检查是否超出长度限制
            if current_length + len(piece) > max_tokens:
                break
                
            chat_history += piece
            current_length += len(piece)
        
        chat_history += "\n</history>\n"

        return chat_history.strip()
    
    @staticmethod
    def create_chat_prompt(
        messages: List[Dict[str, str]], 
        knowledge: Optional[List[Dict[str, str]]] = None,
        max_tokens: int = None,
        consistency_mode: bool = False,
        model_name: Optional[str] = None
    ) -> str:
        """创建聊天提示词
        
        Args:
            messages: 历史消息列表，每条消息包含role和content
            knowledge: 相关的知识片段列表
            max_tokens: 最大token数量，默认使用配置中的LLM_MAX_TOKENS
            consistency_mode: 是否启用一致性模式
            model_name: 模型名称，用于获取该模型的最大token数
            
        Returns:
            组装后的提示词
        """
        # 如果未提供max_tokens，使用配置中的值
        if max_tokens is None:
            if model_name:
                # 根据模型名称获取最大token数
                max_tokens = settings.LLM_MODEL_MAX_TOKENS.get(model_name, settings.LLM_MAX_TOKENS)
            else:
                # 使用默认值
                max_tokens = settings.LLM_MAX_TOKENS
                
        # 确保max_tokens有合理的最小值
        max_tokens = max(4000, max_tokens)
        
        # 1. 选择合适的系统提示词
        system_prompt = PromptTemplates._get_system_prompt(messages, knowledge, consistency_mode)
        current_length = len(system_prompt)

        # 2. 添加用户问题
        user_question = f"注意，你需要回答的用户问题是：\n{messages[-1]['content']}\n"
        current_length += len(user_question)
        
        # 3. 添加知识上下文（提高到80%的token）
        knowledge_prompt = PromptTemplates._format_knowledge(
            knowledge, 
            max_tokens=int(max_tokens * 0.8)
        )
        current_length += len(knowledge_prompt)
        
        # 4. 组装最终提示词
        final_prompt = f"{system_prompt}\n\n{knowledge_prompt}\n\n{user_question}\n\n"
        
        # 添加调试日志，打印生成的prompt
        logger.debug(f"===== FINAL PROMPT START =====")
        logger.debug(final_prompt)
        logger.debug(f"===== FINAL PROMPT END =====")
        return final_prompt.strip()
    
    @staticmethod
    def create_stream_prompt(chunk: str) -> str:
        """创建流式输出的提示词
        
        Args:
            chunk: 文本块
            
        Returns:
            处理后的文本块
        """
        # 直接返回原始文本块，保留所有换行符
        return chunk
