#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证 macOS 修复
"""
import asyncio
import platform

print(f"操作系统: {platform.system()}")
print(f"Python 版本: {platform.python_version()}")

async def test_basic_async():
    """测试基本异步功能"""
    print("测试基本异步功能...")
    await asyncio.sleep(0.1)
    print("基本异步功能正常")

async def test_gather():
    """测试 asyncio.gather"""
    print("测试 asyncio.gather...")
    
    async def simple_task(n):
        await asyncio.sleep(0.1)
        return f"任务 {n} 完成"
    
    # 在 macOS 上测试是否还会有资源泄漏
    tasks = [simple_task(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    print(f"gather 测试完成: {results}")

async def test_semaphore():
    """测试信号量"""
    print("测试信号量...")
    
    semaphore = asyncio.Semaphore(2)
    
    async def limited_task(n):
        async with semaphore:
            await asyncio.sleep(0.1)
            return f"限制任务 {n} 完成"
    
    tasks = [limited_task(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    print(f"信号量测试完成: {results}")

async def main():
    """主测试函数"""
    print("开始简单测试...")
    
    await test_basic_async()
    await test_gather()
    await test_semaphore()
    
    print("所有测试完成")
    
    # 等待确保所有资源清理
    await asyncio.sleep(0.2)

if __name__ == "__main__":
    try:
        asyncio.run(main())
        print("程序正常退出")
    except KeyboardInterrupt:
        print("程序被中断")
    except Exception as e:
        print(f"程序出错: {e}")
