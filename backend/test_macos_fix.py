#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
macOS 修复测试脚本 - 验证资源泄漏修复是否有效
"""
import asyncio
import sys
import os
import time
import platform
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['NKRA_ENV'] = 'dev'

from src.core.config_loader import load_configurations
from src.core.config import settings
from loguru import logger


async def test_basic_import():
    """测试基本导入是否正常"""
    try:
        logger.info("=== 测试基本导入 ===")
        
        # 加载配置
        load_configurations()
        logger.info(f"配置加载成功，macOS 序列化模式: {settings.MACOS_FORCE_SEQUENTIAL}")
        
        # 测试数据库导入
        from src.core.database import async_session_factory
        logger.info("数据库模块导入成功")
        
        # 测试检索器导入
        from src.services.rag.retrievers import MultiQueryRetriever, HybridRetriever
        logger.info("检索器模块导入成功")
        
        # 测试 LLM 服务导入
        from src.services.llm_service_factory import create_service
        logger.info("LLM 服务模块导入成功")
        
        logger.info("✅ 所有基本导入测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本导入测试失败: {str(e)}")
        return False


async def test_simple_retrieval():
    """测试简单检索功能"""
    try:
        logger.info("\n=== 测试简单检索功能 ===")
        
        from src.core.database import init_db, async_session_factory
        from src.repositories.knowledge_repository import KnowledgeRepository
        from src.services.rag.retrievers import VectorRetriever
        
        # 初始化数据库
        await init_db()
        logger.info("数据库初始化成功")
        
        # 创建简单的向量检索测试
        async with async_session_factory() as session:
            knowledge_repo = KnowledgeRepository(session)
            retriever = VectorRetriever(knowledge_repo)
            
            # 执行一个简单的检索
            results = await retriever.retrieve(
                query="测试查询",
                limit=3,
                min_score=0.1
            )
            
            logger.info(f"简单检索完成，返回 {len(results)} 条结果")
        
        logger.info("✅ 简单检索测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 简单检索测试失败: {str(e)}")
        return False


async def test_llm_service():
    """测试 LLM 服务"""
    try:
        logger.info("\n=== 测试 LLM 服务 ===")
        
        from src.services.llm_service_factory import create_service
        
        # 创建 LLM 服务
        llm_service = create_service()
        logger.info(f"LLM 服务创建成功: {type(llm_service).__name__}")
        
        # 测试简单的生成
        try:
            response = await llm_service.generate("你好，这是一个测试。请简短回复。")
            logger.info(f"LLM 生成测试成功，响应长度: {len(response)} 字符")
        except Exception as llm_error:
            logger.warning(f"LLM 生成测试失败（可能是网络问题）: {str(llm_error)}")
            # LLM 失败不影响整体测试
        
        logger.info("✅ LLM 服务测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM 服务测试失败: {str(e)}")
        return False


async def test_resource_cleanup():
    """测试资源清理"""
    try:
        logger.info("\n=== 测试资源清理 ===")
        
        # 创建一些对象并确保它们能正确清理
        from src.services.llm_service_factory import create_service
        
        services = []
        for i in range(3):
            service = create_service()
            services.append(service)
            logger.debug(f"创建服务 {i+1}")
        
        # 清理服务
        for i, service in enumerate(services):
            if hasattr(service, 'close'):
                try:
                    await service.close()
                    logger.debug(f"关闭服务 {i+1}")
                except:
                    pass
        
        # 清空引用
        services.clear()
        
        logger.info("✅ 资源清理测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 资源清理测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    logger.info(f"开始 macOS 修复验证测试")
    logger.info(f"操作系统: {platform.system()} {platform.release()}")
    logger.info(f"Python 版本: {platform.python_version()}")
    
    tests = [
        ("基本导入", test_basic_import),
        ("简单检索", test_simple_retrieval),
        ("LLM 服务", test_llm_service),
        ("资源清理", test_resource_cleanup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            logger.error(f"测试 '{test_name}' 出现异常: {str(e)}")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！macOS 修复验证成功。")
        logger.info("现在可以尝试正常使用应用，应该不会再出现资源泄漏警告。")
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败，可能还存在问题。")
    
    # 等待一小段时间确保所有异步操作完成
    await asyncio.sleep(0.5)
    
    logger.info("测试完成，程序即将退出...")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        sys.exit(1)
