#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行检索测试脚本 - 用于测试 macOS 上的并行检索稳定性
"""
import asyncio
import sys
import os
import time
import platform
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['NKRA_ENV'] = 'dev'

from src.core.config_loader import load_configurations
from src.core.database import async_session_factory, init_db
from src.repositories.knowledge_repository import KnowledgeRepository
from src.services.rag.retrievers import MultiQueryRetriever, VectorRetriever, HybridRetriever
from src.services.llm_service_factory import create_service
from src.core.config import settings
from loguru import logger


async def test_parallel_retrieval():
    """测试并行检索功能"""
    logger.info("=== 开始并行检索测试 ===")
    logger.info(f"操作系统: {platform.system()} {platform.release()}")
    logger.info(f"数据库类型: {settings.DB_TYPE}")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("数据库初始化成功")
        
        # 创建数据库会话
        async with async_session_factory() as session:
            knowledge_repo = KnowledgeRepository(session)
            
            # 创建 LLM 服务
            llm_service = create_service()
            
            # 创建基础检索器
            if settings.USE_HYBRID_RETRIEVER:
                base_retriever = HybridRetriever(knowledge_repo)
                logger.info("使用混合检索器")
            else:
                base_retriever = VectorRetriever(knowledge_repo)
                logger.info("使用向量检索器")
            
            # 创建多查询检索器
            multi_retriever = MultiQueryRetriever(
                base_retriever=base_retriever,
                llm_service=llm_service,
                num_queries=3,
                use_cache=True
            )
            
            # 测试查询列表
            test_queries = [
                "Grid网格元素拖拽交换如何实现",
                "HarmonyOS应用开发最佳实践",
                "ArkTS语言特性和用法",
                "组件生命周期管理",
                "状态管理和数据绑定"
            ]
            
            logger.info(f"准备测试 {len(test_queries)} 个查询")
            
            # 逐个测试查询
            for i, query in enumerate(test_queries, 1):
                logger.info(f"\n--- 测试查询 {i}/{len(test_queries)}: {query} ---")
                start_time = time.time()
                
                try:
                    results = await multi_retriever.retrieve(
                        query=query,
                        limit=5,
                        min_score=0.3
                    )
                    
                    end_time = time.time()
                    elapsed = (end_time - start_time) * 1000
                    
                    logger.info(f"查询完成: 找到 {len(results)} 条结果, 耗时: {elapsed:.2f}ms")
                    
                    # 显示前3个结果的标题
                    for j, result in enumerate(results[:3], 1):
                        title = result.get('title', '无标题')[:50]
                        score = result.get('relevance', 0)
                        logger.info(f"  {j}. {title}... (相关度: {score:.3f})")
                    
                except Exception as e:
                    logger.error(f"查询失败: {str(e)}")
                    raise
                
                # 在查询之间稍作停顿
                await asyncio.sleep(0.5)
            
            logger.info("\n=== 并行检索测试完成 ===")
            logger.info("所有查询都成功完成，没有发生崩溃")
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise


async def test_concurrent_queries():
    """测试并发查询"""
    logger.info("\n=== 开始并发查询测试 ===")
    
    try:
        # 创建多个并发查询任务
        async def single_query(query_id: int, query: str):
            async with async_session_factory() as session:
                knowledge_repo = KnowledgeRepository(session)
                llm_service = create_service()
                
                if settings.USE_HYBRID_RETRIEVER:
                    base_retriever = HybridRetriever(knowledge_repo)
                else:
                    base_retriever = VectorRetriever(knowledge_repo)
                
                multi_retriever = MultiQueryRetriever(
                    base_retriever=base_retriever,
                    llm_service=llm_service,
                    num_queries=2,  # 减少变体数量
                    use_cache=True
                )
                
                start_time = time.time()
                results = await multi_retriever.retrieve(
                    query=query,
                    limit=3,
                    min_score=0.3
                )
                end_time = time.time()
                
                elapsed = (end_time - start_time) * 1000
                logger.info(f"并发查询 {query_id} 完成: {len(results)} 条结果, 耗时: {elapsed:.2f}ms")
                return query_id, len(results), elapsed
        
        # 创建并发任务
        concurrent_queries = [
            "Grid组件使用方法",
            "ArkTS开发指南", 
            "HarmonyOS最佳实践"
        ]
        
        tasks = [
            single_query(i, query) 
            for i, query in enumerate(concurrent_queries, 1)
        ]
        
        # 并发执行
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_elapsed = (end_time - start_time) * 1000
        logger.info(f"\n并发查询总耗时: {total_elapsed:.2f}ms")
        
        # 检查结果
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"并发查询 {i+1} 失败: {str(result)}")
            else:
                query_id, result_count, elapsed = result
                logger.info(f"并发查询 {query_id} 成功: {result_count} 条结果")
                success_count += 1
        
        logger.info(f"并发测试完成: {success_count}/{len(tasks)} 个查询成功")
        
    except Exception as e:
        logger.error(f"并发测试失败: {str(e)}")
        raise


async def main():
    """主函数"""
    try:
        # 加载配置
        load_configurations()
        
        # 运行测试
        await test_parallel_retrieval()
        await test_concurrent_queries()
        
        logger.info("\n🎉 所有测试通过！并行检索功能正常工作。")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
