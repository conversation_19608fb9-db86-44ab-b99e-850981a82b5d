# 开发环境配置文件
# 使用环境变量 HMQA_ENV=dev 时自动加载此文件
# ==================== LLM服务配置 ====================
# LLM服务类型: "siliconflow" 或 "ollama"
LLM_SERVICE: "siliconflow"

# SiliconFlow配置
SILICONFLOW_MODEL: "Qwen/Qwen3-30B-A3B"
SILICONFLOW_BASE_URL: "https://api.siliconflow.cn"

# Ollama配置
OLLAMA_MODEL: "deepseek-r1:1.5b"
OLLAMA_BASE_URL: "http://localhost:11434"


LLM_REQUEST_TIMEOUT: 60.0  # LLM请求超时时间（秒）
LLM_STREAM_TIMEOUT: 120.0  # 流式请求超时时间（秒）
LLM_MAX_TOKENS: 4096  # 默认LLM最大上下文窗口大小

# 各模型的最大上下文窗口大小配置
LLM_MODEL_MAX_TOKENS:
  # SiliconFlow 模型配置
  "Qwen/QwQ-32B": 16000
  "Qwen/Qwen2-7B-Instruct": 4000
  # Ollama 模型配置
  "deepseek-r1:1.5b": 4000
  "qwen:7b": 8000
  "qwen2:7b": 32000

# ==================== 向量模型配置 ====================
EMBEDDING_MODEL: "/Users/<USER>/code/EMBEDDING_MODEL/bge-m3" # 使用正斜杠路径
EMBEDDING_DEVICE: "cpu"  # "cpu" 或 "cuda"


# ==================== 文档处理配置 ====================
CHUNK_SIZE: 1000  # 文档分块大小
CHUNK_OVERLAP: 200  # 分块重叠大小

CONTENT_ENHANCEMENT_CONFIGS:
  "generate_document_summary": true  # 是否为文档生成摘要
  "generate_chunk_queries": true  # 是否为每个分片生成查询问题
  "generate_chunk_summary": true  # 是否为每个分片生成摘要
  "generate_chunk_table_description": true  # 是否为表格分片生成描述
  "generate_chunk_code_description": true  # 是否为代码分片生成描述
  "max_queries_per_chunk": 2  # 每个分片生成的查询问题数量
  "replace_table_and_code_with_description": false  # 是否用描述替换表格和代码分片
  "chunk_parallal_per_task": 3

# ==================== 查询变体生成配置 ====================
# 变体生成专用LLM服务，可以使用更小的模型提高速度
# 留空则使用主LLM服务
VARIANT_LLM_SERVICE: "siliconflow"
VARIANT_LLM_MODEL: "Qwen/Qwen2-7B-Instruct"

# ==================== 检索配置 ====================
# 是否启用多查询扩展
USE_MULTI_QUERY: true 
# 多查询变体数量
MULTI_QUERY_COUNT: 3
# 多查询缓存大小
MULTI_QUERY_CACHE_SIZE: 100
# 多查询缓存过期时间(秒)
MULTI_QUERY_CACHE_TTL: 86400
# 最小相关度分数
MIN_RELEVANCE_SCORE: 0.3
# 默认检索数量
DEFAULT_SEARCH_LIMIT: 5

# ==================== 混合检索配置 ====================
USE_HYBRID_RETRIEVER: true
HYBRID_VECTOR_WEIGHT: 0.5
HYBRID_BM25_WEIGHT: 0.5
HYBRID_FUSION_METHOD: "weighted_sum"

# ==================== 数据库配置 ====================
DB_TYPE: "sqlite"

# ==================== Elasticsearch配置 ====================
ELASTICSEARCH:
  HOSTS: ["http://localhost:9200"]
  INDEX_NAME: "nkra_knowledge"
  TIMEOUT: 60
  MAX_RETRIES: 3
  USE_SSL: false
  VERIFY_CERTS: false

# ==================== 其他配置 ====================
# CORS设置
BACKEND_CORS_ORIGINS: ["*"]

# 日志等级: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
LOG_LEVEL: "DEBUG"

# SQL查询回显(调试用)
SQL_ECHO: false 