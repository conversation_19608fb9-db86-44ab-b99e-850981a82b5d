#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试 - 专门测试资源泄漏问题
"""
import asyncio
import platform
import signal
import sys

print(f"操作系统: {platform.system()}")
print(f"Python 版本: {platform.python_version()}")

# 全局标志用于优雅退出
should_exit = False

def signal_handler(signum, frame):
    """信号处理器"""
    global should_exit
    print(f"\n收到信号 {signum}，准备退出...")
    should_exit = True

async def test_basic_async():
    """测试基本异步操作"""
    print("测试基本异步操作...")
    
    async def simple_task(n):
        await asyncio.sleep(0.01)
        return f"任务 {n}"
    
    # 测试 gather
    tasks = [simple_task(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    print(f"gather 测试: {len(results)} 个任务完成")

async def test_semaphore_if_needed():
    """仅在非 macOS 系统上测试信号量"""
    if platform.system() == 'Darwin':
        print("macOS 上跳过信号量测试（避免资源泄漏）")
        return
    
    print("测试信号量...")
    semaphore = asyncio.Semaphore(2)
    
    async def limited_task(n):
        async with semaphore:
            await asyncio.sleep(0.01)
            return f"限制任务 {n}"
    
    tasks = [limited_task(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    print(f"信号量测试: {len(results)} 个任务完成")

async def test_http_session():
    """测试 HTTP 会话（如果可用）"""
    try:
        import aiohttp
        print("测试 HTTP 会话...")
        
        # 使用正确的会话管理
        async with aiohttp.ClientSession() as session:
            try:
                # 测试一个简单的请求
                async with session.get('https://httpbin.org/get', timeout=aiohttp.ClientTimeout(total=2)) as response:
                    if response.status == 200:
                        print("HTTP 会话测试成功")
                    else:
                        print(f"HTTP 响应状态: {response.status}")
            except asyncio.TimeoutError:
                print("HTTP 请求超时（正常）")
            except Exception as e:
                print(f"HTTP 请求失败: {str(e)}")
                
    except ImportError:
        print("aiohttp 未安装，跳过 HTTP 测试")

async def main():
    """主函数"""
    global should_exit
    
    print("开始最小化资源泄漏测试...")
    
    # 设置信号处理器
    if platform.system() != 'Windows':
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 运行测试
        await test_basic_async()
        
        if not should_exit:
            await test_semaphore_if_needed()
        
        if not should_exit:
            await test_http_session()
        
        print("所有测试完成")
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
    
    # 等待一小段时间确保资源清理
    await asyncio.sleep(0.1)
    print("等待资源清理...")

if __name__ == "__main__":
    try:
        # 在 macOS 上使用更保守的事件循环策略
        if platform.system() == 'Darwin':
            print("macOS 上使用默认事件循环策略")
            asyncio.set_event_loop_policy(asyncio.DefaultEventLoopPolicy())
        
        # 运行主函数
        asyncio.run(main())
        print("✅ 程序正常退出")
        
    except KeyboardInterrupt:
        print("\n⚠️  程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        sys.exit(1)
