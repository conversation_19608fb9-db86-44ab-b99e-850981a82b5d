"""add domain to knowledge table

Revision ID: 003
Revises: 002
Create Date: 2024-07-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # 从constants导入KnowledgeDomain值
    default_domain = "best_practices"
    
    # 添加domain字段
    op.add_column('knowledge', sa.Column('domain', sa.String(50), nullable=False, server_default=default_domain))
    
    # 创建索引
    op.create_index('ix_knowledge_domain', 'knowledge', ['domain'])
    
    # 数据迁移逻辑，你可以根据需要在此处执行一些基本的数据迁移
    # 例如：将指定时间段的记录设置为特定领域
    
    conn = op.get_bind()
    
    # 示例：将2024年第一季度的知识记录设置为API文档
    conn.execute(
        text("UPDATE knowledge SET domain = 'api_documentation' "
             "WHERE created_at BETWEEN '2024-01-01 00:00:00' AND '2024-03-31 23:59:59'")
    )
    
    # 示例：将2024年第二季度的知识记录设置为鸿蒙突击队经验-黄大年
    conn.execute(
        text("UPDATE knowledge SET domain = 'harmony_exp_HDN' "
             "WHERE created_at BETWEEN '2024-04-01 00:00:00' AND '2024-06-30 23:59:59'")
    )
    
    # 注意：对于更复杂的数据迁移逻辑，建议使用单独的脚本（apply_domain_data_migration.py）在迁移后运行

def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_knowledge_domain')
    
    # 删除字段
    op.drop_column('knowledge', 'domain') 