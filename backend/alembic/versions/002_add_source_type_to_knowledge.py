"""add source_type to knowledge table

Revision ID: 002
Revises: 001
Create Date: 2024-02-26 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # 添加source_type字段
    op.add_column('knowledge', sa.Column('source_type', sa.String(50), nullable=False, server_default='manual'))
    
    # 创建索引
    op.create_index('ix_knowledge_source_type', 'knowledge', ['source_type'])

def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_knowledge_source_type')
    
    # 删除字段
    op.drop_column('knowledge', 'source_type') 