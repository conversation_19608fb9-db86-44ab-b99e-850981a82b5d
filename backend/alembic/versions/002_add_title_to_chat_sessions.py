"""add title to chat sessions

Revision ID: 001
Revises: None
Create Date: 2024-03-24 15:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # 添加title字段到chat_sessions表
    op.add_column('chat_sessions', sa.Column('title', sa.String(255), nullable=True))

def downgrade() -> None:
    # 删除title字段
    op.drop_column('chat_sessions', 'title') 