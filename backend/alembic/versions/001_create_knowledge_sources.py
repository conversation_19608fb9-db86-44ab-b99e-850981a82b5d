"""create knowledge sources table

Revision ID: 001
Revises: 
Create Date: 2024-02-25 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.create_table(
        'knowledge_sources',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('name', sa.String(length=255), nullable=False),
        sa.Column('url', sa.String(length=1024), nullable=False),
        sa.Column('source_type', sa.String(length=50), nullable=False),
        sa.Column('content_hash', sa.String(length=32)),
        sa.Column('last_check_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('last_update_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('update_frequency', sa.Integer(), server_default='3600'),
        sa.Column('status', sa.String(length=20), server_default='active'),
        sa.Column('error_message', sa.Text()),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('ix_knowledge_sources_name', 'knowledge_sources', ['name'])
    op.create_index('ix_knowledge_sources_source_type', 'knowledge_sources', ['source_type'])
    op.create_index('ix_knowledge_sources_status', 'knowledge_sources', ['status'])
    op.create_index('ix_knowledge_sources_last_check_time', 'knowledge_sources', ['last_check_time'])

def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_knowledge_sources_name')
    op.drop_index('ix_knowledge_sources_source_type')
    op.drop_index('ix_knowledge_sources_status')
    op.drop_index('ix_knowledge_sources_last_check_time')
    
    # 删除表
    op.drop_table('knowledge_sources') 