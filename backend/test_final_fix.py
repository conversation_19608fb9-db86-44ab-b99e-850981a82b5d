#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证脚本
"""
import asyncio
import platform
import time
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['NKRA_ENV'] = 'dev'

print(f"操作系统: {platform.system()}")
print(f"Python 版本: {platform.python_version()}")

async def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    try:
        from src.core.config_loader import load_configurations
        from src.core.config import settings
        
        load_configurations()
        
        print(f"macOS 序列化执行: {settings.MACOS_FORCE_SEQUENTIAL}")
        print(f"macOS 禁用流式: {settings.MACOS_DISABLE_STREAMING}")
        print(f"macOS 禁用重载: {settings.MACOS_DISABLE_RELOAD}")
        print("配置加载成功")
        return True
    except Exception as e:
        print(f"配置加载失败: {str(e)}")
        return False

async def test_database_connection():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    try:
        from src.core.database import async_session_factory, init_db, close_db
        
        # 初始化数据库
        await init_db()
        print("数据库初始化成功")
        
        # 测试连接
        async with async_session_factory() as session:
            result = await session.execute("SELECT 1")
            await result.fetchone()
            print("数据库连接测试成功")
        
        # 关闭数据库
        await close_db()
        print("数据库关闭成功")
        
        return True
    except Exception as e:
        print(f"数据库测试失败: {str(e)}")
        return False

async def test_retriever_creation():
    """测试检索器创建"""
    print("\n=== 测试检索器创建 ===")
    try:
        from src.core.database import async_session_factory, init_db
        from src.repositories.knowledge_repository import KnowledgeRepository
        from src.services.rag.retrievers import VectorRetriever, HybridRetriever, MultiQueryRetriever
        from src.services.llm_service_factory import create_service
        
        # 初始化数据库
        await init_db()
        
        async with async_session_factory() as session:
            knowledge_repo = KnowledgeRepository(session)
            
            # 创建基础检索器
            vector_retriever = VectorRetriever(knowledge_repo)
            print("向量检索器创建成功")
            
            hybrid_retriever = HybridRetriever(knowledge_repo)
            print("混合检索器创建成功")
            
            # 创建 LLM 服务
            llm_service = create_service()
            print("LLM 服务创建成功")
            
            # 创建多查询检索器
            multi_retriever = MultiQueryRetriever(
                base_retriever=vector_retriever,
                llm_service=llm_service,
                num_queries=2,
                use_cache=True
            )
            print("多查询检索器创建成功")
        
        return True
    except Exception as e:
        print(f"检索器创建失败: {str(e)}")
        return False

async def test_simple_retrieval():
    """测试简单检索"""
    print("\n=== 测试简单检索 ===")
    try:
        from src.core.database import async_session_factory, init_db
        from src.repositories.knowledge_repository import KnowledgeRepository
        from src.services.rag.retrievers import VectorRetriever
        
        # 初始化数据库
        await init_db()
        
        async with async_session_factory() as session:
            knowledge_repo = KnowledgeRepository(session)
            retriever = VectorRetriever(knowledge_repo)
            
            # 执行简单检索
            results = await retriever.retrieve(
                query="测试查询",
                limit=3,
                min_score=0.1
            )
            
            print(f"简单检索完成，返回 {len(results)} 条结果")
        
        return True
    except Exception as e:
        print(f"简单检索失败: {str(e)}")
        return False

async def test_resource_cleanup():
    """测试资源清理"""
    print("\n=== 测试资源清理 ===")
    try:
        # 创建一些异步任务
        async def dummy_task(n):
            await asyncio.sleep(0.01)
            return f"任务 {n} 完成"
        
        # 测试 gather
        tasks = [dummy_task(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        print(f"异步任务测试: {len(results)} 个任务完成")
        
        # 测试信号量（如果在非 macOS 系统上）
        if platform.system() != 'Darwin':
            semaphore = asyncio.Semaphore(2)
            
            async def limited_task(n):
                async with semaphore:
                    await asyncio.sleep(0.01)
                    return f"限制任务 {n} 完成"
            
            tasks = [limited_task(i) for i in range(3)]
            results = await asyncio.gather(*tasks)
            print(f"信号量测试: {len(results)} 个任务完成")
        else:
            print("macOS 上跳过信号量测试")
        
        return True
    except Exception as e:
        print(f"资源清理测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始最终修复验证测试...")
    
    if platform.system() == 'Darwin':
        print("检测到 macOS 系统，将使用所有优化策略")
    
    tests = [
        ("配置加载", test_config_loading),
        ("数据库连接", test_database_connection),
        ("检索器创建", test_retriever_creation),
        ("简单检索", test_simple_retrieval),
        ("资源清理", test_resource_cleanup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n--- 执行测试: {test_name} ---")
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！修复验证成功。")
        if platform.system() == 'Darwin':
            print("macOS 优化策略已生效，现在可以启动后端服务：")
            print("  python main.py")
            print("或使用专用启动脚本：")
            print("  python start_macos.py")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，可能还存在问题。")
    
    # 等待确保所有异步操作完成
    await asyncio.sleep(0.5)
    print("测试完成，程序即将退出...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
        print("\n✅ 测试程序正常退出，没有检测到资源泄漏")
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试程序异常退出: {str(e)}")
        sys.exit(1)
