#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
macOS 专用启动脚本 - 避免资源泄漏
"""
import asyncio
import signal
import sys
import os
import platform
from pathlib import Path

# 添加项目根目录到 Python 路径
root_dir = str(Path(__file__).parent.parent)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 先加载配置
from src.core.config_loader import load_configurations
load_configurations()

from src.core.config import settings
from src.core.logging import setup_logging, app_logger as logger

# 初始化日志配置
setup_logging(log_level=settings.LOG_LEVEL, log_to_file=True)

# 全局变量用于优雅关闭
shutdown_event = asyncio.Event()
server = None

async def shutdown_handler():
    """优雅关闭处理器"""
    logger.info("收到关闭信号，开始优雅关闭...")
    
    # 设置关闭事件
    shutdown_event.set()
    
    # 关闭服务器
    if server:
        logger.info("关闭 HTTP 服务器...")
        server.should_exit = True
        
    # 等待一小段时间让请求完成
    await asyncio.sleep(0.5)
    
    # 关闭数据库连接
    try:
        from src.core.database import close_db
        await close_db()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.warning(f"关闭数据库连接失败: {str(e)}")
    
    # 关闭 ElasticSearch
    try:
        from src.core.elasticsearch import ElasticSearch
        es_global = ElasticSearch()
        await es_global.close()
        logger.info("ElasticSearch 连接已关闭")
    except Exception as e:
        logger.warning(f"关闭 ElasticSearch 失败: {str(e)}")
    
    logger.info("资源清理完成")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}")
    # 创建任务来处理关闭
    asyncio.create_task(shutdown_handler())

async def run_server():
    """运行服务器"""
    global server
    
    try:
        import uvicorn
        from main import app
        
        # 创建服务器配置
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            reload=False,  # 在 macOS 上禁用重载
            log_level="info"
        )
        
        # 创建服务器实例
        server = uvicorn.Server(config)
        
        logger.info(f"启动 {settings.PROJECT_NAME} Backend (macOS 优化版)...")
        logger.info(f"环境: {os.environ.get('NKRA_ENV', '默认')}")
        logger.info(f"日志级别: {settings.LOG_LEVEL}")
        logger.info(f"数据库类型: {settings.DB_TYPE}")
        logger.info("macOS 优化: 禁用重载，使用序列化执行")
        
        # 运行服务器
        await server.serve()
        
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        raise

async def main():
    """主函数"""
    try:
        # 设置信号处理器
        if platform.system() != 'Windows':
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        
        # 运行服务器
        await run_server()
        
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
        await shutdown_handler()
    except Exception as e:
        logger.error(f"应用运行出错: {str(e)}")
        await shutdown_handler()
        raise
    finally:
        logger.info("应用已退出")

if __name__ == "__main__":
    if platform.system() != 'Darwin':
        print("此脚本专为 macOS 设计，其他系统请使用 python main.py")
        sys.exit(1)
    
    try:
        # 在 macOS 上使用更严格的事件循环策略
        if hasattr(asyncio, 'set_event_loop_policy'):
            # 使用默认策略，避免某些 macOS 特定问题
            asyncio.set_event_loop_policy(asyncio.DefaultEventLoopPolicy())
        
        # 运行主函数
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {str(e)}")
        sys.exit(1)
    finally:
        print("程序已完全退出")
