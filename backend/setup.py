from setuptools import setup, find_packages
import os
import re

# 读取requirements.txt文件
def get_requirements():
    with open(os.path.join(os.path.dirname(__file__), 'requirements.txt'), 'r') as f:
        requirements = []
        for line in f:
            # 忽略注释行和空行
            line = line.strip()
            if line and not line.startswith('#'):
                # 处理可能的行尾注释
                line = re.sub(r'#.*$', '', line).strip()
                requirements.append(line)
        return requirements

setup(
    name="harmonyqa",
    version="0.1.0",
    description="HarmonyQA - 智能问答系统",
    author="HarmonyQA Team",
    author_email="<EMAIL>",
    url="https://github.com/example/harmonyqa",
    packages=find_packages(),
    python_requires=">=3.8",
    install_requires=get_requirements(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
)