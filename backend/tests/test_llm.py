import pytest
import asyncio
from src.llm import OllamaService, PromptTemplates
from src.llm.siliconflow import LLMServiceError

@pytest.mark.asyncio
async def test_simple_generate():
    """测试简单生成"""
    try:
        service = OllamaService(model="deepseek-r1:1.5b")  # 使用本地Ollama服务
        response = await service.generate("你好，请介绍一下自己。")
        assert response and isinstance(response, str)
        print(f"\n回答：{response}")
    except Exception as e:
        print(f"\n请求失败：{str(e)}")
        raise

@pytest.mark.asyncio
async def test_stream_generate():
    """测试流式生成"""
    try:
        service = OllamaService(model="deepseek-r1:1.5b")
        stream = await service.generate("你好，请介绍一下自己。", stream=True)
        try:
            async for chunk in stream:
                assert chunk and isinstance(chunk, str)
                print(chunk, end="", flush=True)
            print()
        finally:
            if hasattr(stream, 'close'):
                await stream.close()
    except Exception as e:
        print(f"\n请求失败：{str(e)}")
        raise

@pytest.mark.asyncio
async def test_generate_with_context():
    """测试带上下文生成"""
    try:
        service = OllamaService(model="qwen:7b")
        messages = [
            {"role": "user", "content": "鸿蒙系统是什么？"},
            {"role": "assistant", "content": "鸿蒙系统是华为自主开发的分布式操作系统..."},
            {"role": "user", "content": "它的主要特点是什么？"}
        ]
        knowledge = [
            {
                "content": "HarmonyOS（鸿蒙系统）的主要特点包括：1. 分布式架构 2. 一次开发，多端部署 3. 超级终端互联 4. 统一开发框架"
            }
        ]
        stream = await service.generate_with_context(messages, knowledge, stream=True)
        try:
            async for chunk in stream:
                assert chunk and isinstance(chunk, str)
                print(chunk, end="", flush=True)
            print()
        finally:
            if hasattr(stream, 'close'):
                await stream.close()
    except Exception as e:
        print(f"\n请求失败：{str(e)}")
        raise

@pytest.mark.asyncio
async def test_prompt_templates():
    """测试提示词模板"""
    messages = [
        {"role": "user", "content": "如何使用ArkTS开发鸿蒙应用？"}
    ]
    knowledge = [
        {
            "content": "ArkTS是鸿蒙系统的开发语言，基于TypeScript扩展，支持声明式UI开发。"
        }
    ]
    prompt = PromptTemplates.create_chat_prompt(messages, knowledge)
    assert prompt and isinstance(prompt, str)
    print(f"\n生成的提示词：\n{prompt}")

if __name__ == "__main__":
    try:
        # asyncio.run(test_simple_generate())
        asyncio.run(test_stream_generate())
        # asyncio.run(test_generate_with_context())
        # asyncio.run(test_prompt_templates())
    except Exception as e:
        print(f"\n测试失败：{str(e)}")
        exit(1) 