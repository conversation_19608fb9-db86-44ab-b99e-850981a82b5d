import unittest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.core.database import Base
from main import app
import json

# 使用SQLite内存数据库进行测试
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

class TestChat(unittest.TestCase):
    def setUp(self):
        # 设置测试数据库
        self.engine = create_engine(SQLALCHEMY_DATABASE_URL)
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        Base.metadata.create_all(bind=self.engine)
        self.db = TestingSessionLocal()
        
        # 设置测试客户端
        self.client = TestClient(app)
    
    def tearDown(self):
        self.db.close()
        Base.metadata.drop_all(bind=self.engine)
    
    def test_send_message(self):
        """测试发送消息接口"""
        response = self.client.post(
            "/api/v1/chat/messages",
            json={
                "message": "测试消息",
                "session_id": None,
                "context_messages": [],
                "stream": False
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["code"], 200)
        self.assertEqual(data["message"], "success")
        self.assertIn("session_id", data["data"])
        self.assertIn("message_id", data["data"])
        self.assertIn("answer", data["data"])
        self.assertIsInstance(data["data"]["references"], list)
    
    def test_get_chat_history(self):
        """测试获取聊天历史接口"""
        # 先发送一条消息
        response = self.client.post(
            "/api/v1/chat/messages",
            json={
                "message": "测试消息",
                "session_id": None,
                "context_messages": [],
                "stream": False
            }
        )
        data = response.json()
        session_id = data["data"]["session_id"]
        
        # 获取历史记录
        response = self.client.get(
            f"/api/v1/chat/messages/history",
            params={
                "session_id": session_id,
                "page_size": 10,
                "page_number": 1
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["code"], 200)
        self.assertEqual(data["message"], "success")
        self.assertGreater(len(data["data"]["messages"]), 0)
        self.assertGreater(data["data"]["total"], 0)
        self.assertEqual(data["data"]["page_size"], 10)
        self.assertEqual(data["data"]["page_number"], 1)

if __name__ == '__main__':
    unittest.main() 