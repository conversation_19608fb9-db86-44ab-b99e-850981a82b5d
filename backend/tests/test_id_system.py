#!/usr/bin/env python3
"""
ID系统测试文件

测试新的统一ID系统的各种功能，确保ID生成、解析、验证等功能正常工作。
"""

import pytest
import time
from pathlib import Path

import sys  

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.core.id_generator import IDGenerator


class TestIDGenerator:
    """ID生成器测试类"""
    
    def test_generate_knowledge_id(self):
        """测试Knowledge ID生成"""
        # 生成多个ID
        ids = [IDGenerator.generate_knowledge_id() for _ in range(10)]
        
        # 验证格式
        for id_str in ids:
            assert id_str.startswith('k_'), f"ID应该以'k_'开头: {id_str}"
            parts = id_str.split('_')
            assert len(parts) == 3, f"ID应该有3个部分: {id_str}"
            assert parts[0] == 'k', f"第一部分应该是'k': {id_str}"
            assert parts[1].isdigit(), f"第二部分应该是时间戳: {id_str}"
            assert len(parts[2]) == 6, f"第三部分应该是6位随机字符: {id_str}"
        
        # 验证唯一性
        assert len(set(ids)) == len(ids), "生成的ID应该都是唯一的"
    
    def test_generate_chunk_id(self):
        """测试Chunk ID生成"""
        knowledge_id = "k_1703123456_a1b2c3"
        
        # 生成不同索引的chunk ID
        chunk_ids = [
            IDGenerator.generate_chunk_id(knowledge_id, i) 
            for i in range(10)
        ]
        
        # 验证格式
        for i, chunk_id in enumerate(chunk_ids):
            assert chunk_id.startswith(knowledge_id), f"Chunk ID应该以knowledge_id开头: {chunk_id}"
            assert f"_c{i:03d}" in chunk_id, f"Chunk ID应该包含正确的索引: {chunk_id}"
            expected = f"{knowledge_id}_c{i:03d}"
            assert chunk_id == expected, f"期望: {expected}, 实际: {chunk_id}"
        
        # 测试边界情况
        chunk_0 = IDGenerator.generate_chunk_id(knowledge_id, 0)
        assert chunk_0 == f"{knowledge_id}_c000"
        
        chunk_999 = IDGenerator.generate_chunk_id(knowledge_id, 999)
        assert chunk_999 == f"{knowledge_id}_c999"
    
    def test_extract_knowledge_id_from_chunk_id(self):
        """测试从Chunk ID提取Knowledge ID"""
        knowledge_id = "k_1703123456_a1b2c3"
        chunk_id = f"{knowledge_id}_c001"
        
        extracted = IDGenerator.extract_knowledge_id_from_chunk_id(chunk_id)
        assert extracted == knowledge_id, f"期望: {knowledge_id}, 实际: {extracted}"
        
        # 测试边界情况
        chunk_0 = f"{knowledge_id}_c000"
        extracted_0 = IDGenerator.extract_knowledge_id_from_chunk_id(chunk_0)
        assert extracted_0 == knowledge_id
        
        chunk_999 = f"{knowledge_id}_c999"
        extracted_999 = IDGenerator.extract_knowledge_id_from_chunk_id(chunk_999)
        assert extracted_999 == knowledge_id
        
        # 测试无效输入
        invalid_chunk_id = "k_1703123456_a1b2c3"  # 没有_c部分
        result = IDGenerator.extract_knowledge_id_from_chunk_id(invalid_chunk_id)
        assert result is None
    
    def test_get_chunk_index_from_chunk_id(self):
        """测试从Chunk ID提取索引"""
        knowledge_id = "k_1703123456_a1b2c3"
        
        # 测试不同索引
        test_cases = [0, 1, 5, 10, 99, 999]
        for index in test_cases:
            chunk_id = IDGenerator.generate_chunk_id(knowledge_id, index)
            extracted_index = IDGenerator.get_chunk_index_from_chunk_id(chunk_id)
            assert extracted_index == index, f"期望索引: {index}, 实际: {extracted_index}"
        
        # 测试无效输入
        invalid_chunk_id = "k_1703123456_a1b2c3"  # 没有_c部分
        result = IDGenerator.get_chunk_index_from_chunk_id(invalid_chunk_id)
        assert result is None
        
        # 测试格式错误的chunk ID
        malformed_chunk_id = "k_1703123456_a1b2c3_cabc"
        result = IDGenerator.get_chunk_index_from_chunk_id(malformed_chunk_id)
        assert result is None
    
    def test_is_knowledge_id(self):
        """测试Knowledge ID识别"""
        # 有效的Knowledge ID
        valid_knowledge_ids = [
            "k_1703123456_a1b2c3",
            "k_1000000000_abc123",
            "k_9999999999_000000"
        ]
        
        for id_str in valid_knowledge_ids:
            assert IDGenerator.is_knowledge_id(id_str), f"应该识别为Knowledge ID: {id_str}"
        
        # 无效的Knowledge ID
        invalid_knowledge_ids = [
            "k_1703123456_a1b2c3_c001",  # 这是chunk ID
            "1703123456_a1b2c3",  # 没有k_前缀
            "k_abc_def",  # 时间戳不是数字
            "knowledge_123",  # 格式不对
            "",
            None
        ]
        
        for id_str in invalid_knowledge_ids:
            assert not IDGenerator.is_knowledge_id(id_str), f"不应该识别为Knowledge ID: {id_str}"
    
    def test_is_chunk_id(self):
        """测试Chunk ID识别"""
        # 有效的Chunk ID
        valid_chunk_ids = [
            "k_1703123456_a1b2c3_c000",
            "k_1703123456_a1b2c3_c001",
            "k_1703123456_a1b2c3_c999",
        ]
        
        for id_str in valid_chunk_ids:
            assert IDGenerator.is_chunk_id(id_str), f"应该识别为Chunk ID: {id_str}"
        
        # 无效的Chunk ID
        invalid_chunk_ids = [
            "k_1703123456_a1b2c3",  # 这是knowledge ID
            "1703123456_a1b2c3_c001",  # 没有k_前缀
            "k_abc_def_c001",  # 时间戳不是数字
            "chunk_123",  # 格式不对
            "",
            None
        ]
        
        for id_str in invalid_chunk_ids:
            assert not IDGenerator.is_chunk_id(id_str), f"不应该识别为Chunk ID: {id_str}"
    
    def test_id_uniqueness_across_time(self):
        """测试跨时间的ID唯一性"""
        # 快速生成多个ID（在同一秒内）
        ids_batch1 = [IDGenerator.generate_knowledge_id() for _ in range(100)]
        
        # 等待一秒后再生成
        time.sleep(1)
        ids_batch2 = [IDGenerator.generate_knowledge_id() for _ in range(100)]
        
        # 合并所有ID
        all_ids = ids_batch1 + ids_batch2
        
        # 验证唯一性
        assert len(set(all_ids)) == len(all_ids), "所有ID应该都是唯一的"
    
    def test_chunk_id_relationship(self):
        """测试Chunk ID与Knowledge ID的关系"""
        knowledge_id = IDGenerator.generate_knowledge_id()
        
        # 生成多个chunk
        chunk_ids = [
            IDGenerator.generate_chunk_id(knowledge_id, i) 
            for i in range(5)
        ]
        
        # 验证每个chunk都能正确提取出父knowledge ID
        for i, chunk_id in enumerate(chunk_ids):
            extracted_knowledge_id = IDGenerator.extract_knowledge_id_from_chunk_id(chunk_id)
            assert extracted_knowledge_id == knowledge_id, \
                f"Chunk {i} 应该能提取出正确的knowledge ID"
            
            extracted_index = IDGenerator.get_chunk_index_from_chunk_id(chunk_id)
            assert extracted_index == i, \
                f"Chunk {i} 应该能提取出正确的索引"
    
    def test_id_format_consistency(self):
        """测试ID格式的一致性"""
        # 生成一批Knowledge ID
        knowledge_ids = [IDGenerator.generate_knowledge_id() for _ in range(10)]
        
        for knowledge_id in knowledge_ids:
            # 验证Knowledge ID格式
            assert IDGenerator.is_knowledge_id(knowledge_id)
            assert not IDGenerator.is_chunk_id(knowledge_id)
            
            # 为每个Knowledge生成chunks
            for i in range(3):
                chunk_id = IDGenerator.generate_chunk_id(knowledge_id, i)
                
                # 验证Chunk ID格式
                assert IDGenerator.is_chunk_id(chunk_id)
                assert not IDGenerator.is_knowledge_id(chunk_id)
                
                # 验证关系
                assert IDGenerator.extract_knowledge_id_from_chunk_id(chunk_id) == knowledge_id
                assert IDGenerator.get_chunk_index_from_chunk_id(chunk_id) == i


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
    print("✅ 所有ID系统测试通过!") 