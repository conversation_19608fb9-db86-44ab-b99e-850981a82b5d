import sqlite3
import os
from pathlib import Path

# 获取数据库文件路径
db_path = Path("../data/harmonyqa.db")
if not db_path.exists():
    print(f"数据库文件不存在: {db_path}")
    exit(1)

# 连接数据库
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 获取所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("数据库中的表:")
for table in tables:
    print(f"- {table[0]}")

# 检查import_tasks表是否存在
if ('import_tasks',) in tables:
    print("\nimport_tasks表存在")
    # 获取表结构
    cursor.execute("PRAGMA table_info(import_tasks);")
    columns = cursor.fetchall()
    print("\nimport_tasks表结构:")
    for col in columns:
        print(f"- {col[1]} ({col[2]})")
    
    # 获取表中的数据
    cursor.execute("SELECT COUNT(*) FROM import_tasks;")
    count = cursor.fetchone()[0]
    print(f"\nimport_tasks表中有 {count} 条记录")
else:
    print("\nimport_tasks表不存在")

# 关闭连接
conn.close() 