import sqlite3
import os
from pathlib import Path
import uuid
from datetime import datetime
import json

# 获取数据库文件路径
db_path = Path("../data/harmonyqa.db")
if not db_path.exists():
    print(f"数据库文件不存在: {db_path}")
    exit(1)

# 连接数据库
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 创建一个测试导入任务
task_id = str(uuid.uuid4())
status = "completed"
total_files = 5
processed_files = 5
failed_files = 0
progress = 100.0
error_message = None
result = json.dumps({
    "success": [
        {"file_path": "test1.txt", "knowledge_id": str(uuid.uuid4())},
        {"file_path": "test2.txt", "knowledge_id": str(uuid.uuid4())},
        {"file_path": "test3.txt", "knowledge_id": str(uuid.uuid4())},
        {"file_path": "test4.txt", "knowledge_id": str(uuid.uuid4())},
        {"file_path": "test5.txt", "knowledge_id": str(uuid.uuid4())}
    ],
    "failed": []
})
created_at = datetime.now().isoformat()
updated_at = datetime.now().isoformat()

# 插入数据
cursor.execute(
    """
    INSERT INTO import_tasks 
    (id, status, total_files, processed_files, failed_files, progress, error_message, result, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """,
    (task_id, status, total_files, processed_files, failed_files, progress, error_message, result, created_at, updated_at)
)

# 提交事务
conn.commit()

print(f"已创建测试导入任务，ID: {task_id}")

# 关闭连接
conn.close() 