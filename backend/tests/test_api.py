import requests
import json

def test_import_tasks_api():
    """测试导入任务列表API"""
    url = "http://localhost:8000/api/v1/knowledge/import-tasks"
    params = {
        "page": 1,
        "page_size": 10
    }
    
    try:
        response = requests.get(url, params=params)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
        # 检查请求路径
        print(f"\n实际请求URL: {response.request.url}")
        
    except Exception as e:
        print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    test_import_tasks_api() 