import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
root_dir = str(Path(__file__).parent.parent)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from src.core.database import get_db, init_db
from src.repositories.knowledge_repository import KnowledgeRepository
from src.repositories.import_task_repository import ImportTaskRepository
from src.services.knowledge_service import KnowledgeService

async def test_list_import_tasks():
    """测试获取导入任务列表"""
    # 首先初始化数据库，确保所有表都已创建
    print("初始化数据库...")
    await init_db()
    print("数据库初始化完成")
    
    # 获取数据库会话
    db_gen = get_db()
    db = await db_gen.__anext__()
    
    try:
        # 创建仓库和服务
        knowledge_repo = KnowledgeRepository(db)
        import_repo = ImportTaskRepository(db)
        service = KnowledgeService(knowledge_repo, import_repo)
        
        # 获取导入任务列表
        tasks, total = await service.list_import_tasks(0, 10)
        
        print(f"总任务数: {total}")
        print(f"任务列表: {tasks}")
        
        # 检查任务列表
        if tasks:
            print("\n第一个任务详情:")
            for key, value in tasks[0].items():
                print(f"- {key}: {value}")
        else:
            print("\n没有找到任何任务")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")
    
    finally:
        # 关闭数据库会话
        try:
            await db_gen.aclose()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_list_import_tasks()) 