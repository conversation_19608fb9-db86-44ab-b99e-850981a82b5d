from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import platform
import sys
import os
from pathlib import Path
from contextlib import asynccontextmanager

# 先加载配置
from src.core.config_loader import load_configurations
load_configurations()

# 导入settings（初始化已在load_configurations中完成）
from src.core.config import settings

# 导入日志配置
from src.core.logging import setup_logging, app_logger as logger, setup_standard_logging_intercept
# 导入中间件
from src.core.middleware import RequestLoggingMiddleware
# 导入错误处理器
from src.core.error_handlers import register_exception_handlers
from src.api.v1.api import api_router

# 引入全局 ElasticSearch 单例
from src.core.elasticsearch import ElasticSearch
# 导入知识库仓库
from src.repositories.knowledge_repository import KnowledgeRepository

# 初始化日志配置
setup_logging(log_level=settings.LOG_LEVEL, log_to_file=True)
# 设置标准库logging拦截
# setup_standard_logging_intercept()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    try:
        if platform.system() == "Windows":
            # Windows 平台使用同步方式
            from src.core.database import init_db_sync
            init_db_sync()
        else:
            # 其他平台使用异步方式
            from src.core.database import init_db
            await init_db()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise e
    
    # 初始化elastic_search
    es_global = ElasticSearch()
    await es_global.ensure_index_exists()


    yield

    # 关闭 ElasticSearch 客户端
    try:
        await es_global.close()
        logger.info("ElasticSearch 连接已关闭")
    except Exception as e:
        logger.warning(f"关闭 ElasticSearch 失败: {str(e)}")

    logger.info("应用关闭，清理资源...")

# 增强API文档配置
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=f"""
    {settings.PROJECT_NAME} 是一个专门的非代码知识检索智能体（NKRA）。NKRA的核心目标是：在理解代码上下文的基础上，为开发者和代码生成工具提供精确、及时、上下文感知的非代码知识支持。

    ## 认证方式
    API使用Bearer Token认证，需要先通过登录接口获取token。
    """,
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    swagger_ui_parameters={"defaultModelsExpandDepth": -1, "persistAuthorization": True},
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加请求日志中间件
app.add_middleware(
    RequestLoggingMiddleware,
    exclude_paths=["/api/v1/health", "/metrics", "/favicon.ico"]
)

# 注册异常处理器
register_exception_handlers(app)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.PROJECT_NAME} Backend"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    # 添加项目根目录到 Python 路径
    root_dir = str(Path(__file__).parent.parent)
    if root_dir not in sys.path:
        sys.path.append(root_dir)

    logger.info(f"Starting {settings.PROJECT_NAME} Backend...")
    logger.info(f"环境: {os.environ.get('NKRA_ENV', '默认')}")
    logger.info(f"日志级别: {settings.LOG_LEVEL}")
    logger.info(f"数据库类型: {settings.DB_TYPE}")

    import uvicorn
    uvicorn.run("backend.main:app", host="0.0.0.0", port=8000, reload=True)