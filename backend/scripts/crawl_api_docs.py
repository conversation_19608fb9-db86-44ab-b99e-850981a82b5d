import asyncio
import json
import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.crawlers.sites.harmony_api_crawler import HarmonyApiCrawler
from src.core.database import init_db,async_session_factory
from src.services.knowledge_service import KnowledgeService
from src.repositories.import_task_repository import ImportTaskRepository
from src.repositories.knowledge_repository import KnowledgeRepository
from src.core.logging import get_cli_logger

# 获取CLI专用日志记录器
logger = get_cli_logger("crawl_api_docs")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='爬取鸿蒙API参考文档并导入数据库')
    parser.add_argument('--max-docs', type=int, default=10, help='最大爬取文档数量(默认:10)')
    parser.add_argument('--output-dir', type=str, default='data/harmony_api_references', help='输出目录路径')
    return parser.parse_args()

def main():
    # 解析命令行参数
    args = parse_args()
    
    try:
        # 初始化爬虫
        crawler = HarmonyApiCrawler()
        # 设置最大爬取文档数
        max_docs = args.max_docs
        
        # 执行爬虫任务
        logger.info(f"Starting to crawl HarmonyOS API references, limit to {max_docs} documents...")
        documents = crawler.crawl(max_docs=max_docs)
        
        # 打印结果摘要
        logger.info(f"Crawling completed, found {len(documents)} documents")
        for i, doc in enumerate(documents):
            logger.info(f"Document {i+1}: {doc['title']} - {doc['url']}")
            
        # 保存到文件
        output_path = crawler.save_results(documents, output_dir=args.output_dir)
        logger.info(f"结果已保存到 {output_path}")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise

if __name__ == "__main__":
    main() 
    # python backend/scripts/crawl_api_docs.py --output-dir data/harmony_api --max-docs 10 