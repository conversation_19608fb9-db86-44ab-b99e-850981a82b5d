#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
知识库domain数据迁移脚本
用于在Alembic迁移添加domain字段后，将指定时间段的知识的domain设置为指定的值
此脚本使用原生sqlite3连接，不依赖SQLAlchemy

使用方法：
python apply_domain_data_migration.py --start-time "2024-01-01" --end-time "2024-03-31" --domain api_documentation

参数说明：
--start-time: 开始时间（YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS）
--end-time: 结束时间（YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS）
--domain: 要设置的domain值，可选值包括default、api_documentation、best_practices、tool_team、game_team、2012_harmony
--dry-run: 仅打印要执行的操作，不实际更新数据库
--db-file: 数据库文件路径，默认使用项目配置中的路径
"""

import os
import sys
import sqlite3
import argparse
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-8s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("apply_domain_data_migration")

# 知识领域枚举值 参考constants.py
KNOWLEDGE_DOMAINS = [
    "default",           # 默认分类
    "api_documentation", # API文档
    "best_practices",    # 最佳实践
    "harmony_exp_2012",      # 2012网站鸿蒙突击队
    "harmony_exp_HDN",         # 鸿蒙突击队经验-黄大年
    "game_team_wiki"          # 游戏分队经验-wiki
]

# 默认数据库文件路径
DEFAULT_DB_PATH = str(Path(__file__).parent.parent / "data" / "harmonyqa.db")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="更新知识库domain字段数据")
    parser.add_argument("--start-time", help="开始时间 (YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD)", required=True)
    parser.add_argument("--end-time", help="结束时间 (YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD)", required=True)
    parser.add_argument("--domain", help="要设置的domain值", 
                       choices=KNOWLEDGE_DOMAINS, 
                       default="best_practices")
    parser.add_argument("--dry-run", help="仅打印要执行的操作，不实际更新数据库", 
                       action="store_true", default=False)
    parser.add_argument("--db-file", help="SQLite数据库文件路径", default=DEFAULT_DB_PATH)
    
    return parser.parse_args()

def parse_time(time_str):
    """解析时间字符串，支持两种格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS"""
    try:
        # 尝试解析完整时间格式
        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # 尝试解析日期格式，如果只有日期则使用当天的00:00:00或23:59:59
            return datetime.strptime(time_str, '%Y-%m-%d')
        except ValueError:
            return None

def update_knowledge_domain(conn, start_time, end_time, domain, dry_run=False):
    """更新指定时间段的知识条目的domain值"""
    cursor = conn.cursor()
    
    # 查询要更新的记录数量
    count_query = """
    SELECT COUNT(*) FROM knowledge 
    WHERE created_at BETWEEN ? AND ?
    """
    cursor.execute(count_query, (start_time, end_time))
    count = cursor.fetchone()[0]
    
    logger.info(f"将要更新 {count} 条知识记录的domain为 '{domain}'")
    logger.info(f"时间范围: {start_time} 至 {end_time}")
    
    if dry_run:
        logger.info("dry-run模式，不执行实际更新操作")
        return count
    
    # 执行更新
    update_query = """
    UPDATE knowledge 
    SET domain = ? 
    WHERE created_at BETWEEN ? AND ?
    """
    cursor.execute(update_query, (domain, start_time, end_time))
    conn.commit()
    
    logger.info(f"成功更新 {cursor.rowcount} 条知识记录的domain值")
    return cursor.rowcount

def main():
    """主函数"""
    args = parse_args()
    
    start_time_str = args.start_time
    end_time_str = args.end_time
    domain = args.domain
    dry_run = args.dry_run
    db_file = args.db_file
    
    # 验证并解析时间格式
    start_time = parse_time(start_time_str)
    if not start_time:
        logger.error(f"开始时间格式错误: {start_time_str}，应为YYYY-MM-DD或YYYY-MM-DD HH:MM:SS")
        sys.exit(1)
        
    end_time = parse_time(end_time_str)
    if not end_time:
        logger.error(f"结束时间格式错误: {end_time_str}，应为YYYY-MM-DD或YYYY-MM-DD HH:MM:SS")
        sys.exit(1)
    
    # 如果只提供了日期，则为开始时间设置为当天00:00:00，结束时间设置为当天23:59:59
    if start_time.hour == 0 and start_time.minute == 0 and start_time.second == 0 and len(start_time_str) <= 10:
        start_time = datetime.strptime(f"{start_time.strftime('%Y-%m-%d')} 00:00:00", '%Y-%m-%d %H:%M:%S')
        
    if end_time.hour == 0 and end_time.minute == 0 and end_time.second == 0 and len(end_time_str) <= 10:
        end_time = datetime.strptime(f"{end_time.strftime('%Y-%m-%d')} 23:59:59", '%Y-%m-%d %H:%M:%S')
    
    # 转换为字符串格式
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_file):
        logger.error(f"数据库文件不存在: {db_file}")
        sys.exit(1)
    
    try:
        # 直接连接SQLite数据库
        logger.info(f"连接数据库: {db_file}")
        conn = sqlite3.connect(db_file)
        
        # 更新知识条目的domain值
        update_knowledge_domain(conn, start_time_str, end_time_str, domain, dry_run)
        
    except Exception as e:
        logger.error(f"更新失败: {e}")
        sys.exit(1)
    finally:
        if 'conn' in locals():
            conn.close()
    
    logger.info("更新完成")

if __name__ == "__main__":
    main() 