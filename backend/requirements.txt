# 数据库
sqlalchemy>=2.0.40
alembic>=1.14.1
psycopg2-binary>=2.9.9
asyncpg>=0.29.0
aiosqlite>=0.21.0

# HTTP客户端
requests>=2.32.3
aiohttp>=3.9.3

# Git操作
gitpython>=3.1.43

# 工具库
python-dotenv>=1.0.1
pydantic>=2.10.6
pydantic-settings>=2.8.1
fastapi>=0.115.8
uvicorn>=0.34.0

# 日志
loguru>=0.7.2

# 测试
pytest>=8.0.0
pytest-asyncio>=0.23.5
httpx>=0.27.0

# 向量数据库和文本处理
chromadb>=0.4.22
sentence-transformers>=3.4.1
beautifulsoup4>=4.12.3
nltk>=3.8.1
markdownify>=0.11.6
pypandoc>=1.15
mammoth>=1.9.0

# Elasticsearch
elasticsearch[async]>=8.17.1

# 异步文件操作
aiofiles>=23.2.1

# 其他工具
python-multipart>=0.0.9
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
tenacity>=8.2.3
email-validator>=2.1.0.post1
faiss-cpu>=1.10.0
numpy>=2.2.2
scikit-learn>=1.6.1
click>=8.1.0 